package constants

// Environment type represents various kind of environments an application can run in.
type Environment string

const (
	Production  = Environment("prod")
	Development = Environment("dev")
	QA          = Environment("qa")
	Staging     = Environment("staging")
)

const (
	LogFilePath = "/opt/logs/hotels-scion/"
)

const (
	PmsFetchUrl = "http://fpm.mmt.mmt:8080/pms/client/getPropertyFile?id=bloomConfig"
)

const (
	MMT = "MMT"
)

const (
	CBSearchHotelsAPI             = "CB_SEARCH_HOTELS"
	CBXsellStaticHotelsAPI        = "CB_XSELL_STATIC_HOTELS"
	CBComparatorAPI               = "CB_COMPARATOR"
	SingularityAPI                = "SINGULARITY_API"
	CBFetchCollectionsAPI         = "CB_FETCH_COLLECTIONS"
	AltAccoPropertyAPI            = "ALTACCO_PROPERTY_TYPE"
	CGXsellSearchContextHotelsAPI = "CG_XSELL_SEARCH_CONTEXT_HOTELS"
	CGSearchHotelsAPI             = "CG_SEARCH_HOTELS"
	HesCardsData                  = "HES_CARDS_DATA"
	CityDetailsApi                = "WEBAPI_CITYDETAILS"
	CBSearchPersonalizedHotels    = "CB_SEARCH_PERSONALIZED_HOTELS"
	CGLandingCardHotels           = "CG_LANDING_CARD_HOTELS"
	MobLanding                    = "MOB_LANDING"
)

const (
	RSQ_SPLITTER                   = "e"
	AND_SEPARATOR                  = "&"
	DESKTOP_PROPERTYTYPE_URL_PARAM = "propType"
	PR_SEPARATOR                   = "="
	LOCUS_LOCATION_ID              = "locusId"
	LOCUS_TYPE                     = "locusType"
	DT_FRMT_SEARCH_CNTXT           = "2006-01-02"
	DT_FRMT_DEEPLINK_URL           = "01022006"
	DT_FRMT_SEARCH_STR             = "01-02-2006 00:00:00"
	ALT_ACCO_PROPERTY              = "ALTH_Property"
	DT_FRMT_DEEPLINK_URL_V2        = "20060102"
)

const (
	OFFER_PERSUASIONS    = "offerPersuasions"
	COLLECTION           = "COLLECTION"
	BEST_DEALS           = "BEST_DEALS"
	REQUEST_SOURCE_SCION = "SCION"
	VALUESTAYS           = "VALUESTAYS"
)

const (
	HOMESTAY_FUNNEL       = "homestays"
	HOTEL_FUNNEL          = "hotels"
	DATE_FILTER_TEXT      = "DATE_FILTER"
	DRIVING_DISTANCE_TEXT = "DRIVING_DISTANCE_KM"
	HOTElS_TEXT           = "HOTELS"
	HOMESTAY_TEXT         = "HOMESTAY"
	BUSINESS_TEXT         = "BUSINESS"
	HOMESTAYS_TITLE       = "Homestays"
	HOTELS_TITLE          = "Hotels"
)

const (
	TRANSFER_ENCODING_KEY           = "Transfer-Encoding"
	TRANSFER_ENCODING_VALUE_CHUNKED = "chunked"
	CONTENT_TYPE_KEY                = "Content-Type"
	CONTENT_TYPE_VALUE              = "application/json"
	PROFILE_TYPE_KEY                = "profileType"
)

const (
	MINIMUM_INTENT_FOR_ALT_ACCO_COUNT     = 2
	HEADER_HOMESTAY_HEADING               = "HOMESTAYS"
	HEADER_HOMESTAY_SUBHEADING            = "Based on your search for homestays in <NOP>"
	HEADER_HOTELS_HEADING                 = "HOTELS & HOMESTAYS"
	HEADER_HOTELS_GCC_HEADING             = "HOTELS"
	HEADER_HOTELS_SUBHEADING              = "Based on your search for properties in <NOP>"
	CROSS_SELL_CONDITION                  = "user_searches"
	HEADER_MMTEXCLUSIVE_HOTELS_SUBHEADING = "MMT Exclusive Hotels"
)

const (
	LUXE_CATEGORY_VALUE              = "LUXE"
	LUXE_CATEGORY_HOTEL_URL          = "https://promos.makemytrip.com/Hotels_product/Luxe/App_listing_luxe.png"
	FUNNEL_SOURCE_SHORTSTAYS         = "SHORTSTAYS"
	SHORTSTAYS_CARD_HEADING          = "Staycations"
	SHORTSTAYS_CARD_HEADING_LUXE     = "Refreshing Staycations"
	SHORTSTAYS_CARD_SUB_HEADING      = "Drivable Luxury Stays around <LocusName>"
	SHORTSTAYS_CARD_SUB_HEADING_LUXE = "Around <LocusName>"
	LUXURY_FILTERS_VALUE             = "luxury_hotels"
)

const (
	HOTELLANDING                  = "HOTELLANDING"
	RECENTLY_VIEWED_HOTELS        = "RECENTLY_VIEWED_HOTELS"
	HANDPICKED_PROPERITES         = "HANDPICKED_PROPERITES"
	GREAT_VALUE_PACKAGES          = "GREAT_VALUE_PACKAGES"
	GREAT_VALUE_PACKAGES_V2       = "GREAT_VALUE_PACKAGES_V2"
	MMT_EXCLUSIVE_OFFERS          = "MMT_EXCLUSIVE_OFFERS"
	MMT_IMMERESIVE_THEMES_CARD    = "HTL_IMMERSIVE_THEME"
	MMT_IMMERESIVE_THEMES_CARD_V2 = "HTL_IMMERSIVE_THEME_V2"
	MMT_PACKAGE_RATE_FILTER       = "PACKAGE_RATE"
	HOTEL_LANDING                 = "HOTEL_LANDING"
)

const (
	HEADER_GREAT_VALUE_HEADING              = "MMT Luxe - Super Packages"
	HEADER_GREAT_VALUE_SUBHEADING           = "Lavish stays with world-class amenities & experiences"
	HEADER_GREAT_VALUE_HEADING_V2           = "Stay Super Packages"
	HEADER_GREAT_VALUE_HEADING_V2_INTENT    = "Stays in <NOP>"
	HEADER_GREAT_VALUE_SUBHEADING_V2        = "With Benefits"
	HEADER_GREAT_VALUE_SUBHEADING_V2_INTENT = "Super Packages"
	HEADER_HANDPICKED_HEADING               = "Rare Finds"
	HEADER_HANDPICKED_SUBHEADING            = "Handpicked stays near you"
	HEADER_RECENTLY_VIEWED_HEADING          = "Recently Viewed"
)

const (
	IMMERSIVE_THEMES_CARD_HEADING_INTENT    = "Stays in <NOP>"
	IMMERSIVE_THEMES_CARD_SUBHEADING_INTENT = "Handpicked For You"
	IMMERSIVE_THEMES_CARD_HEADING           = "Handpicked Hotels"
	IMMERSIVE_THEMES_CARD_SUBHEADING        = "For Memorable Stays"
)

const (
	MMT_CROSS_SELL_CARD               = "HOTEL_CROSS_SELL"
	HEADER_SUBHEADING                 = "For your stay in <NOP>"
	MMT_CROSS_SELL_DROPOFF_SUBHEADING = "For your stay in <NOP>"
)

const (
	ANDROID_CLIENT = "ANDROID"
	IOS_CLIENT     = "IOS"
	PWA_CLIENT     = "PWA"
	DESKTOP_CLIENT = "DESKTOP"
)

const (
	HOTEL_XSELL_BENEFITS            = "HOTEL_XSELL_BENEFITS"
	HOTEL_EXTEND_YOUR_TRIP          = "HOTEL_EXTEND_YOUR_TRIP"
	HOTEL_EXTEND_YOUR_TRIP_CTA_TEXT = "Show all Stays options"
	CTA_TEXT                        = "BOOK A HOTEL"
	CTA_POSITION                    = "right"
	INTL_CASHBACK_CARD              = "INTL_CASHBACK_CARD"
)

const (
	HOTEL_XSELL_PRICE_TYPE = "onwards"
	SHORTSTAYS_PRICE_TYPE  = "per night"
)

const (
	HOTEL_LISTING_DT_DEEPLINK_GI = "https://www.goibibo.com/hotels/hotel-listing/?checkin=%s&checkout=%s&roomString=%s&locusId=%s&locusType=%s&cityCode=%s&cc=%s&searchText=%s&mmtId=%s&giHotelId=%s&_uCurrency=%s"
	ROOM_STRING                  = "%d-%d-%d"
)

const (
	CITY_LISTING_DT_DEEPLINK_GI = "https://www.goibibo.com/hotels/hotel-listing/?checkin=%s&checkout=%s&roomString=%s&locusId=%s&locusType=%s&cityCode=%s&cc=%s&searchText=%s"
)

const (
	EXCLUSIVE_PERSUASION_TEXT      = "<p><b>Exclusive Rates for you! Use coupon BOOKSTAYS to get upto additional 15% OFF</b></p>"
	EXCLUSIVE_PERSUASION_ICON      = "https://go-assets.ibcdn.com/u/MMT/images/1732521335403-discountLogo.png"
	EXCLUSIVE_PERSUASION_BGCOLOR   = "#E6FFF9"
	EXCLUSIVE_PERSUASION_TEXTCOLOR = "#007e7d"
)

const (
	VID = "vid"
)
