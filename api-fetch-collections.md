# Fetch Collections API

## Overview
The Fetch Collections API provides hotel collections and deals data. It's a synchronous REST API that returns structured collection information including hotel lists, offers, and promotional content.

**Endpoint:** `POST /hotels-scion/api/fetchCollections`

## Architecture

### Type
- **Synchronous REST API** with request-response pattern
- **Pattern:** Traditional REST with comprehensive error handling
- **Error Handling:** Panic recovery with graceful error responses
- **Response:** Structured collection data with hotel information

### Key Features
- Comprehensive request validation
- Panic recovery mechanism
- Correlation key tracking
- Image details processing
- Feature flag support

## Downstream Services

| Service | URL | Purpose |
|---------|-----|---------|
| **ClientGateway Fetch Collections** | `http://hotels-clientgateway-oth.ecs.mmt/clientbackend/cg/fetchCollections/%s/2` | Collections data via gateway |
| **HES Cards Data** | `http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/getCard` | Hotel card information |
| **Web API City Details** | `http://htlwebapi.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getcitydetails` | City information |

## Request Format

### Headers
```
Content-Type: application/json
uuid: string (optional)
```

### Request Body
```json
{
  "correlationKey": "uuid-string",
  "context": {
    "contextId": "string",
    "experimentData": "string",
    "pageContext": "string",
    "scope": "string",
    "funnelSource": "string"
  },
  "cards": [
    {
      "cardId": "VALUESTAYS",
      "cardVariantId": "string",
      "templateId": "string",
      "componentId": "string",
      "searchEvent": {
        "sc": {
          "lob": "string",
          "lobCategory": "string",
          "fromDateTime": {
            "str": "2024-01-15",
            "ts": 1705276800,
            "zone": "Asia/Kolkata"
          },
          "toDateTime": {
            "str": "2024-01-17",
            "ts": 1705449600,
            "zone": "Asia/Kolkata"
          },
          "pax": [
            {
              "count": 2,
              "details": {
                "adult": {
                  "ages": [25, 30],
                  "count": 2
                },
                "child": {
                  "ages": [],
                  "count": 0
                },
                "infant": {
                  "ages": [],
                  "count": 0
                }
              }
            }
          ],
          "from": {
            "lobCity": "string",
            "locus": {
              "city": "string",
              "areaId": "string",
              "areaName": "string",
              "poiId": "string",
              "poiName": "string",
              "type": "city",
              "id": "CTBOM",
              "cityName": "Mumbai"
            },
            "cityName": "Mumbai",
            "countryName": "India",
            "countryCode": "IN",
            "longitude": 72.8777,
            "latitude": 19.076
          },
          "to": {
            "lobCity": "string",
            "locus": {
              "city": "string",
              "areaId": "string",
              "areaName": "string",
              "poiId": "string",
              "poiName": "string",
              "type": "city",
              "id": "CTDEL",
              "cityName": "Delhi"
            },
            "cityName": "Delhi",
            "countryName": "India",
            "countryCode": "IN",
            "longitude": 77.209,
            "latitude": 28.6139
          },
          "timestamp": 1705276800,
          "rooms": 1,
          "funnelSource": "string",
          "personalizedSearch": false,
          "product": {
            "id": "string",
            "name": "string"
          }
        }
      },
      "data": {
        "cardList": [
          {
            "description": "string",
            "subDescription": "string",
            "imageUrl": "string",
            "deepLink": "string",
            "actionText": "string"
          }
        ],
        "cardInfo": {
          "index": 0,
          "starText": "string",
          "cardId": "string",
          "titleText": "string",
          "subText": "string",
          "cardAction": [
            {
              "webViewUrl": "string",
              "title": "string"
            }
          ],
          "cardPayload": {
            "genericCardData": [
              {
                "titleText": "string",
                "iconUrl": "string"
              }
            ]
          },
          "iconUrl": "string",
          "bgImageUrl": "string",
          "templateId": "string"
        },
        "hotelList": [
          {
            "id": "hotel-123",
            "name": "Taj Mahal Palace",
            "propertyType": "HOTEL",
            "mainImages": [
              "https://imgak.mmtcdn.com/hotels/123/image1.jpg"
            ],
            "starRating": 5,
            "currencyCode": "INR",
            "displayFare": 15000.0,
            "cityName": "Mumbai",
            "desktopDeeplink": "https://www.makemytrip.com/hotels/hotel-details?hotelId=hotel-123",
            "appDeepLink": "mmyt://htl/listing/?hotelId=hotel-123",
            "locationPersuasion": ["Near Airport"],
            "freeCancellationText": "Free cancellation till 24 hours before check-in",
            "inclusions": ["Breakfast", "WiFi"],
            "cancellationTimeline": "24 hours",
            "flyfishReviewSummary": {
              "rating": 4.5,
              "reviewCount": 1250
            }
          }
        ],
        "appliedFilterMap": {
          "PRICE_RANGE": [
            {
              "filterGroup": "PRICE_RANGE",
              "filterValue": "1000-5000",
              "title": "₹1000 - ₹5000",
              "rangeFilter": true
            }
          ],
          "STAR_RATING": [
            {
              "filterGroup": "STAR_RATING",
              "filterValue": "4",
              "title": "4 Star",
              "rangeFilter": false
            }
          ]
        },
        "bgImageUrl": "https://imgak.mmtcdn.com/collections/bg.jpg",
        "offerPersuasions": [
          {
            "text": "Get 15% off on your first booking",
            "imageUrl": "https://imgak.mmtcdn.com/offers/15-off.png",
            "expiryTimestamp": 1705276800
          }
        ]
      },
      "headerData": {
        "header": "Value Stays",
        "subheader": "Best deals on budget hotels",
        "lobSubheader": "Hotels under ₹5000",
        "cta": {
          "title": "View All",
          "deeplink": "https://www.makemytrip.com/hotels/collections/value-stays"
        }
      }
    }
  ],
  "user": {
    "mmtAuth": "auth-token",
    "uuid": "user-uuid",
    "profileType": "PERSONAL",
    "visitorId": "visitor-123",
    "mcid": "mcid-123",
    "state": "Maharashtra",
    "location": {
      "locationData": {
        "currentLocation": {
          "latitude": 19.076,
          "longitude": 72.8777
        }
      }
    },
    "deviceInfo": {
      "id": "device-123",
      "platform": "ANDROID",
      "model": "Samsung Galaxy S21",
      "appVer": "8.5.1"
    }
  },
  "imageDetails": {
    "categories": [
      {
        "type": "H",
        "count": 2,
        "height": 388.8,
        "width": 583.2,
        "imageFormat": "webp"
      }
    ],
    "types": ["professional"]
  }
}
```

## Response Format

### Headers
```
Content-Type: application/json
```

### Response Body
```json
{
  "cardData": {
    "VALUESTAYS": {
      "cardId": "VALUESTAYS",
      "cardVariantId": "string",
      "templateId": "string",
      "componentId": "string",
      "data": {
        "cardList": [
          {
            "description": "Budget-friendly hotels with great amenities",
            "subDescription": "Starting from ₹999",
            "imageUrl": "https://imgak.mmtcdn.com/collections/value-stays.jpg",
            "deepLink": "https://www.makemytrip.com/hotels/collections/value-stays",
            "actionText": "Explore Now"
          }
        ],
        "cardInfo": {
          "index": 1,
          "starText": "4+ Star Hotels",
          "cardId": "VALUESTAYS",
          "titleText": "Value Stays",
          "subText": "Best deals on budget hotels",
          "cardAction": [
            {
              "webViewUrl": "https://www.makemytrip.com/hotels/collections/value-stays",
              "title": "View Collection"
            }
          ],
          "cardPayload": {
            "genericCardData": [
              {
                "titleText": "Free Breakfast",
                "iconUrl": "https://imgak.mmtcdn.com/icons/breakfast.png"
              },
              {
                "titleText": "Free WiFi",
                "iconUrl": "https://imgak.mmtcdn.com/icons/wifi.png"
              }
            ]
          },
          "iconUrl": "https://imgak.mmtcdn.com/icons/value-stays.png",
          "bgImageUrl": "https://imgak.mmtcdn.com/collections/value-stays-bg.jpg",
          "templateId": "template-123"
        },
        "hotelList": [
          {
            "id": "hotel-123",
            "name": "Hotel Comfort Inn",
            "propertyType": "HOTEL",
            "mainImages": [
              "https://imgak.mmtcdn.com/hotels/123/image1.jpg",
              "https://imgak.mmtcdn.com/hotels/123/image2.jpg"
            ],
            "starRating": 4,
            "currencyCode": "INR",
            "displayFare": 2500.0,
            "cityName": "Mumbai",
            "desktopDeeplink": "https://www.makemytrip.com/hotels/hotel-details?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17",
            "appDeepLink": "mmyt://htl/listing/?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17",
            "locationPersuasion": ["Near Airport", "Free Shuttle"],
            "freeCancellationText": "Free cancellation till 24 hours before check-in",
            "inclusions": ["Breakfast", "WiFi", "Parking"],
            "cancellationTimeline": "24 hours",
            "flyfishReviewSummary": {
              "rating": 4.2,
              "reviewCount": 856
            }
          }
        ],
        "appliedFilterMap": {
          "PRICE_RANGE": [
            {
              "filterGroup": "PRICE_RANGE",
              "filterValue": "1000-5000",
              "title": "₹1000 - ₹5000",
              "rangeFilter": true
            }
          ]
        },
        "bgImageUrl": "https://imgak.mmtcdn.com/collections/value-stays-bg.jpg",
        "offerPersuasions": [
          {
            "text": "Get 15% off on your first booking",
            "imageUrl": "https://imgak.mmtcdn.com/offers/15-off.png",
            "expiryTimestamp": 1705276800
          }
        ]
      },
      "headerData": {
        "header": "Value Stays",
        "subheader": "Best deals on budget hotels",
        "lobSubheader": "Hotels under ₹5000",
        "cta": {
          "title": "View All",
          "deeplink": "https://www.makemytrip.com/hotels/collections/value-stays"
        }
      }
    }
  },
  "status": "OK",
  "statusCode": 200,
  "message": "Success",
  "correlationKey": "uuid-string"
}
```

## Data Flow

```
1. Client Request
   ↓
2. Request Validation
   ├── Validate Scion Fetch Collection Request
   └── Check Required Fields
   ↓
3. Request Transformation
   ├── Set Request Details
   ├── Set Image Details
   ├── Set Feature Flags
   ├── Set Device Details
   └── Set Search Criteria
   ↓
4. Downstream API Call
   ├── ClientGateway Fetch Collections
   ├── HES Cards Data (if required)
   └── Web API City Details (if required)
   ↓
5. Response Processing
   ├── Transform Response
   ├── Process Value Stays Card
   └── Build Card Data Map
   ↓
6. Return Response
```

## Error Handling

### Common Error Scenarios
- **Validation Error:** Returns 400 with validation message
- **Downstream Service Failure:** Returns 500 with error details
- **Panic Recovery:** Catches panics and returns 500 error
- **No Data Found:** Returns error status with appropriate message

### Error Response Format
```json
{
  "cardData": {},
  "status": "ERROR",
  "statusCode": 500,
  "message": "Error description",
  "correlationKey": "uuid-string"
}
```

### Validation Errors
```json
{
  "cardData": {},
  "status": "Failure",
  "statusCode": 400,
  "message": "Validation error: correlationKey is required",
  "correlationKey": ""
}
```

## Performance Considerations

### Optimization Features
- **Request Validation:** Early validation to fail fast
- **Panic Recovery:** Graceful handling of unexpected errors
- **Correlation Tracking:** Request tracing for debugging
- **Image Processing:** Optimized image details handling

### Timeouts
- **Downstream Service Timeout:** 30 seconds (configurable)
- **Request Processing Timeout:** 60 seconds
- **Validation Timeout:** 5 seconds

## Usage Examples

### cURL Example
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/fetchCollections \
  -H 'Content-Type: application/json' \
  -H 'uuid: user-123' \
  -d '{
    "correlationKey": "req-123",
    "context": {
      "pageContext": "HOTEL_LANDING",
      "experimentData": "exp_123"
    },
    "cards": [
      {
        "cardId": "VALUESTAYS",
        "searchEvent": {
          "sc": {
            "from": {"locus": {"id": "CTBOM"}},
            "to": {"locus": {"id": "CTDEL"}},
            "fromDateTime": {"str": "2024-01-15"},
            "toDateTime": {"str": "2024-01-17"},
            "pax": [{"count": 2, "details": {"adult": {"count": 2, "ages": [25, 30]}}}]
          }
        }
      }
    ],
    "user": {
      "uuid": "user-123",
      "profileType": "PERSONAL"
    }
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/hotels-scion/api/fetchCollections', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'uuid': 'user-123'
  },
  body: JSON.stringify({
    correlationKey: 'req-123',
    context: {
      pageContext: 'HOTEL_LANDING'
    },
    cards: [{
      cardId: 'VALUESTAYS',
      searchEvent: {
        sc: {
          from: { locus: { id: 'CTBOM' } },
          to: { locus: { id: 'CTDEL' } },
          fromDateTime: { str: '2024-01-15' },
          toDateTime: { str: '2024-01-17' },
          pax: [{ count: 2, details: { adult: { count: 2, ages: [25, 30] } } }]
        }
      }
    }],
    user: {
      uuid: 'user-123',
      profileType: 'PERSONAL'
    }
  })
});

const data = await response.json();
console.log('Collections:', data);
```

## Monitoring and Logging

### Key Metrics
- **Response Time:** API processing time
- **Success Rate:** Percentage of successful requests
- **Error Rate:** Percentage of failed requests
- **Downstream Service Performance:** Individual service response times

### Logging
- **Request Logging:** Full request payload with correlation key
- **Response Logging:** Response status and timing
- **Error Logging:** Detailed error information with stack traces
- **Validation Logging:** Validation failures and reasons

## Rate Limiting

### Limits
- **Requests per minute:** 500 (configurable)
- **Concurrent requests:** 50 (configurable)
- **Downstream service calls:** Based on service-specific limits

### Throttling
- **429 Too Many Requests:** When rate limit exceeded
- **Retry-After header:** Indicates when to retry
- **Graceful degradation:** Returns cached data when possible

## Security

### Authentication
- **UUID Header:** Optional user identification
- **Correlation tracking:** UUID-based request correlation
- **Request validation:** Comprehensive input validation

### Data Protection
- **HTTPS only:** All communications encrypted
- **PII handling:** Sensitive data masked in logs
- **Input sanitization:** All inputs validated and sanitized

## Supported Card Types

### Available Collections
- **VALUESTAYS:** Budget-friendly hotels
- **LUXE:** Luxury hotels and resorts
- **BEST_DEALS:** Special offers and deals
- **COLLECTION:** Curated hotel collections
- **OFFER_PERSUASIONS:** Promotional offers

### Card Features
- **Hotel Lists:** Curated hotel recommendations
- **Offer Persuasions:** Promotional content
- **Image Details:** Optimized image specifications
- **Filter Support:** Applied filter information
- **Deeplinks:** Direct navigation to hotel details 