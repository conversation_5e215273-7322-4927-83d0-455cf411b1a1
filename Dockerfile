FROM golang:1.13 as build-env


WORKDIR /opt
RUN git clone https://github.com/edenhill/librdkafka.git

WORKDIR /opt/librdkafka
RUN git checkout v1.8.2
RUN ./configure --prefix /usr/local && make clean && make && make install
RUN ldconfig



ADD . /tmp/app/Hotels-Scion
WORKDIR /tmp/app/Hotels-Scion

RUN make onlybuild

FROM ubuntu:20.04


ENV DEBIAN_FRONTEND=noninteractive 

RUN apt-get update && apt-get install -y --no-install-recommends tzdata \
    && rm -rf /var/lib/apt/lists/*
ENV TZ=Asia/Calcutta
ENV GODEBUG="madvdontneed=1"
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime \
    && dpkg-reconfigure --frontend noninteractive tzdata
RUN apt-get -qq update && apt-get -qq install curl git gcc g++ build-essential pkg-config libssl-dev libsasl2-dev krb5-user libsasl2-modules-gssapi-mit moreutils && apt-get clean

WORKDIR /
RUN git clone https://github.com/edenhill/librdkafka.git

WORKDIR /librdkafka
RUN ./configure && make && make install && ls /usr/local/lib/librd*

RUN ldconfig

RUN mkdir -p /opt/logs/hotels-scion/
RUN mkdir -p /etc/kerberos/

COPY --from=build-env /tmp/app/Hotels-Scion/hotels-scion /home/<USER>/
COPY --from=build-env /tmp/app/Hotels-Scion/resources /home/<USER>/resources/
COPY docker/krb5.conf /etc/
COPY docker/hotels_scion.keytab /etc/kerberos/

EXPOSE 80 8080

WORKDIR /home/<USER>

CMD ["./hotels-scion" ,"--environment=prod", "--configPath=resources"]

