package pdt

import (
	"Hotels-Scion/models/pdt"
	"Hotels-Scion/utils/avro"
	"Hotels-Scion/utils/kafka"
	"Hotels-Scion/utils/logging"
	"encoding/binary"
	"encoding/json"
	"github.com/linkedin/goavro/v2"

	"strconv"
)

var (
	log = logging.GetLogger()
)

func Log(pdtModel pdt.PdtModelInterface, topic, template string) {

	codec, err := goavro.NewCodec(avro.AvroSchemaMap[template])
	if err != nil {
		log.Error("Error occurred while setting avro schema : " + err.Error())
		return
	}
	bs := make([]byte, 4)
	i, err := strconv.Atoi(template)
	if err != nil {
		log.Error("Atoi of template id failed and hence returning")
		return
	}
	binary.BigEndian.PutUint32(bs, uint32(i))
	hdr := append([]byte{0}, bs...) //magic byte

	mappedData := pdtModel.ToStringMap()
	//mappedData := structs.Map(pdtModel)
	b, _ := json.Marshal(mappedData) //TODO remove after QA
	log.Debugf("Logging PDT data : %v", string(b))
	binary, err := codec.BinaryFromNative(nil, mappedData)
	request := append(hdr, binary...)

	//e.Info("request", request)
	if err != nil {
		log.Error("Issue occurred while converting from native go to binary using avro schema " + err.Error())
		return
	}
	err = kafka.KafkaProducer.SendMessage(topic, "", request)
	if err != nil {
		log.Error("Error occurred while pushing PDT data : " + err.Error())
		return
	}
}
