package confluent

import (
	"Hotels-Scion/utils/kafka/kconfig"
	"Hotels-Scion/utils/logging"
	confluent "github.com/confluentinc/confluent-kafka-go/kafka"
)

var (
	log = logging.GetLogger()
)

//Producer .
type Producer struct {
	Producer *confluent.Producer
	Config   kconfig.Config
}

//NewProducer .
func NewProducer(c kconfig.Config) *Producer {
	return &Producer{Config: c}
}

//Connect .
func (p *Producer) Connect() error {
	config := &confluent.ConfigMap{
		"metadata.broker.list":       p.Config.Brokers,
		"sasl.kerberos.service.name": p.Config.ServiceName,
		"sasl.kerberos.keytab":       p.Config.KeytabPath,
		"sasl.mechanisms":            "GSSAPI",
		"security.protocol":          "SASL_PLAINTEXT",
		"sasl.kerberos.principal":    p.Config.Principal,
		"session.timeout.ms":         p.Config.SessionTimeout,
		//"group.id":                   p.Config.GroupID,
		"default.topic.config":    confluent.ConfigMap{"auto.offset.reset": "earliest"},
		"go.delivery.reports":     p.Config.IsDeliveryReportsReq,
		"sasl.kerberos.kinit.cmd": `kinit %{sasl.kerberos.principal} -k -t %{sasl.kerberos.keytab}`,
		"debug":                   "topic,generic,broker,metadata,security,msg",
	}

	p2, err := confluent.NewProducer(config)
	if err != nil {
		log.Error("Failed to create Producer: ", err)
		return err
	}

	log.Info("Created Producer: ", p2)

	p.Producer = p2

	return nil
}

//SendMessage .
func (p *Producer) SendMessage(topic string, key string, value interface{}) error {
	message := &confluent.Message{
		TopicPartition: confluent.TopicPartition{
			Topic:     &topic,
			Partition: confluent.PartitionAny,
		},
	}

	switch v := value.(type) {
	case []byte:
		message.Value = v
	case string:
		message.Value = []byte(v)
	}

	//log.Warn("Pushing data: ", message.Value, " into kafka topic: ", topic)

	deliveryChan := make(chan confluent.Event)
	//p.Producer.ProduceChannel() <- message

	p.Producer.Produce(message, deliveryChan)

	e := <-deliveryChan
	m := e.(*confluent.Message)

	if m.TopicPartition.Error != nil {
		log.Warningf("Delivery failed: %v\n", m.TopicPartition.Error)
	} else {
		log.Warningf("Delivered message to topic %s [%d] at offset %v\n",
			*m.TopicPartition.Topic, m.TopicPartition.Partition, m.TopicPartition.Offset)
	}

	defer close(deliveryChan)
	return nil

}

func (p *Producer) Close() error {
	p2 := *p.Producer
	p2.Close()
	return nil
}
