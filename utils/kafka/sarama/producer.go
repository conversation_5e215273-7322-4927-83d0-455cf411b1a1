package sarama

import (
	"errors"
	"Hotels-Scion/utils/kafka/kconfig"
	"strings"

	"github.com/Shopify/sarama"
)

type Producer struct {
	Producer *sarama.SyncProducer
	Config   kconfig.Config
}


func NewProducer(config kconfig.Config) *Producer {
	return &Producer{ Config:config}
}

func (p *Producer) Connect() error {
	cfg := sarama.NewConfig()
	cfg.Producer.RequiredAcks = sarama.WaitForLocal
	cfg.Producer.Return.Successes = true
	cfg.Metadata.Retry.Max = 0

	brokers := p.Config.Brokers

	if strings.TrimSpace(brokers) == "" {
		return errors.New("brokers can't be blank")
	}
	sp, err := sarama.NewSyncProducer(strings.Split(brokers, ","), cfg)
	if err != nil {
		return err
	}
	p.Producer = &sp
	return nil
}

func (p *Producer) SendMessage(topic string, key string, value interface{}) error {

	msg := &sarama.ProducerMessage{Topic: topic}

	// if strings.TrimSpace(key) == "" {
	// 	return errors.New("key cannot be blank")
	// }
	msg.Key = sarama.StringEncoder(strings.TrimSpace(key))

	switch v := value.(type) {
	case []byte:
		msg.Value = sarama.ByteEncoder(v)
	case string:
		msg.Value = sarama.StringEncoder(v)
	}
	p2 := *p.Producer
	_, _, err := p2.SendMessage(msg)
	return err

}

func (p *Producer) Close() error {
	p2 := *p.Producer
	if err := p2.Close(); err != nil {
		return err
	}
	return nil
}
