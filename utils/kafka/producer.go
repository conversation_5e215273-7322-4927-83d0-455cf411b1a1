package kafka

import (
	"Hotels-Scion/config"
	"Hotels-Scion/utils/kafka/confluent"
	"Hotels-Scion/utils/kafka/kconfig"
	"Hotels-Scion/utils/logging"
	"errors"
	"os"
)

var (
	KafkaProducer *ProducerWrapper
	log           = logging.GetLogger()
)

type Producer interface {
	Connect() error
	SendMessage(topic string, key string, value interface{}) error
	Close() error
}

// ProducerWrapper .
type ProducerWrapper struct {
	Producer   Producer
	AvroSchema string
	Config     kconfig.Config
}

func InitKafkaProducer() {
	os.Setenv("KRB5_CONFIG", "/etc/krb5.conf")
	os.Setenv("KRB5_TRACE", "/root/krb5.log")
	kConfig := &kconfig.Config{
		Brokers:              config.AppConfig.KafkaConfig.Brokers,
		KeytabPath:           config.AppConfig.KafkaConfig.KafkaKeytabPath,
		ServiceName:          config.AppConfig.KafkaConfig.KafkaServiceName,
		Principal:            config.AppConfig.KafkaConfig.KafkaPrincipal,
		SessionTimeout:       config.AppConfig.KafkaConfig.KafkaSessionTimeout,
		IsDeliveryReportsReq: true,
	}
	KafkaProducer = NewProducerWrapper(*kConfig)
}

// NewProducerWrapper client asks for confluent or sarama kafka basis switch
// same will be returned basis input param
func NewProducerWrapper(c kconfig.Config) *ProducerWrapper {
	var p Producer
	p = confluent.NewProducer(c)

	w := &ProducerWrapper{
		Config:   c,
		Producer: p,
	}

	log.Info("Making connection with Kafka brokers: ", c.Brokers)
	if err := p.Connect(); err != nil {
		panic(err)
	}

	return w
}

// SendMessage .
func (w *ProducerWrapper) SendMessage(topic string, key string, value interface{}) error {
	if topic == "" {
		log.Error("topic can't be blank")
		return errors.New("blank Topic")
	}

	if value == nil {
		log.Warn("nothing to push")
		return nil
	}

	return w.Producer.SendMessage(topic, key, value)
}

// Close .
func (w *ProducerWrapper) Close() {
	w.Producer.Close()
}
