package metrics

import (
	"fmt"
	"Hotels-Scion/utils/kafka"
	"Hotels-Scion/utils/kafka/kconfig"
	"Hotels-Scion/utils/kafka/sarama"
	"net/url"
	"time"
	rcmetrics "github.com/rcrowley/go-metrics"
)

const (
	tagSeparator   = "~"
	tagKVSeparator = ":"
)

// MetricManager is your interface for interacting with this library
type MetricManager interface {

	// IncrementCounter increments counter for given tags
	IncrementCounter(methodName string, tags []MTagKV)

	// UpdateTimer logs timer for given tags
	UpdateTimer(methodName string, dur time.Duration, tags []MTagKV)
}

type metricManager struct {
	timeInterval   int
	enable         bool
	metricName     string
	tagSeparator   string
	tagKVSeparator string
	rcRegistry     rcmetrics.Registry
	metricProducer kafka.Producer
}

// MTagKV encapsulates a K/V pair which are going to be a part of metric name
type MTagKV struct {
	Key   string
	Value string
}

// InitMetrics inititializes a Kafka producer, Sarama library for now, and creates a new metric manager with it.
// metricsBroker: Metrics kafka broker FQDN
// managerName: Name to provide to your manager. This will be your " field in grafana
func InitMetrics(managerName, metricsBroker string) (MetricManager, error) {
	k := sarama.NewProducer(kconfig.Config{
		Brokers: metricsBroker,
	})
	if err := k.Connect(); err != nil {
		return nil, err
	}
	return newManager(managerName, 1, true, k), nil
}

func newManager(name string, interval int, enable bool, kfk kafka.Producer) *metricManager {
	if interval < 1 {
		interval = 1
	}
	mm := metricManager{
		timeInterval:   interval,
		enable:         enable,
		metricName:     name,
		tagSeparator:   tagSeparator,
		tagKVSeparator: tagKVSeparator,
		rcRegistry:     rcmetrics.NewRegistry(),
		metricProducer: kfk,
	}
	mm.initMetrics()
	return &mm
}

func (m *metricManager) initMetrics() {
	if m.enable {
		ticker := time.NewTicker(time.Duration(m.timeInterval) * time.Minute)
		go func(evaluateMetrics func(*metricManager)) {
			for {
				select {
				case <-ticker.C:
					evaluateMetrics(m)
				}
			}
		}(Evaluator)
	}
}

// ResetMetrics is used for unregistering all metrics which have been registered until now
func (m *metricManager) ResetMetrics() (err error) {
	m.rcRegistry.UnregisterAll()
	return err
}

// func (m *MetricManager) Enable(enable bool) {
// 	if enable {
// 		m.enable = enable
// 		m.initMetrics()
// 	}
// }

// GetName returns the name of the metric manager
func (m *metricManager) GetName() string {
	return m.metricName
}

// IncrementCounter increments counter for given tags
func (m *metricManager) IncrementCounter(methodName string, tags []MTagKV) {
	counterName := getMetricName(methodName, "COUNTERS", tags)
	counter := m.rcRegistry.GetOrRegister(counterName, rcmetrics.NewCounter()).(rcmetrics.Counter)
	counter.Inc(1)
}

// UpdateTimer logs timer for given tags
func (m *metricManager) UpdateTimer(methodName string, dur time.Duration, tags []MTagKV) {
	timerName := getMetricName(methodName, "TIMERS", tags)
	timer := m.rcRegistry.GetOrRegister(timerName, rcmetrics.NewTimer()).(rcmetrics.Timer)
	timer.Update(dur)
}

func getMetricName(methodName, metricType string, tags []MTagKV) string {
	metricName := fmt.Sprintf("METRIC_NAME=%s", metricType)
	for _, _t := range tags {
		if _t.Key == "URL" {
			u, err := url.Parse(_t.Value)
			if err != nil {
				continue
			}
			_t.Value = u.Path
		}
		metricName += " " + _t.Key + "=" + _t.Value
	}
	return metricName
}
