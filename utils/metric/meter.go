package metrics

import (
	"strings"

	"github.com/gin-gonic/gin"
	metric "github.com/rcrowley/go-metrics"
)

type Meter struct {
	tags map[string]string
	M    metric.Meter
}

func NewMeter(name string) Meter {
	return Meter{make(map[string]string), metric.NewRegisteredMeter(name, nil)}
}

func (m Meter) AddTags(key, value string) {
	m.tags[key] = value
}

func (m Meter) GetTags() string {
	var result string
	for k, v := range m.tags {
		result = result + k + "=" + v
		result = result + " "
	}

	result = strings.TrimRight(result, " ")

	return result
}

func (m Meter) Mark() {
	m.M.Mark(1)
}

func (m Meter) GetData() map[string]interface{} {
	snap := m.M.Snapshot()
	values := make(map[string]interface{})

	values["COUNT"] = snap.Count()
	values["RATEONEMIN"] = snap.Rate1()
	values["RATEFIVEMIN"] = snap.Rate5()
	values["RATEFIFTEENMIN"] = snap.Rate15()

	return values
}

func (m Meter) Gin() gin.HandlerFunc {
	return func(c *gin.Context) {
		m.M.Mark(1)
		c.Next()
	}
}
