package metrics

import (
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	metric "github.com/rcrowley/go-metrics"
)

type Timer struct {
	tags map[string]string
	T    metric.Timer
}

func NewTimer(name string) Timer {
	return Timer{make(map[string]string), metric.NewRegisteredTimer(name, nil)}
}

func (t Timer) AddTags(key, value string) {
	t.tags[key] = value
}

func (t Timer) GetTags() string {
	var result string
	for k, v := range t.tags {
		result = result + k + "=" + v
		result = result + " "
	}

	result = strings.TrimRight(result, " ")

	return result
}

func (t Timer) Update(d time.Duration) {
	t.T.Update(d)
}

func (t Timer) GetData() map[string]interface{} {
	snap := t.T.Snapshot()
	ps := snap.Percentiles([]float64{0.5, 0.75, 0.95, 0.99, 0.999})
	values := make(map[string]interface{})

	values["MIN"] = snap.Min()
	values["MAX"] = snap.Max()
	values["MEAN"] = snap.Mean()
	values["MEDIAN"] = ps[0]
	values["PERCENTILE75"] = ps[1]
	values["PERCENTILE95"] = ps[2]
	values["PERCENTILE99"] = ps[3]
	values["PERCENTILE999"] = ps[4]

	return values
}

func (t Timer) Gin() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		defer func() {
			dur := time.Since(start)
			t.Update(dur)
		}()
		c.Next()
	}
}
