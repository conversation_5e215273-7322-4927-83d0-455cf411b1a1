package metrics

import (
	"strings"

	"github.com/gin-gonic/gin"
	metric "github.com/rcrowley/go-metrics"
)

type Counter struct {
	tags map[string]string
	C    metric.Counter
}

func NewCounter(name string) Counter {
	return Counter{make(map[string]string), metric.NewRegisteredCounter(name, nil)}
}

func (c Counter) AddTags(key, value string) {
	c.tags[key] = value
}

func (c Counter) GetTags() string {
	var result string
	for k, v := range c.tags {
		result = result + k + "=" + v
		result = result + " "
	}

	result = strings.TrimRight(result, " ")

	return result
}

func (c Counter) Inc() {
	c.C.Inc(1)
}

func (c Counter) Clear() {
	c.C.Clear()
}

func (c Counter) GetData() map[string]interface{} {
	values := make(map[string]interface{})

	snap := c.C.Snapshot()

	values["COUNT"] = snap.Count()

	return values
}

func (c Counter) Gin() gin.HandlerFunc {
	return func(cg *gin.Context) {
		c.C.Inc(1)
		cg.Next()
	}
}
