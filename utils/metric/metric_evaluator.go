package metrics

import (
	"fmt"
	"net"
	"os"
	"strconv"
	"strings"
	"time"
)

var QUEUE_NAME = "opentsdb_queue"
var METRIC_NAME = "METRIC_NAME"
var IP_ADDRESS = "IP_ADDRESS"
var DATA_CENTER = "DC"
var VALUE = "VALUE"

func Evaluator(m *metricManager) {
	AllMetricsEvaluator(m)
}

func pushToKafka(data map[string]string, tags string, t time.Time, applicationName string, m *metricManager) (err error) {
	for k, v := range data {
		stdTags := getStandardTags(k, "")
		tagsR := tags + " " + stdTags
		command := "put " + applicationName + " " + strconv.FormatInt(t.UnixNano()/1000000, 10) + " " + v + " " + tagsR
		tagsR = ""
		if err1 := m.metricProducer.SendMessage(QUEUE_NAME, "", command); err1 != nil {
			fmt.Println(err1.Error())
		}
	}
	return err
}

func getStandardTags(key string, metricType string) string {
	tags := make(map[string]string)

	tags[DATA_CENTER] = getDataCenter()
	tags[IP_ADDRESS] = GetIPAddr()
	tags[VALUE] = strings.ToUpper(key)

	var result string

	for k, v := range tags {
		result = result + k + "=" + v
		result = result + " "
	}

	result = strings.TrimRight(result, " ")
	return result
}

func AllMetricsEvaluator(mm *metricManager) (err error) {
	newMetrics := mm.rcRegistry.GetAll()

	if len(newMetrics) > 0 {
		for metricName, metricValueMap := range newMetrics {
			dataToSend := make(map[string]string)
			tags := metricName

			if len(metricValueMap) == 0 {
				continue
			}

			for kd, vd := range metricValueMap {
				switch vd.(type) {
				case int64:
					dataToSend[kd] = strconv.FormatInt(vd.(int64), 10)
				case float64:
					dataToSend[kd] = strconv.FormatFloat(vd.(float64), 'f', -1, 64)
				}
			}
			pushToKafka(dataToSend, tags, time.Now(), mm.GetName(), mm)

		}
		//resetMetrics
		mm.ResetMetrics()
	}

	return err
}

func GetIPAddr() string {

	var host string
	env := os.Getenv("HOST_IP")
	if env != "" {
		return env
	}

	intrf, _ := net.Interfaces()

	for _, i := range intrf {
		if i.Name == "eth0" {
			e, _ := i.Addrs()
			for _, k := range e {
				if ipnet, ok := k.(*net.IPNet); ok && !ipnet.IP.IsLoopback() {
					if ipnet.IP.To4() != nil {
						host = ipnet.IP.String()
					}
				}
			}
		}
	}

	return host
}

func getDataCenter() string {
	var dc string
	host := GetIPAddr()
	if strings.Contains(host, "10.96") {
		dc = "mum"
	} else if strings.Contains(host, "10.66") {
		dc = "cmmt"
	}
	return dc
}
