package pms

import (
	"errors"
	"fmt"
	"io"
	"net/http"
)

type Reloadable interface {
	Reload() (string, error)
}

//ReloadableProp .
type ReloadableProp struct {
}

//Reload .
func (r *ReloadableProp) Reload() (string, error) {
	return "reloaded", nil
}

//PropertyManager .
type PropertyManager struct {
	client http.Client
	url    string
}

//New accepts two params. First takes the REST URL for the config key and
//second is the subscriptionUrl URL
func New(url string, ) *PropertyManager {
	p := &PropertyManager{
		http.Client{},
		url,
	}

	return p
}

//Get .
func (p *PropertyManager) Get(qualifier string) Reloadable {
	return &ReloadableProp{}
}

//Load hits the REST endpoint and fetches the property from PropertyManager
//It returns the io.Reader which wraps the response. This can be further
//consumed by util/config.ReadConfig()
func (p *PropertyManager) Load() (io.Reader, error) {
	req, err := http.NewRequest("GET", p.url, nil)
	if err != nil {
		return nil, err
	}

	res, errConn := p.client.Do(req)
	if errConn != nil {
		return nil, errConn
	}

	if res.ContentLength == 0 || (res.StatusCode < 200 || res.StatusCode >= 300) {
		return nil, errors.New(fmt.Sprintf("Error from Girgit : Code : %s for Url : %s", res.Status, p.url))
	}

	return res.Body, nil
}
