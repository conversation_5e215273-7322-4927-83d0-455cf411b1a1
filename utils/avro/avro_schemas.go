package avro

import (
	"Hotels-Scion/config"
	"Hotels-Scion/utils/logging"
	"encoding/json"
	"errors"
	"fmt"
	"io/ioutil"
	"net/http"
)

var (
	log           = logging.GetLogger()
	AvroSchemaMap = make(map[string]string)
)

func InitializeAvroSchemas() {
	schema, err := fetchAvroSchema(config.AppConfig.AvroConfig.CrossellTemplateId)
	if err != nil {
		log.Fatalln("Failed to create request log file:", err)
	}
	AvroSchemaMap[config.AppConfig.AvroConfig.CrossellTemplateId] = schema
}

func fetchAvroSchema(templateId string) (string, error) {
	url := fmt.Sprintf(config.AppConfig.AvroConfig.AvroSchemaURL, templateId)
	if url == "" {
		log.Error("avro url is blank hence returning without setting")
		return "", errors.New("blank schema URL")
	}

	req, e := http.NewRequest("GET", url, nil)

	if e != nil {
		return "", e
	}

	client := &http.Client{}
	res, err := client.Do(req)

	if err != nil {
		return "", e
		panic(e)
	}

	defer res.Body.Close()

	resp, err := ioutil.ReadAll(res.Body)

	if err != nil {
		panic(e)
		return "", e
	}
	data := make(map[string]string)
	json.Unmarshal(resp, &data)
	log.Warn("Schema from : ", url, ", is: ", string(data["schema"]))

	if res.StatusCode != http.StatusOK {
		log.Error("Avro generation failed: ", res.StatusCode)
		return "", errors.New("avro schema generation failed")
	}

	return string(data["schema"]), nil
}
