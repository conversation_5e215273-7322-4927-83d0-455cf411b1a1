package cache

import (
	logging2 "Hotels-Scion/utils/logging"
	"encoding/json"
	"gopkg.in/couchbase/gocb.v1"
	"reflect"
	"time"
)

var (
	log    = logging2.GetLogger()
	bucket *gocb.Bucket
)

func InitializeCouchDb() {
//	log.Infof("hosts %s  bucket %s  pass %s",config.AppConfig.Couch.Hosts,config.AppConfig.Couch.Bucket,config.AppConfig.Couch.Password)
//	cluster, err := gocb.Connect(config.AppConfig.Couch.Hosts)
//	if err != nil {
//		log.Fatalf("Error occurred while connecting to couch: %v", err)
//		panic(err)
//	}
//	log.Info("Couch cluster connected")
//	bkt, errBucket := cluster.OpenBucket(config.AppConfig.Couch.Bucket, config.AppConfig.Couch.Password)
//	if errBucket != nil {
//		log.Fatalf("Error occurred while connecting to couch: %v", errBucket)
//		panic(errBucket)
//	}else{
//		bkt.SetOperationTimeout(time.Duration(config.AppConfig.Couch.KeyValueTimeoutInMillis)  * time.Millisecond)
//	}
//	bucket = bkt
//	log.Info("bucket connected")

}

func getBucket(name string) *gocb.Bucket {
	return bucket
}

func Upsert(key string, data interface{}, expiry time.Duration, bucketName string) error {
	b := getBucket(bucketName)
	d := reflect.ValueOf(data).Interface()
	e := int(expiry) / 1000000000
	_, err := b.Upsert(key, &d, uint32(e))
	if err != nil {
		log.Errorf("Error in Upsert %v", err)
		return err
	}
	return nil
}

func Get(key string, bucketName string) ([]byte, error) {
	b := getBucket(bucketName)
	var doc interface{}
	_, err := b.Get(key, &doc)

	if err != nil {
		log.Errorf("Error in Get for key %s , error =  %v", key, err)
		return nil, err
	}

	var s []byte
	s, _ = json.Marshal(doc)
	return s, nil
}
