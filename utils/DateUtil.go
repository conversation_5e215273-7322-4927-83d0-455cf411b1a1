package utils

import (
	"Hotels-Scion/constants"
	"time"
)

func FormatDateString(date string, inputFormat string, outputFormat string) string {
	t, _ := time.Parse(inputFormat, date)
	return t.Format(outputFormat)
}

func GetTimeFromEpochAndZone(zone string, ts int64) time.Time {
	loc, err1 := time.LoadLocation(zone)

	var cTime time.Time
	secs := ts / 1000
	if err1 == nil {
		cTime = time.Unix(secs, 0).In(loc)
	} else {
		cTime = time.Unix(secs, 0)
	}

	return cTime
}

func DateToEpoch(date string) int64 {
	inputFormat := "02-01-2006"

	t, _ := time.Parse(inputFormat, date)

	return t.Unix() * 1000
}

func GetTimeInFormattedString(t time.Time) string {
	//"2006-01-02" is the magic date to be used to parse date
	return t.Format(constants.DT_FRMT_SEARCH_CNTXT)
}

func GetTimeInDDMMYYYYFormattedString(t time.Time) string {
	//"2006-01-02" is the magic date to be used to parse date
	return t.Format(constants.DT_FRMT_DEEPLINK_URL_V2)
}
