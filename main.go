package main

import (
	"Hotels-Scion/config"
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/constants"
	"Hotels-Scion/handlers"
	"Hotels-Scion/utils/avro"
	"Hotels-Scion/utils/cache"
	"Hotels-Scion/utils/kafka"
	"Hotels-Scion/utils/logging"
	"io"
	"net/http/pprof"
	"os"
	"runtime"
	"strconv"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gopkg.in/alecthomas/kingpin.v2"
)

var (
	confPath = kingpin.Flag("configPath", "Path for configuration files").
			Default("./resources").Short('r').String()
	env = kingpin.Flag("environment", "Environment to use for running the application").
		Default("dev").Short('e').Enum("dev", "qa", "staging", "prod")
	log = logging.GetLogger()
)

func main() {

	log.WithFields(logrus.Fields{
		"GoVersion": runtime.Version(),
		"GOOS":      runtime.GOOS,
		"GOARCH":    runtime.GOARCH,
		"NumCPU":    runtime.NumCPU(),
		"GOPATH":    os.Getenv("GOPATH"),
		"GOROOT":    runtime.GOROOT(),
	}).Info("Starting up ... wait for it... *HOTELS SCION*")

	kingpin.Parse()
	config.Initialize(*confPath, *env)
	cache.InitializeCouchDb()
	restconnectors.InitHTTPClients()
	kafka.InitKafkaProducer()
	avro.InitializeAvroSchemas()
	initializeHTTPServer()
	log.Info("Started HOTELS SCION")

}

func initializeHTTPServer() {

	logfile, err := os.OpenFile(constants.LogFilePath+"access.log", os.O_RDWR|os.O_CREATE|os.O_APPEND, os.ModePerm)
	if err != nil {
		log.Fatalln("Failed to create request log file:", err)
	}

	router := gin.New()

	//	gin.DefaultWriter = io.MultiWriter(logfile)
	router.Use(logging.LoggerWithWriter(io.MultiWriter(logfile)))
	router.Use(gin.Recovery())
	apiGroup := router.Group("/hotels-scion")
	{
		apiGroup.POST("/api/crossSell", handlers.HandleCrossellRequest)
		apiGroup.POST("/api/searchHotels", handlers.SearchHotelsPersonalised)
		apiGroup.POST("/api/similarHotels", handlers.GetSimilarHotels)
		apiGroup.POST("/api/fetchCollections", handlers.FetchCollectionsNew)
		apiGroup.POST("/api/getPropertyType", handlers.GetAltAccoPropertyType)
		apiGroup.GET("/health", handlers.GetHealthStatus)
		apiGroup.POST("/api/cross-sell-stream", handlers.HandleCrossSellStreamRequest)
		apiGroup.POST("/api/v2/cross-sell-stream", handlers.HandleCrossSellV2StreamRequest)
		apiGroup.POST("/api/v2/landing-card-discovery", handlers.LandingDiscoveryV2StreamRequest)
		apiGroup.POST("/api/v2/mob-landing", handlers.MobLandingHandler)
	}
	// TODO : This is mainly added for instrumentation, should be removed in normal prod pipeline
	pprofAddUrls(router)
	router.Run(":" + strconv.Itoa(config.AppConfig.Port))
}

// Add several routes from package `net/http/pprof` to *gin.Engine object
func pprofAddUrls(router *gin.Engine) {

	router.GET("/debug/pprof", func(ginContext *gin.Context) {
		pprof.Index(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/heap", func(ginContext *gin.Context) {
		pprof.Handler("heap").ServeHTTP(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/goroutine", func(ginContext *gin.Context) {
		pprof.Handler("goroutine").ServeHTTP(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/block", func(ginContext *gin.Context) {
		pprof.Handler("block").ServeHTTP(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/threadcreate", func(ginContext *gin.Context) {
		pprof.Handler("threadcreate").ServeHTTP(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/cmdline", func(ginContext *gin.Context) {
		pprof.Cmdline(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/profile", func(ginContext *gin.Context) {
		pprof.Profile(ginContext.Writer, ginContext.Request)
	})

	router.GET("/debug/pprof/symbol", func(ginContext *gin.Context) {
		pprof.Symbol(ginContext.Writer, ginContext.Request)
	})
}
