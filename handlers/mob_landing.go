package handlers

import (
	"Hotels-Scion/helpers"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"Hotels-Scion/services"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"io/ioutil"
	"net/http"
)

func MobLandingHandler(c *gin.Context) {
	mobLandingRequest, err := convertRequestToMobLandingRequest(c.Request)

	if mobLandingRequest != nil {
		requestJSON, err := json.Marshal(*mobLandingRequest)
		if err != nil {
			logger.Errorf("Error marshalling MobLandingHandler, request: %v", *mobLandingRequest)
		} else {
			logger.Infof("MobLandingHandler Request is %s", string(requestJSON))
		}
	}

	resp := response.MobLandingResponse{}
	//setHeader(c)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	var error = helpers.ValidateMobLandingRequest(mobLandingRequest)
	if error == "" {
		resp, _ = services.GetMobLandingResponse(mobLandingRequest, c)
	}
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}
	c.JSON(http.StatusOK, resp)
}

func setHeader(c *gin.Context) {

	// Create a map to store the headers
	headersMap := make(map[string]string)

	// Populate the map with the headers from the context
	for key, values := range c.Request.Header {
		headersMap[key] = values[0]
	}

	// Add the mmt-auth header to the map

	// Print the headers map for demonstration purposes
	c.JSON(http.StatusOK, headersMap)
}

func convertRequestToMobLandingRequest(r *http.Request) (*request.MobLandingRequest, error) {
	body, err := ioutil.ReadAll(r.Body)
	if err != nil {
		return nil, err
	}
	defer r.Body.Close()

	var mobLandingRequest request.MobLandingRequest
	err = json.Unmarshal(body, &mobLandingRequest)
	if err != nil {
		return nil, err
	}

	return &mobLandingRequest, nil
}
