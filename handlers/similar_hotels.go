package handlers

import (
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"math/rand"
	"net/http"
	"strconv"
)

//this is a proxy api for hotelsComparator
func GetSimilarHotels(c *gin.Context) {

	var similarHotelRequest request.SimilarHotelRequest
	_ = c.BindJSON(&similarHotelRequest)

	requestJSON, err := json.Marshal(similarHotelRequest)
	if err != nil {
		logger.Errorf("Error marshalling GetSimilarHotels, request: %v", similarHotelRequest)
	} else {
		logger.Infof("GetSimilarHotels Request is %s", string(requestJSON))
	}

	comparatorRequest, _ := json.Marshal(&similarHotelRequest.Hotel.SearchEvent)
	rawcomapartorResponse, err := restconnectors.PostComparatorHotelsRequestGeneric(&comparatorRequest)

	finalResponse := mapToCosmosResponseFormat(rawcomapartorResponse, err)
	c.J<PERSON>(http.StatusOK, finalResponse)

}

func mapToCosmosResponseFormat(hotelData *json.RawMessage, err error) response.SearchHotelResponse {
	result := hotelData
	if err != nil {
		var errResp json.RawMessage
		errResp, _ = json.Marshal(err.Error())
		result = &errResp
	}

	SimilarHotelPersonalisedData := &response.SimilarHotelData{
		Data: response.SimilarHotelDataInner{
			Heading:    "Similar Hotels",
			SubHeading: "Hotels you might like",
			Data:       result,
		},
		DataKey: strconv.Itoa(rand.Intn(100000)),
	}
	searchHotelResponse := response.SearchHotelResponse{
		OfferData: response.OfferData{
			SimilarHotelData: SimilarHotelPersonalisedData,
		},
		Status:        "OK",
		StatusDetails: "",
	}
	return searchHotelResponse
}
