package handlers

import (
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/request/common"
	"Hotels-Scion/services"
	"encoding/json"
	"github.com/gin-gonic/gin"
	guuid "github.com/google/uuid"
	"net/http"
)

func GetAltAccoPropertyType(c *gin.Context) {
	var getPropertyTypeResponse interface{}
	var altAccoPropertyReq request.AltAccoPropertyReq
	_ = c.BindJSON(&altAccoPropertyReq)

	requestJSON, err := json.Marshal(altAccoPropertyReq)
	if err != nil {
		logger.Errorf("Error marshalling GetAltAccoPropertyType, request: %v", altAccoPropertyReq)
	} else {
		logger.Infof("GetAltAccoPropertyType Request is %s", string(requestJSON))
	}

	uuid := c.GetHeader("uuid")
	cosmosCommon := altAccoPropertyReq.Common
	correlationKey := cosmosCommon.CorrelationKey
	if len(correlationKey) == 0 {
		correlationKey = guuid.New().String()
	}
	logger.Infof("GetPropertyType request received for uuid : %s , correlationKey : %s", uuid, correlationKey)

	cosmosSearchEvent, _ := json.Marshal(&altAccoPropertyReq.Hotel.SearchEvent)
	var searchParams common.SearchEvent
	err = json.Unmarshal(cosmosSearchEvent, &searchParams)
	if err != nil {
		panic(err)
	}

	getPropertyTypeResponse = services.GetAltAccoData(&searchParams, correlationKey)
	c.JSON(http.StatusOK, getPropertyTypeResponse)
}
