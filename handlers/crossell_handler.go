package handlers

import (
	"Hotels-Scion/config"
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/constants"
	"Hotels-Scion/helpers"
	"Hotels-Scion/models"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/downstream/orchestrator"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"Hotels-Scion/services"
	"Hotels-Scion/utils/logging"
	"encoding/json"
	"math/rand"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	guuid "github.com/google/uuid"
)

var logger = logging.GetLogger()

func HandleCrossellRequest(c *gin.Context) {
	var crossellResponse interface{}

	var cosmosRequest request.HotelCosmosRequest
	_ = c.BindJSON(&cosmosRequest)

	requestJSON, err := json.Marshal(cosmosRequest)
	if err != nil {
		logger.Errorf("Error marshalling HandleCrossellRequest, request: %v", cosmosRequest)
	} else {
		logger.Infof("HandleCrossellRequest Request is %s", string(requestJSON))
	}

	auth := c.GetHeader("mmtAuth")
	uuid := c.GetHeader("uuid")
	profileType := c.GetHeader("profileType")
	ver := c.GetHeader("ver")

	common := cosmosRequest.Common
	user := common.User

	correlationKey := common.CorrelationKey
	if len(correlationKey) == 0 {
		correlationKey = guuid.New().String()
	}
	logger.Infof("Crossell request received for uuid : %s , deviceId : %s , correlationKey : %s", uuid, user.DeviceID, correlationKey)

	// Validations
	error := helpers.ValidateCrosselRequest(&common)

	if error != "" {
		crossellResponse = response.HotelCrossSellResponse{Status: "Forbidden", StatusDetails: error}
	} else {
		requestBO := models.RequestBO{}
		requestBO.DeviceId = user.DeviceID
		requestBO.BookingDevice = common.BookingDevice
		requestBO.LoginEmailId = user.Email
		requestBO.VisitorId = user.VisitorID
		requestBO.Uuid = uuid
		requestBO.ProfileType = profileType
		ctx := strings.Split(common.PageContext, "_")
		requestBO.PageContext = ctx[len(ctx)-1]
		requestBO.BookingId = common.BookingID
		requestBO.CorrelationKey = correlationKey

		requestBO.CrossSellBookingDetail = cosmosRequest.BookingDetails
		requestBO.MmtAuth = auth
		requestBO.AppVersion = ver
		requestBO.Channel = common.Channel
		requestBO.SkipValidation = true
		requestBO.IdContext = common.IDContext
		requestBO.RequestType = common.RequestType
		requestBO.DeviceType = common.DeviceType
		requestBO.CrossSellUniqueIds = common.UniqueIds.CrossSellPersonalised
		requestBO.ExpData = helpers.BuildExperimentDataFromString(common.HotelExperimentData)

		crossellResponse = services.GetCrossellResponse(&requestBO)
	}

	c.JSON(http.StatusOK, crossellResponse)
}

func FetchCollections(c *gin.Context) {

	var finalResponse interface{}

	var cosmosRequest request.HotelCosmosRequest
	_ = c.BindJSON(&cosmosRequest)

	requestJSON, err := json.Marshal(cosmosRequest)
	if err != nil {
		logger.Errorf("Error marshalling FetchCollections, request: %v", cosmosRequest)
	} else {
		logger.Infof("FetchCollections Request is %s", string(requestJSON))
	}

	auth := c.GetHeader("mmtAuth")
	uuid := c.GetHeader("uuid")
	profileType := c.GetHeader("profileType")
	ver := c.GetHeader("ver")

	var pageType string
	query := c.Request.URL.Query()
	pageTypeParamArr := query["pageType"]
	if len(pageTypeParamArr) > 0 {
		pageType = pageTypeParamArr[0]
	}

	common := cosmosRequest.Common
	user := common.User

	common.PageType = pageType

	//Request validation
	valErr := helpers.ValidateFetchCollectionRequest(&common)
	if valErr != "" {
		finalResponse = response.HotelCosmosResponse{Status: "Forbidden", StatusDetails: valErr}
	} else {
		correlationKey := common.CorrelationKey
		if len(correlationKey) == 0 {
			correlationKey = guuid.New().String()
		}
		logger.Infof("Fetch Collections request received for uuid : %s , deviceId : %s , correlationKey : %s", uuid, user.DeviceID, correlationKey)

		requestBO := models.RequestBO{}
		requestBO.DeviceId = user.DeviceID
		requestBO.BookingDevice = common.BookingDevice
		requestBO.LoginEmailId = user.Email
		requestBO.VisitorId = user.VisitorID
		requestBO.Uuid = uuid
		requestBO.ProfileType = profileType
		requestBO.CorrelationKey = correlationKey

		requestBO.MmtAuth = auth
		requestBO.AppVersion = ver
		requestBO.Channel = common.Channel
		requestBO.SkipValidation = true
		requestBO.IdContext = common.IDContext
		requestBO.RequestType = common.RequestType
		requestBO.DeviceType = common.DeviceType
		requestBO.Locus = common.Locus
		requestBO.LocationName = common.LocationName
		requestBO.LocationType = common.Locationtype
		requestBO.Checkin = common.CheckIn
		requestBO.Checkout = common.Checkout
		requestBO.AthenaCategory = common.AthenaCategory

		updateReqBOForPageType(&requestBO, pageType)
		if config.AppConfig.LocationDetailsFlag && &common.LocationData != nil {
			updateCityFromLatLng(&requestBO, &common.LocationData)
		}

		requestBO.ExpData = helpers.BuildExperimentDataFromString(common.HotelExperimentData)
		requestBO.ExpDataStr = common.HotelExperimentData
		rawResp, err := services.FetchHotelCollections(&requestBO)
		finalResponse = mapToCosmosResponse(&requestBO, rawResp, err)
	}

	c.JSON(http.StatusOK, finalResponse)
}

func FetchCollectionsNew(c *gin.Context) {

	var finalResponse interface{}
	defer func() {
		if err := recover(); err != nil {
			finalResponse = response.FetchCollectionResponse{Status: "ERROR", Message: "Something went wrong in FetchCollections"}
			logger.Errorf("Exception occurred in FetchCollectionsNew: %s error %s", constants.CBFetchCollectionsAPI, err)
			c.JSON(http.StatusInternalServerError, finalResponse)
		}
	}()

	var fetchCollectionRequest request.FetchCollectionRequest
	_ = c.BindJSON(&fetchCollectionRequest)

	requestJSON, err := json.Marshal(fetchCollectionRequest)
	if err != nil {
		logger.Errorf("Error marshalling FetchCollectionsNew, request: %v", fetchCollectionRequest)
	} else {
		logger.Infof("FetchCollectionsNew Request is %s", string(requestJSON))
	}

	uuid := c.GetHeader("uuid")

	valErr := helpers.ValidateScionFetchCollectionRequest(&fetchCollectionRequest)
	if valErr != "" {
		finalResponse = response.FetchCollectionResponse{Status: "Failure", Message: valErr}
	} else {
		correlationKey := fetchCollectionRequest.CorrelationKey
		if len(correlationKey) == 0 {
			correlationKey = guuid.New().String()
		}
		logger.Infof("Fetch Collections request received for uuid : %s , correlationKey : %s", uuid, correlationKey)

		fetchCollectionRequestCG := request.FetchCollectionRequestCG{}
		fetchCollectionRequestCG.RequestDetails = setRequestDetails(&fetchCollectionRequest)
		fetchCollectionRequestCG.ImageDetails = setImageDetails(&fetchCollectionRequest)
		fetchCollectionRequestCG.FeatureFlags = setFeatureFlags(&fetchCollectionRequest)
		fetchCollectionRequestCG.DeviceDetails = setDeviceDetails(&fetchCollectionRequest)
		fetchCollectionRequestCG.SearchCriteria = setSearchCriteria(&fetchCollectionRequest, c)
		fetchCollectionRequestCG.ExpData = fetchCollectionRequest.Context.ExperimentData
		fetchCollectionRequestCG.CorrelationKey = correlationKey

		e, _ := json.Marshal(fetchCollectionRequestCG)
		logger.Infof("Request for fetchCollection is: %s", string(e))

		resp, err := restconnectors.ClientStruct.PostFetchCollectionRequest(&fetchCollectionRequestCG, c)

		finalResponse = transformResponse(fetchCollectionRequest, &resp.Response, err)
	}

	c.JSON(http.StatusOK, finalResponse)
}

func setImageDetails(fetchCollectionRequest *request.FetchCollectionRequest) request.ImageDetails {
	var imageDetail request.ImageDetails
	if reflect.ValueOf(fetchCollectionRequest.ImageDetails).IsZero() {
		imageDetail.Categories = buildCategories()
		var types []string
		types = append(types, "professional")
		imageDetail.Types = types
	} else {
		imageDetail = fetchCollectionRequest.ImageDetails
	}
	return imageDetail
}

func buildCategories() []request.ImageCategory {
	var imageCategories []request.ImageCategory
	var imageCategory request.ImageCategory
	imageCategory.Type = "H"
	imageCategory.Count = 2
	imageCategory.Height = 388.8
	imageCategory.Width = 583.1999999999999
	imageCategory.ImageFormat = "webp"
	imageCategories = append(imageCategories, imageCategory)
	return imageCategories
}

func transformResponse(collectionRequest request.FetchCollectionRequest, cgResp *response.Response, err error) interface{} {
	var resp response.FetchCollectionResponse
	if err != nil || cgResp == nil || cgResp.FetchCollectionList == nil {
		logger.Error("Error in fetchCollections", err)
		resp = response.FetchCollectionResponse{Status: "ERROR", Message: "No Data Found for FetchCollections"}
	} else {
		resp = response.FetchCollectionResponse{Status: "OK"}
		resp.CorrelationKey = cgResp.CorrelationKey
		resp.CardData = make(map[string]request.Card)
		valueStayCollection := getValueStaysFetchCollectionCard(cgResp)
		cgCardMap := make(map[string]bool)
		for i := 0; i < len(cgResp.FetchCollectionList); i++ {
			cgCardMap[cgResp.FetchCollectionList[i].CardType] = true
		}
		for i := 0; i < len(collectionRequest.Cards); i++ {
			if collectionRequest.Cards[i].CardId == constants.VALUESTAYS && valueStayCollection.CardType == constants.VALUESTAYS && valueStayCollection.CardInfo.CardPayload.GenericCardData == nil {
				continue
			}
			if _, exist := cgCardMap[collectionRequest.Cards[i].CardId]; exist {
				resp.CardData[collectionRequest.Cards[i].CardId] = collectionRequest.Cards[i]
			}
		}
		for i := 0; i < len(cgResp.FetchCollectionList); i++ {
			fetchCollectionCard := cgResp.FetchCollectionList[i]
			if strings.EqualFold(fetchCollectionCard.CardType, constants.COLLECTION) && len(fetchCollectionCard.CardId) != 0 {
				if value, exist := resp.CardData[fetchCollectionCard.CardId]; exist {
					value.Data.HotelList = buildHotelList(fetchCollectionCard.HotelList, collectionRequest)
					if len(value.Data.HotelList) != 0 {
						value.Data.OfferPersuasions = getOfferPersuasions()
					}
					value.HeaderData = request.HeaderData{Header: fetchCollectionCard.Heading, Subheader: fetchCollectionCard.SubHeading, LobSubheader: fetchCollectionCard.CollectionDescription}
					value.HeaderData.Cta = fetchCollectionCard.Cta
					resp.CardData[fetchCollectionCard.CardId] = value
				}
			}
			if value, exist := resp.CardData[fetchCollectionCard.CardType]; exist {
				value.Data.CardList = fetchCollectionCard.CardList
				value.Data.CardInfo = fetchCollectionCard.CardInfo
				value.HeaderData = request.HeaderData{Header: fetchCollectionCard.Heading, Subheader: fetchCollectionCard.SubHeading, LobSubheader: fetchCollectionCard.CollectionDescription}
				value.HeaderData.Cta = fetchCollectionCard.Cta
				value.Data.BgImageUrl = fetchCollectionCard.BgImageUrl
				resp.CardData[fetchCollectionCard.CardType] = value
			}
		}

		if value, exist := resp.CardData["STATICFILTERS"]; exist {
			value.Data.AppliedFilterMap = cgResp.SuggestedFilters.AppliedFilterMap
			value.HeaderData = request.HeaderData{Header: cgResp.SuggestedFilters.Heading, Subheader: cgResp.SuggestedFilters.SubHeading, LobSubheader: cgResp.SuggestedFilters.CollectionDescription}
			value.HeaderData.Cta = cgResp.SuggestedFilters.Cta
			value.Data.BgImageUrl = cgResp.SuggestedFilters.BgImageUrl
			resp.CardData["STATICFILTERS"] = value
		}
	}

	return resp
}

func getValueStaysFetchCollectionCard(cgResp *response.Response) response.FetchCollectionCard {
	for i := 0; i < len(cgResp.FetchCollectionList); i++ {
		fetchCollectionCard := cgResp.FetchCollectionList[i]
		if fetchCollectionCard.CardType == constants.VALUESTAYS {
			return fetchCollectionCard
		}
	}
	return response.FetchCollectionCard{}
}

func buildHotelList(hesHotelList []request.HotelHES, collectionRequest request.FetchCollectionRequest) []request.HotelCG {
	var hotelListCG []request.HotelCG
	for i := 0; i < len(hesHotelList); i++ {
		var hotelCG request.HotelCG
		hotelCG.Id = hesHotelList[i].Id
		hotelCG.Name = hesHotelList[i].Name
		hotelCG.PropertyType = hesHotelList[i].PropertyType
		if !reflect.ValueOf(collectionRequest.ImageDetails).IsZero() && len(hesHotelList[i].MainImages) > 0 {
			hotelCG.MainImages = hesHotelList[i].MainImages
		} else if reflect.ValueOf(collectionRequest.ImageDetails).IsZero() && len(hesHotelList[i].MainImages) >= 2 {
			var slice = make([]string, 2)
			slice[0] = hesHotelList[i].MainImages[0]
			slice[1] = hesHotelList[i].MainImages[1]
			hotelCG.MainImages = slice
		}
		hotelCG.StarRating = hesHotelList[i].StarRating
		hotelCG.CurrencyCode = hesHotelList[i].CurrencyCode
		hotelCG.DisplayFare = hesHotelList[i].DisplayFare
		hotelCG.CityName = hesHotelList[i].CityName
		hotelCG.DesktopDeeplink = hesHotelList[i].DesktopDeeplink
		hotelCG.AppDeepLink = hesHotelList[i].AppDeepLink
		hotelCG.LocationPersuasion = hesHotelList[i].LocationPersuasion
		hotelCG.FreeCancellationText = hesHotelList[i].FreeCancellationText
		hotelCG.Inclusions = hesHotelList[i].Inclusions
		hotelCG.CancellationTimeline = hesHotelList[i].CancellationTimeline
		hotelCG.FlyfishReviewSummary = hesHotelList[i].FlyfishReviewSummary
		hotelListCG = append(hotelListCG, hotelCG)
	}
	return hotelListCG
}

func getOfferPersuasions() []request.OfferPersuasion {
	var offerPersuasionMap = config.PmsCommonConfig.OfferPersuasionsMap
	var res map[string][]request.OfferPersuasion
	_ = json.Unmarshal([]byte(offerPersuasionMap), &res)
	offerPersuasions := res[constants.OFFER_PERSUASIONS]
	out, err := json.Marshal(offerPersuasions)
	if err != nil {
		logger.Infof("Error while getting offerPersuasions .... %s", err.Error())
		panic(err)
	}
	logger.Infof("offerPersuasions ..... %s", out)
	for i := 0; i < len(offerPersuasions); i++ {
		logger.Infof("Getting endOfDayTime ..... ")
		endOfDayTime := GetEndOfDayTime("Asia/Dubai")
		logger.Infof("endOfDayTime ..... %s", endOfDayTime.String())
		offerPersuasions[i].ExpiryTimestamp = endOfDayTime.UnixNano() / 1000000
	}
	return offerPersuasions
}

func GetEndOfDayTime(location string) time.Time {
	var err error
	var locationUAE *time.Location
	if locationUAE, err = time.LoadLocation(location); err != nil {
		logger.Infof("Error while getting locationUAE .... %s", err.Error())
		panic(err)
	}
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, locationUAE)
	return endOfDay
}

func setRequestDetails(fetchCollectionRequest *request.FetchCollectionRequest) request.RequestDetails {
	var requestDetails request.RequestDetails
	requestDetails.Uuid = fetchCollectionRequest.User.Uuid
	requestDetails.PageContext = fetchCollectionRequest.Context.Scope
	requestDetails.FunnelSource = fetchCollectionRequest.Context.FunnelSource
	requestDetails.SrLat = fetchCollectionRequest.User.Location.LocationData.DeviceCurrentLocation.Lat
	requestDetails.SrLng = fetchCollectionRequest.User.Location.LocationData.DeviceCurrentLocation.Lng
	if fetchCollectionRequest.User.VisitorId == "" {
		requestDetails.VisitorId = constants.VID
	} else {
		requestDetails.VisitorId = fetchCollectionRequest.User.VisitorId
	}
	requestDetails.VisitNumber = 3
	requestDetails.IdContext = fetchCollectionRequest.Context.ContextId
	requestDetails.Requestor = "SCION"
	return requestDetails
}

func setFeatureFlags(fetchCollectionRequest *request.FetchCollectionRequest) request.FeatureFlags {
	var featureFlags request.FeatureFlags
	featureFlags.Locus = true
	return featureFlags
}

func setDeviceDetails(fetchCollectionRequest *request.FetchCollectionRequest) request.DeviceDetails {
	var deviceDetails request.DeviceDetails
	deviceDetails.DeviceId = fetchCollectionRequest.User.DeviceInfo.Id
	deviceDetails.BookingDevice = fetchCollectionRequest.User.DeviceInfo.Platform
	deviceDetails.DeviceType = fetchCollectionRequest.User.DeviceInfo.Model
	deviceDetails.AppVersion = fetchCollectionRequest.User.DeviceInfo.AppVer
	return deviceDetails
}

func setSearchCriteria(fetchCollectionRequest *request.FetchCollectionRequest, c *gin.Context) request.SearchCriteria {
	var searchCriteria request.SearchCriteria
	searchCriteria.NearBySearch = false
	searchCriteria.PersonalizedSearch = false
	sc := fetchCollectionRequest.Cards[0].SearchEvent.Sc
	searchCriteria.Lng = sc.To.Longitude
	searchCriteria.Lat = sc.To.Latitude
	searchCriteria.CheckIn = sc.FromDateStr.Str
	searchCriteria.CheckOut = sc.ToDateStr.Str
	searchCriteria.CountryCode = sc.To.CountryCode
	currency := c.GetHeader("currency")
	if len(currency) != 0 {
		searchCriteria.Currency = currency
	}
	searchCriteria.Limit = 5
	searchCriteria.LocationId = sc.To.Locus.City
	searchCriteria.RoomStayCandidates = setRoomStayCandidates(sc.Pax)
	searchCriteria = buildDefaultSearchContext(searchCriteria)
	for i := 0; i < len(fetchCollectionRequest.Cards); i++ {
		switch cardType := fetchCollectionRequest.Cards[i].CardId; cardType {
		case "LUXESELECTION":
			searchCriteria.CollectionCriteria.LuxeCardRequired = true
		case constants.BEST_DEALS:
			searchCriteria.CollectionCriteria.CollectionRequired = true
		case "STATICFILTERS":
			searchCriteria.CollectionCriteria.StaticFilterCardsRequired = true
			fetchCollectionRequest.Cards[i].SearchEvent = request.Card{}.SearchEvent
		case "PROPERTY":
			searchCriteria.CollectionCriteria.PropertyTypeCards = true
			fetchCollectionRequest.Cards[i].SearchEvent = request.Card{}.SearchEvent
		case "INSPIRED":
			searchCriteria.CollectionCriteria.InspiredCardsRequired = true
			fetchCollectionRequest.Cards[i].SearchEvent = request.Card{}.SearchEvent
		case "VALUESTAYS":
			searchCriteria.CollectionCriteria.ValueStayCardsRequired = true
			fetchCollectionRequest.Cards[i].SearchEvent = request.Card{}.SearchEvent
		case "OFFBEAT":
			searchCriteria.CollectionCriteria.OffbeatCitiesCardsRequired = true
			fetchCollectionRequest.Cards[i].SearchEvent = request.Card{}.SearchEvent
		}
	}
	searchCriteria.CollectionCriteria.AthenaCategory = "All"
	return searchCriteria
}

func setRoomStayCandidates(pax []request.Pax) []request.RoomStayCandidate {
	var roomStayCandidateList []request.RoomStayCandidate
	for i := 0; i < len(pax); i++ {
		var roomStayCandidate request.RoomStayCandidate
		currentPax := pax[i]
		roomStayCandidate.AdultCount = currentPax.Details.Adult.Count
		var childAges = make([]int, currentPax.Details.Child.Count)
		for j := 0; j < len(currentPax.Details.Child.Ages); j++ {
			childAges[j] = currentPax.Details.Child.Ages[j]
		}
		roomStayCandidate.ChildAges = childAges
		roomStayCandidateList = append(roomStayCandidateList, roomStayCandidate)

	}
	return roomStayCandidateList
}

func buildDefaultSearchContext(searchCriteria request.SearchCriteria) request.SearchCriteria {
	/* emperia will start sending dynamic search context from now (reqd for for HTL-38322) ,
	building sc with default configs in case SC received as null from emperia
	*/
	currentTime := time.Now()
	if len(searchCriteria.CheckIn) == 0 {
		checkinTime := currentTime.AddDate(0, 0, config.AppConfig.DefaultSearchContext.CheckinDateFromCurrent)
		searchCriteria.CheckIn = checkinTime.Format("2006-01-02")
	}
	if len(searchCriteria.CheckOut) == 0 {
		checkoutTime := currentTime.AddDate(0, 0, config.AppConfig.DefaultSearchContext.CheckoutDateFromCurrent)
		searchCriteria.CheckOut = checkoutTime.Format("2006-01-02")
	}
	if len(searchCriteria.LocationId) == 0 {
		searchCriteria.LocationId = config.AppConfig.DefaultSearchContext.LocationIdForFetchCollections
	}
	if len(searchCriteria.CountryCode) == 0 {
		searchCriteria.CountryCode = config.AppConfig.DefaultSearchContext.CountryIdForFetchCollections
	}
	if len(searchCriteria.LocationType) == 0 {
		searchCriteria.LocationType = config.AppConfig.DefaultSearchContext.LocationType
	}
	if (searchCriteria.RoomStayCandidates) == nil {
		var roomStayCandidateList []request.RoomStayCandidate
		var roomStayCandidate request.RoomStayCandidate
		roomStayCandidate.AdultCount = config.AppConfig.DefaultSearchContext.RoomStayCandidates
		searchCriteria.RoomStayCandidates = append(roomStayCandidateList, roomStayCandidate)
	}
	return searchCriteria
}

func updateCityFromLatLng(requestBO *models.RequestBO, locationData *request.LocationData) {
	if &locationData.DeviceCurrentLocation != nil && locationData.DeviceCurrentLocation.Lat != 0.0 && locationData.DeviceCurrentLocation.Lng != 0.0 {
		var getCityDetails = orchestrator.CityDetailsResponse{}
		getCityDetails = restconnectors.GetCityByLatLng(locationData.DeviceCurrentLocation.Lat, locationData.DeviceCurrentLocation.Lng)
		if getCityDetails.Success {
			requestBO.LocationName = getCityDetails.Response.CityCode
			requestBO.LocationType = "city"
			requestBO.CountryCode = getCityDetails.Response.CountryCode
		}

	}

}

func mapToCosmosResponse(reqBo *models.RequestBO, collectionResp *json.RawMessage, err error) response.HotelCosmosResponse {
	var resp response.HotelCosmosResponse
	if err != nil {
		logger.Error("Error in fetchCollections", err)
		resp = response.HotelCosmosResponse{Status: "ERROR", StatusDetails: "No Data Found for FetchCollections"}
	} else {
		resp = response.HotelCosmosResponse{Status: "OK"}
		resp.OfferData = make(map[string]response.HotelCosmosOfferData)
		resp.OfferData[reqBo.CardHeading] = response.HotelCosmosOfferData{Data: collectionResp, DataKey: strconv.Itoa(rand.Intn(100000))}
	}
	return resp
}

func updateReqBOForPageType(reqBO *models.RequestBO, pageType string) {
	switch strings.ToLower(pageType) {
	case "singlecollection":
		reqBO.PageContext = "HOMEPAGE"
		reqBO.CollectionRequired = true
		reqBO.CardHeading = "Recommended_Premium_Hotels"
	case "multicollection":
		reqBO.PageContext = "AltAccoLanding"
		reqBO.CollectionRequired = true
		reqBO.CardHeading = "Recommended_Premium_Homestays"
	case "trendingnow":
		reqBO.PageContext = "AltAccoLanding"
		reqBO.TrendingNow = true
		reqBO.CardHeading = "Trending_ALTH"
	default:
		logger.Infof("Invalid page type received from client: {}", pageType)
	}

}

// HandleCrossSellStreamRequest - handler to handle stream request
func HandleCrossSellStreamRequest(c *gin.Context) {
	var w http.ResponseWriter = c.Writer
	var r = c.Request

	var crossSellRequest request.CrossSellRequest
	_ = c.ShouldBindJSON(&crossSellRequest)

	requestJSON, err := json.Marshal(crossSellRequest)
	if err != nil {
		logger.Errorf("Error marshalling HandleCrossSellStreamRequest, request: %v", crossSellRequest)
	} else {
		logger.Infof("HandleCrossSellStreamRequest Request is %s", string(requestJSON))
	}

	cn, ok := w.(http.CloseNotifier)
	if !ok {
		http.NotFound(w, r)
		return
	}
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.NotFound(w, r)
		return
	}

	// Send the initial headers saying we're gonna stream the response.
	w.Header().Set("Transfer-Encoding", "chunked")
	w.Header().Set("Content-Type", "application/json")
	//w.Header().Set("Content-Encoding", "gzip")

	//flusher.Flush()

	//enc := json.NewEncoder(w)
	var ch chan clientgateway.CrossSellStreamResponse
	//var chCorp chan clientgateway.CrossSellStreamResponseForCorp
	var isProfileTypeCorp bool
	for _, sc := range crossSellRequest.SearchEvent {
		select {
		case <-cn.CloseNotify():
			logger.Infof("Client stopped listening")
			return
		default:
			se := sc
			user := crossSellRequest.User
			profileType := c.GetHeader("profileType")
			if len(profileType) != 0 && strings.EqualFold(profileType, "BUSINESS") {
				isProfileTypeCorp = true
			}
			//chCorp = make(chan clientgateway.CrossSellStreamResponseForCorp)
			ch = make(chan clientgateway.CrossSellStreamResponse)
			go services.GetCrossSellStreamResponse(&se, ch, c, crossSellRequest.Context.PageContext, crossSellRequest.Context.ExperimentData, crossSellRequest.SecureURL, crossSellRequest.ImageCount, crossSellRequest.NearBy, &user, isProfileTypeCorp, crossSellRequest.OfferRequired, crossSellRequest, se.CardID)

		}
	}

	w.WriteHeader(http.StatusOK)
	//flusher.Flush()
	/* gz := gzip.NewWriter(w)
	enc := json.NewEncoder(gz) */
	for _, sc := range crossSellRequest.SearchEvent {
		if isProfileTypeCorp {
			js, _ := json.Marshal(<-ch)
			w.Write(js)
		} else {
			js, _ := json.Marshal(<-ch)
			w.Write(js)
		}
		//fmt.Println(string(js))
		logger.Debugf("response for card id: %s", sc.CardID)
		// Send some data.
		/* err := enc.Encode(js)
		if err != nil {
			logger.Errorf("Error while sending the response to client: %v", err)
		} */

		flusher.Flush()
	}
}

func HandleCrossSellV2StreamRequest(c *gin.Context) {
	var w http.ResponseWriter = c.Writer
	var r = c.Request
	var crossSellRequest request.CrossSellRequest
	_ = c.ShouldBindJSON(&crossSellRequest)

	requestJSON, err := json.Marshal(crossSellRequest)
	if err != nil {
		logger.Errorf("Error marshalling HandleCrossSellV2StreamRequest, request: %v", crossSellRequest)
	} else {
		logger.Infof("HandleCrossSellV2StreamRequest Request is %s", string(requestJSON))
	}

	cn, ok := w.(http.CloseNotifier)
	if !ok {
		http.NotFound(w, r)
		return
	}
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.NotFound(w, r)
		return
	}

	w.Header().Set(constants.TRANSFER_ENCODING_KEY, constants.TRANSFER_ENCODING_VALUE_CHUNKED)
	w.Header().Set(constants.CONTENT_TYPE_KEY, constants.CONTENT_TYPE_VALUE)

	var ch chan clientgateway.CrossSellV2StreamResponse
	//var chCorp chan clientgateway.CrossSellStreamResponse
	var isProfileTypeCorp bool
	var channelList []chan clientgateway.CardV2
	var crossSellResp clientgateway.CrossSellV2StreamResponse
	for _, sc := range crossSellRequest.SearchEvent {
		select {
		case <-cn.CloseNotify():
			logger.Infof("Client stopped listening")
			return
		default:
			se := sc
			user := crossSellRequest.User
			profileType := c.GetHeader(constants.PROFILE_TYPE_KEY)
			if len(profileType) != 0 && strings.EqualFold(profileType, constants.BUSINESS_TEXT) {
				isProfileTypeCorp = true
			}

			ch = make(chan clientgateway.CrossSellV2StreamResponse)
			if strings.EqualFold(sc.CardID, constants.GREAT_VALUE_PACKAGES_V2) {
				se.LandingDiscoveryPage = true
				ch1 := make(chan clientgateway.CardV2)
				channelList = append(channelList, ch1)
				go services.GetLandingDiscoveryV2StreamResponse(&se, ch1, c, crossSellRequest.Context.PageContext, crossSellRequest.Context.ExperimentData,
					crossSellRequest.SecureURL, crossSellRequest.ImageCount, crossSellRequest.NearBy, &user, isProfileTypeCorp, crossSellRequest.OfferRequired, crossSellRequest.CorrelationKey, crossSellRequest, se.CardID)
			} else {
				if strings.EqualFold(sc.CardID, constants.HOTEL_XSELL_BENEFITS) || strings.EqualFold(sc.CardID, constants.HOTEL_EXTEND_YOUR_TRIP) || strings.EqualFold(sc.CardID, constants.INTL_CASHBACK_CARD) {
					go services.GetCrossSellV2StreamResponseWithRuleEngineSupport(&se, ch, c, crossSellRequest.Context.PageContext, crossSellRequest.Context.ExperimentData,
						crossSellRequest.SecureURL, crossSellRequest.ImageCount, crossSellRequest.NearBy, &user, isProfileTypeCorp, crossSellRequest.OfferRequired, crossSellRequest.CorrelationKey, crossSellRequest, se.CardID)
				} else {
					go services.GetCrossSellV2StreamResponse(&se, ch, c, crossSellRequest.Context.PageContext, crossSellRequest.Context.ExperimentData,
						crossSellRequest.SecureURL, crossSellRequest.ImageCount, crossSellRequest.NearBy, &user, isProfileTypeCorp, crossSellRequest.OfferRequired, crossSellRequest.CorrelationKey, crossSellRequest, se.CardID)
				}
			}

		}
	}
	for _, v := range channelList {
		var card clientgateway.CardV2
		card = <-v
		if card.CardDataV2.TabsData != nil {
			crossSellResp.CardsV2 = append(crossSellResp.CardsV2, card)
		}
	}

	w.WriteHeader(http.StatusOK)
	for _, sc := range crossSellRequest.SearchEvent {
		if strings.EqualFold(sc.CardID, constants.GREAT_VALUE_PACKAGES_V2) {
			js, _ := json.Marshal(crossSellResp)
			w.Write(js)
		} else {
			if isProfileTypeCorp {
				js, _ := json.Marshal(<-ch)
				w.Write(js)
			} else {
				js, _ := json.Marshal(<-ch)
				w.Write(js)
			}
		}
		logger.Debugf("response for card id: %s", sc.CardID)
		flusher.Flush()
	}
}

func LandingDiscoveryV2StreamRequest(c *gin.Context) {
	var w http.ResponseWriter = c.Writer
	var r = c.Request
	var crossSellRequest request.CrossSellRequest
	startTime := time.Now()
	_ = c.ShouldBindJSON(&crossSellRequest)

	requestJSON, err := json.Marshal(crossSellRequest)
	if err != nil {
		logger.Errorf("Error marshalling LandingDiscoveryV2StreamRequest, request: %v", crossSellRequest)
	} else {
		logger.Infof("LandingDiscoveryV2StreamRequest Request is %s", string(requestJSON))
	}

	cn, ok := w.(http.CloseNotifier)
	if !ok {
		http.NotFound(w, r)
		return
	}
	flusher, ok := w.(http.Flusher)
	if !ok {
		http.NotFound(w, r)
		return
	}
	zoneMap := config.AppConfig.CityToZoneMap
	newSearchEvent := crossSellRequest.SearchEvent
	for i := 0; i < len(crossSellRequest.SearchEvent); i++ {
		if strings.EqualFold(crossSellRequest.SearchEvent[i].CardID, constants.HANDPICKED_PROPERITES) {
			cityId := strings.ToLower(crossSellRequest.SearchEvent[i].SearchContext.To.Locus.Id)
			if _, ok := zoneMap[cityId]; !ok {
				newSearchEvent = removeIndex(crossSellRequest.SearchEvent, i)
				logger.Warnf("Removed HANDPICKED PROPERITES cards from the request, CityId : %s not present in the map configuration", cityId)
			}
		}
	}

	w.Header().Set(constants.TRANSFER_ENCODING_KEY, constants.TRANSFER_ENCODING_VALUE_CHUNKED)
	w.Header().Set(constants.CONTENT_TYPE_KEY, constants.CONTENT_TYPE_VALUE)

	var channelList []chan clientgateway.CardV2
	var isProfileTypeCorp bool
	var crossSellResp clientgateway.CrossSellV2StreamResponse
	for _, sc := range newSearchEvent {
		select {
		case <-cn.CloseNotify():
			logger.Infof("Client stopped listening")
			return
		default:
			se := sc
			//temporary Handled case: for android and ios landing page discovery page context issue,this has to be fixed at client side
			//ios: HOTELLANDING
			//android:HOTEL_LANDING
			se.LandingDiscoveryPage = true
			user := crossSellRequest.User
			ch := make(chan clientgateway.CardV2)
			channelList = append(channelList, ch)
			go services.GetLandingDiscoveryV2StreamResponse(&se, ch, c, crossSellRequest.Context.PageContext, crossSellRequest.Context.ExperimentData,
				crossSellRequest.SecureURL, crossSellRequest.ImageCount, crossSellRequest.NearBy, &user, isProfileTypeCorp, crossSellRequest.OfferRequired, crossSellRequest.CorrelationKey, crossSellRequest, se.CardID)
			logger.Debugf("response for card id: %s", sc.CardID)
		}
	}

	for _, v := range channelList {
		var card clientgateway.CardV2
		card = <-v
		if card.CardDataV2.TabsData != nil {
			crossSellResp.CardsV2 = append(crossSellResp.CardsV2, card)
		}
	}

	w.WriteHeader(http.StatusOK)
	if len(newSearchEvent) == 0 {
		crossSellResp.CardsV2 = nil
		js, _ := json.Marshal(crossSellResp)
		w.Write(js)
	} else {
		js, _ := json.Marshal(crossSellResp)
		w.Write(js)
	}
	logger.Infof("Total time taken to process Landing Discovery response for hotels is %v", time.Since(startTime))

	/*
		for _, sc := range crossSellRequest.SearchEvent {
			if isProfileTypeCorp {
				js, _ := json.Marshal(<-chCorp)
				w.Write(js)
			} else {
				js, _ := json.Marshal(<-ch)
				w.Write(js)
			}
			logger.Debugf("response for card id: %s", sc.CardID)
			flusher.Flush()
		}
	*/

	flusher.Flush()
}

func removeIndex(event []request.SearchEvent, index int) []request.SearchEvent {
	return append(event[:index], event[index+1:]...)
}
