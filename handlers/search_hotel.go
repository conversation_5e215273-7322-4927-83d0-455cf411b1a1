package handlers

import (
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/models/request"
	response "Hotels-Scion/models/response"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"math/rand"
	"net/http"
	"strconv"
)

//this is a proxy api for searchHotels
func SearchHotelsPersonalised(c *gin.Context) {

	var searchHotelRequest request.HotelCosmosSearchRequest
	_ = c.BindJSON(&searchHotelRequest)

	requestJSON, err := json.Marshal(searchHotelRequest)
	if err != nil {
		logger.Errorf("Error marshalling SearchHotelsPersonalised, request: %v", searchHotelRequest)
	} else {
		logger.Infof("SearchHotelsPersonalised Request is %s", string(requestJSON))
	}

	rawRequest, _ := json.Marshal(&searchHotelRequest.Hotel.SearchEvent)
	hotelData, err := restconnectors.PostSearchHotelsRequestGeneric(&rawRequest)

	searchHotelResponse := mapToCosmosFomat(hotelData, err)
	c.<PERSON>(http.StatusOK, searchHotelResponse)

}

func mapToCosmosFomat(hotelData *json.RawMessage, err error) response.SearchHotelResponse {
	result := hotelData
	if err != nil {
		var errResp json.RawMessage
		//errResp = []byte(err.Error())
		errResp, _ = json.Marshal(err.Error())
		result = &errResp
	}
	SearchHotelPersonalisedData := &response.SearchHotelPersonalisedData{
		Data: response.SeachHotelDataInner{
			Heading:    "Hotels For You",
			SubHeading: "Hotels Just for you",
			Data:       result,
		},
		DataKey: strconv.Itoa(rand.Intn(100000)),
	}
	searchHotelResponse := response.SearchHotelResponse{
		OfferData: response.OfferData{
			SearchHotelPersonalisedData: SearchHotelPersonalisedData,
		},
		Status:        "OK",
		StatusDetails: "",
	}
	return searchHotelResponse
}
