package request

type Common struct {
	PageType                        string        `json:"pageType"`
	DeviceType                      string        `json:"deviceType"`
	Channel                         string        `json:"channel"`
	CorrelationKey                  string        `json:"correlationKey"`
	UserType                        string        `json:"userType"`
	UserScope                       string        `json:"userScope"`
	IsCorporateUser                 bool          `json:"isCorporateUser"`
	IsShortlisted                   bool          `json:"isShortlisted"`
	IsBooked                        bool          `json:"isBooked"`
	IsAppLaunch                     bool          `json:"isAppLaunch"`
	IsWalletRefresh                 bool          `json:"isWalletRefresh"`
	IsLogin                         bool          `json:"isLogin"`
	Locus                           bool          `json:"locus"`
	Locationtype                    string        `json:"locationtype"`
	LocationName                    string        `json:"locationName"`
	CheckIn                         string        `json:"checkIn"`
	Checkout                        string        `json:"checkout"`
	AthenaCategory                  string        `json:"athenaCategory"`
	ShowWallet                      bool          `json:"showWallet"`
	IsClientPushNotificationEnabled bool          `json:"isClientPushNotificationEnabled"`
	HotelExperimentData             string        `json:"hotelExperimentData"`
	DeviceModel                     string        `json:"deviceModel"`
	RecentUserEvent                 []interface{} `json:"recentUserEvent"`
	Scope                           []string      `json:"scope"`
	PageContext                     string        `json:"pageContext"`
	BookingID                       string        `json:"bookingId"`
	BookingDevice                   string        `json:"bookingDevice"`
	LocationData					LocationData  `json:"locationData"`
	BookingDetails                  struct {
		AggregateBookingStatus string        `json:"aggregateBookingStatus"`
		BookingDateTime        string        `json:"bookingDateTime"`
		BookingID              string        `json:"bookingID"`
		BookingStatus          string        `json:"bookingStatus"`
		CoachDetails           []interface{} `json:"coachDetails"`
		FlightSegment          []struct {
			TotalPax            int    `json:"totalPax"`
			NoOfAdult           int    `json:"noOfAdult"`
			NoOfChild           int    `json:"noOfChild"`
			EndDateTime         string `json:"endDateTime"`
			StartDateTime       string `json:"startDateTime"`
			ProductCode         string `json:"productCode"`
			AirLineName         string `json:"airLineName"`
			AirLinePNR          string `json:"airLinePNR"`
			AirlineCode         string `json:"airlineCode"`
			Destination         string `json:"destination"`
			DestinationCityName string `json:"destinationCityName"`
			Origin              string `json:"origin"`
			OriginCityName      string `json:"originCityName"`
			Intent              string `json:"intent"`
		} `json:"flightSegment"`
		HotelDetails []interface{} `json:"hotelDetails"`
		MaxArrDate   string        `json:"maxArrDate"`
		MinDeptDate  string        `json:"minDeptDate"`
	} `json:"bookingDetails"`
	RequestType string `json:"requestType"`
	IDContext   string `json:"idContext"`
	User        struct {
		VisitorID string `json:"visitorId"`
		DeviceID  string `json:"deviceId"`
		Email     string `json:"email"`
	} `json:"user"`
	UniqueIds struct {
		CrossSellPersonalised []string `json:"Cross_Sell_Personalised"`
	} `json:"uniqueIds"`
}

type LocationData struct{
	DeviceCurrentLocation DeviceCurrentLocation `"deviceCurrentLocation"`
}
type DeviceCurrentLocation struct{
	Lat float32 `json:"lat"`
	Lng float32 `json:"lng"`
}
type HotelCosmosRequest struct {
	OfferQuery     []string `json:"offerQuery"`
	Common         Common   `json:"common"`
	BookingDetails interface {
	} `json:"bookingDetails"`
}
