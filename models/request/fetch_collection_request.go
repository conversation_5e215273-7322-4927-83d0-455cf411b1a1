package request

type FetchCollectionRequest struct {
	CorrelationKey string       `json:"correlationKey,omitempty"`
	Context        Context      `json:"context,omitempty"`
	Cards          []Card       `json:"cards,omitempty"`
	User           UserDetails  `json:"user,omitempty"`
	ImageDetails   ImageDetails `json:"imageDetails,omitempty"`
}

type Card struct {
	CardId        string `json:"cardId,omitempty"`
	CardVariantId string `json:"cardVariantId,omitempty"`
	TemplateId    string `json:"templateId,omitempty"`
	ComponentId   string `json:"componentId,omitempty"`
	SearchEvent   *struct {
		Sc SearchContext `json:"sc,omitempty"`
	} `json:"searchEvent,omitempty"`
	Data       CardData   `json:"data,omitempty"`
	HeaderData HeaderData `json:"headerData,omitempty"`
}

type UserDetails struct {
	MmtAuth     string            `json:"mmtAuth,omitempty"`
	Uuid        string            `json:"uuid,omitempty"`
	ProfileType string            `json:"profileType,omitempty"`
	VisitorId   string            `json:"visitorId,omitempty"`
	Mcid        string            `json:"mcid,omitempty"`
	State       string            `json:"state,omitempty"`
	Location    LocationDataOuter `json:"location,omitempty"`
	DeviceInfo  Device            `json:"deviceInfo,omitempty"`
}

type Device struct {
	Id       string `json:"id,omitempty"`
	Platform string `json:"platform,omitempty"`
	Model    string `json:"model,omitempty"`
	AppVer   string `json:"appVer,omitempty"`
}

type HeaderData struct {
	Header       string `json:"header,omitempty"`
	Subheader    string `json:"subheader,omitempty"`
	LobSubheader string `json:"lobSubheader,omitempty"`
	Cta          Cta    `json:"cta,omitempty"`
}

type Cta struct {
	Title    string `json:"title,omitempty"`
	Deeplink string `json:"deeplink,omitempty"`
}

type CardData struct {
	CardList         []CardInfo           `json:"cardList,omitempty"`
	CardInfo         DataCard             `json:"cardInfo,omitempty"`
	HotelList        []HotelCG            `json:"hotelList,omitempty"`
	AppliedFilterMap *map[string][]Filter `json:"appliedFilterMap,omitEmpty"`
	BgImageUrl       string               `json:"bgImageUrl,omitempty"`
	OfferPersuasions []OfferPersuasion    `json:"offerPersuasions,omitempty"`
}

// added new node CardInfo to send the cardData in case valuestays is requested
type DataCard struct {
	Index       int          `json:"index,omitempty"`
	StarText    string       `json:"starText,omitempty"`
	Id          string       `json:"CardId,omitempty"`
	TitleText   string       `json:"titleText,omitempty"`
	SubText     string       `json:"subText,omitempty"`
	CardAction  []CardAction `json:"cardAction,omitempty"`
	CardPayload CardPayload  `json:"cardPayload,omitempty"`
	IconUrl     string       `json:"iconUrl,omitempty"`
	BgImageUrl  string       `json:"bgImageUrl,omitempty"`
	TemplateId  string       `json:"templateId,omitempty"`
}

type CardAction struct {
	WebViewUrl string `json:"webViewUrl,omitempty"`
	Title      string `json:"title,omitempty"`
}

type CardPayload struct {
	GenericCardData []GenericCardData `json:"genericCardData,omitempty"`
}

type GenericCardData struct {
	TitleText string `json:"titleText,omitempty"`
	IconUrl   string `json:"iconUrl,omitempty"`
}

type OfferPersuasion struct {
	Text            string `json:"text,omitEmpty"`
	ImageUrl        string `json:"imageUrl,omitempty"`
	ExpiryTimestamp int64  `json:"expiryTimestamp,omitempty"`
}

type CardInfo struct {
	Description    string `json:"description,omitempty"`
	SubDescription string `json:"subDescription,omitempty"`
	ImageUrl       string `json:"imageUrl,omitempty"`
	DeepLink       string `json:"deepLink,omitempty"`
	ActionText     string `json:"actionText,omitempty"`
}

type Filter struct {
	FilterGroup   string `json:"filterGroup,omitempty"`
	FilterValue   string `json:"filterValue,omitempty"`
	Title         string `json:"title,omitempty"`
	IsRangeFilter bool   `json:"rangeFilter,omitempty"`
}

type HotelHES struct {
	Id                   string        `json:"id,omitempty"`
	Name                 string        `json:"name,omitempty"`
	PropertyType         string        `json:"propertyType,omitempty"`
	MainImages           []string      `json:"mainImages,omitempty"`
	StarRating           int32         `json:"starRating,omitempty"`
	CurrencyCode         interface{}   `json:"currencyCode,omitempty"`
	DisplayFare          interface{}   `json:"displayFare,omitempty"`
	CityName             string        `json:"cityName,omitempty"`
	DesktopDeeplink      string        `json:"desktopDeeplink,omitempty"`
	AppDeepLink          string        `json:"appDeepLink,omitempty"`
	LocationPersuasion   []string      `json:"locationPersuasion,omitempty"`
	FreeCancellationText string        `json:"freeCancellationText,omitempty"`
	Inclusions           []interface{} `json:"inclusions,omitempty"`
	CancellationTimeline interface{}   `json:"cancellationTimeline,omitempty"`
	FlyfishReviewSummary interface{}   `json:"flyfishReviewSummary,omitempty"`
}

type HotelCG struct {
	Id                   string        `json:"id,omitempty"`
	Name                 string        `json:"name,omitempty"`
	PropertyType         string        `json:"propertyType,omitempty"`
	MainImages           []string      `json:"mainImages,omitempty"`
	StarRating           int32         `json:"starRating,omitempty"`
	CurrencyCode         interface{}   `json:"currencyCode,omitempty"`
	DisplayFare          interface{}   `json:"displayFare,omitempty"`
	CityName             string        `json:"cityName,omitempty"`
	DesktopDeeplink      string        `json:"desktopDeeplink,omitempty"`
	AppDeepLink          string        `json:"appDeepLink,omitempty"`
	LocationPersuasion   []string      `json:"locationPersuasion,omitempty"`
	FreeCancellationText string        `json:"freeCancellationText,omitempty"`
	Inclusions           []interface{} `json:"inclusions,omitempty"`
	CancellationTimeline interface{}   `json:"cancellationTimeline,omitempty"`
	FlyfishReviewSummary interface{}   `json:"flyfishReviewSummary,omitempty"`
}
