package request

import (
	"Hotels-Scion/models/request/common"
	"encoding/json"
)

type AltAccoPropertyReq struct {
	OfferQuery []string      `json:"offerQuery"`
	Common     common.Common `json:"common"`
	Hotel      struct {
		SearchEvent json.RawMessage `json:"searchEvent"`
		Timestamp   int64           `json:"timestamp"`
	} `json:"hotel"`
}

type RoomStayCandidates []struct {
	GuestCounts []struct {
		AgeQualifyingCode string        `json:"ageQualifyingCode"`
		Ages              []interface{} `json:"ages"`
		Count             string        `json:"count"`
	} `json:"guestCounts"`
}
