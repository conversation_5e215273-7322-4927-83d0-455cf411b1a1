package request

import (
	"Hotels-Scion/models/common"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
)

// TrendValue is trend's values
type TrendValue struct {
	Value     string `json:"value,omitempty" structs:"-"`
	MinValue  int    `json:"minValue,omitempty" structs:"min_value"`
	MaxValue  int    `json:"maxValue,omitempty" structs:"max_value"`
	Timestamp int64  `json:"timestamp,omitempty"`
}

// RecommendedTrend is Trends recommended by Recommendation API
type RecommendedTrend struct {
	Area              []TrendValue `json:"areas,omitempty"`
	Price             []TrendValue `json:"price_bucket,omitempty"`
	ApBucket          []TrendValue `json:"ap_bucket,omitempty"`
	LosBucket         []TrendValue `json:"los_bucket,omitempty"`
	Pax               []TrendValue `json:"pax,omitempty"`
	HotelIDs          []TrendValue `json:"pd_id,omitempty"`
	StarRating        []TrendValue `json:"star_rating,omitempty"`
	AccommodationType []TrendValue `json:"accommodation_type,omitempty"`
	Cities            []TrendValue `json:"cities,omitempty"`
}

// Enrichments are recommended filters
type Enrichments struct {
	Trends         RecommendedTrend `json:"trends,omitempty"`
	UserPreference RecommendedTrend `json:"userPreferences,omitempty"`
}

// CrossSellRequest is new request for stream API
type CrossSellRequest struct {
	Brand          string                        `json:"brand,omitempty"`
	Context        Context                       `json:"context,omitempty"`
	SecureURL      string                        `json:"secureUrl,omitempty"`
	ImageCount     int                           `json:"imageCount,omitempty"`
	SearchEvent    []SearchEvent                 `json:"searchEvent,omitempty"`
	NearBy         bool                          `json:"nearBy,omitempty"`
	OfferRequired  bool                          `json:"offerRequired,omitempty"`
	User           User                          `json:"user,omitempty"`
	ImageCategory  []clientbackend.ImageCategory `json:"imageCategories,omitempty"`
	CorrelationKey string                        `json:"correlationKey,omitempty"`
	MetaInfo       MetaInfo                      `json:"metaInfo,omitempty"`
}

type User struct {
	Location LocationDataOuter `json:"location,omitempty"`
}

type LocationDataOuter struct {
	LocationData LocationDataBO `json:"locationData,omitempty"`
}

type LocationDataBO struct {
	DeviceCurrentLocation DeviceCurrentLocation `json:"currentLocation,omitempty"`
}

type Context struct {
	ContextId      string `json:"contextId,omitempty"`
	ExperimentData string `json:"experimentData,omitempty"`
	PageContext    string `json:"pageContext,omitempty"`
	Scope          string `json:"scope,omitempty"`
	FunnelSource   string `json:"funnelSource,omitempty"`
}

type MetaInfo struct {
	UserName  string `json:"userName,omitempty"`
	BookingId string `json:"bookingId,omitempty"`
}

type SearchEvent struct {
	CardID                 string                                   `json:"cardId,omitempty"`
	TemplateID             string                                   `json:"templateId,omitempty"`
	ComponentID            string                                   `json:"componentId,omitempty"`
	Limit                  int                                      `json:"limit,omitempty"`
	IsFilter               bool                                     `json:"isFilter,omitempty"`
	Priority               int                                      `json:"priority,omitempty"`
	HydraSegments          []string                                 `json:"hydraSegIds,omitempty"`
	Meta                   map[string]string                        `json:"meta,omitempty"`
	SearchContext          SearchContext                            `json:"sc,omitempty"`
	SelectedTabId          string                                   `json:"selectedTabId,omitempty"`
	AppliedFilterMap       *map[common.Filter_Group][]common.Filter `json:"appliedFilterMap,omitEmpty"`
	FunnelActivity         map[string]FunnelActivityValue           `json:"funnelActivity,omitempty"`
	Enrichments            Enrichments                              `json:"enrichments,omitempty"`
	Tags                   Tags                                     `json:"tags,omitempty"`
	FilterList             []clientgateway.Filter                   `json:"filterList,omitempty"`
	Filter                 bool                                     `json:"filter,omitempty"`
	PersuasionRequired     bool                                     `json:"persuasionRequired,omitempty"`
	RecentlyViewedHotelIDs []TrendValue                             `json:"recentlyViewedHotels,omitempty"`
	LandingDiscoveryPage   bool                                     `json:"isLandingDiscoveryPage"`
	RecommendedPlaces      []common.RecommendedPlaces               `json:"recommendedPlaces,omitempty"`
}

// SearchContext has all the info regarding a search
type SearchContext struct {
	Lob                string      `json:"lob,omitempty"`
	LobCategory        string      `json:"lobCategory,omitempty"`
	FromDateStr        Date        `json:"fromDateTime,omitempty"`
	ToDateStr          Date        `json:"toDateTime,omitempty"`
	Pax                []Pax       `json:"pax,omitempty"`
	From               CityDetails `json:"from,omitempty"`
	To                 CityDetails `json:"to,omitempty"`
	Timestamp          int64       `json:"timestamp,omitempty"`
	Rooms              int64       `json:"rooms,omitempty"`
	FunnelSource       string      `json:"funnelSource,omitempty"`
	PersonalizedSearch bool        `json:"personalizedSearch,omitempty"`
	Product            Product     `json:"product,omitempty"`
	Meta               *Meta       `json:"meta,omitempty"`
}
type Product struct {
	ID   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
}
type Date struct {
	Str  string `json:"str,omitempty"`
	TS   int64  `json:"ts,omitempty"`
	Zone string `json:"zone,omitempty"`
}

type Meta struct {
	GoDataString string `json:"gd,omitempty"`
}

type CityDetails struct {
	LobCity     string  `json:"lobCity,omitempty"`
	Locus       Locus   `json:"locus,omitempty"`
	CityName    string  `json:"cityName,omitempty"`
	CountryName string  `json:"countryName,omitempty"`
	CountryCode string  `json:"countryCode,omitempty"`
	Longitude   float32 `json:"longitude,omitempty"`
	Latitude    float32 `json:"latitude,omitempty"`
}

type Locus struct {
	City     string `json:"city,omitempty"`
	AreaId   string `json:"areaId,omitempty"`
	AreaName string `json:"areaName,omitempty"`
	PoiId    string `json:"poiId,omitempty"`
	PoiName  string `json:"poiName,omitempty"`
	Type     string `json:"type,omitempty"`
	Id       string `json:"id,omitempty"`
	CityName string `json:"cityName,omitempty"`
}

type Pax struct {
	Count   int        `json:"count,omitempty"`
	Details PaxDetails `json:"details,omitempty"`
}

type PaxDetails struct {
	Adult  PaxCountDetails `json:"adult,omitempty"`
	Child  PaxCountDetails `json:"child,omitempty"`
	Infant PaxCountDetails `json:"infant,omitempty"`
}

type PaxCountDetails struct {
	Ages  []int `json:"ages,omitempty"`
	Count int   `json:"count,omitempty"`
}

type Tags struct {
	EdgeType string `json:"edge_type,omitempty"`
	Source   string `json:"source,omitempty"`
}

type FunnelActivityValue struct {
	FunnelCounts FunnelCounts `json:"funnelCounts,omitempty"`
	LastHitTs    int64        `json:"lastHitTs,omitempty"`
}

type FunnelCounts struct {
	Pageviews         Pageviews         `json:"pageviews,omitempty"`
	ProductTypeCounts ProductTypeCounts `json:"productTypeCounts,omitempty"`
}

type ProductTypeCounts struct {
	IsAltaccoFalse int `json:"isAltaccoFalse,omitempty"`
	IsAltaccoTrue  int `json:"isAltaccoTrue,omitempty"`
}

type Pageviews struct {
	Detail  int `json:"detail,omitempty"`
	Listing int `json:"listing,omitempty"`
}

type GoData struct {
	Qd         string     `json:"qd,omitempty"`
	Vcid       string     `json:"vcid,omitempty"`
	Vhid       string     `json:"vhid,omitempty"`
	Hn         string     `json:"hn,omitempty"`
	Cn         string     `json:"cn,omitempty"`
	N          string     `json:"n,omitempty"`
	Checkin    string     `json:"checkin,omitempty"`
	Checkout   string     `json:"checkout,omitempty"`
	RoomString string     `json:"roomString,omitempty"`
	T          string     `json:"t,omitempty"`
	Cc         string     `json:"cc,omitempty"`
	Funnel     string     `json:"funnel,omitempty"`
	LocusData  *LocusData `json:"locusData,omitempty"`
}

type Matchmaker struct {
	Id   string `json:"id,omitempty"`
	Name string `json:"name,omitempty"`
	Type string `json:"type,omitempty"`
}

type LocusData struct {
	CityCode    string       `json:"cityCode,omitempty"`
	CountryCode string       `json:"countryCode,omitempty"`
	Id          string       `json:"id,omitempty"`
	Matchmaker  []Matchmaker `json:"matchmaker,omitempty"`
}
