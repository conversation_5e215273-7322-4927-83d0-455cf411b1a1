package common

type SearchEvent struct {
	AdvancedFiltering bool   `json:"advancedFiltering"`
	AppVersion        string `json:"appVersion"`
	BookingDevice     string `json:"bookingDevice"`
	Checkin           string `json:"checkin"`
	Checkout          string `json:"checkout"`
	CohertVar         struct {
		Apps       []interface{} `json:"apps"`
		IsLoggedIn bool          `json:"isLoggedIn"`
	} `json:"cohertVar"`
	CollectionRequired    bool   `json:"collectionRequired"`
	CountryCode           string `json:"countryCode"`
	CityCode			  string `json:"cityCode"`
	Currency              string `json:"currency"`
	DeviceID              string `json:"deviceId"`
	DeviceType            string `json:"deviceType"`
	ExperimentData        string `json:"experimentData"`
	FirstTimeUserState    int    `json:"firstTimeUserState"`
	FlyfishSummaryRequest struct {
		Filter struct {
			Otas []string `json:"otas"`
		} `json:"filter"`
	} `json:"flyfishSummaryRequest"`
	FunnelSource  string `json:"funnelSource"`
	IDContext     string `json:"idContext"`
	ImageCategory []struct {
		Category        string `json:"category"`
		Count           int    `json:"count"`
		Height          int    `json:"height"`
		ImageFormatType string `json:"imageFormatType"`
		Width           int    `json:"width"`
	} `json:"imageCategory"`
	ImageType           []string `json:"imageType"`
	LastViewedTimeStamp int      `json:"lastViewedTimeStamp"`
	Limit               int      `json:"limit"`
	LocationID          string   `json:"locationId"`
	LocationType        string   `json:"locationType"`
	MatchMakerRequest   struct {
		ShowHotel bool `json:"showHotel"`
	} `json:"matchMakerRequest"`
	MobileNumber        string   `json:"mobileNumber"`
	NetworkType         string   `json:"networkType"`
	NumberOfAddons      int      `json:"numberOfAddons"`
	NumberOfCoupons     int      `json:"numberOfCoupons"`
	NumberOfSoldOuts    int      `json:"numberOfSoldOuts"`
	PEmailID            string   `json:"pEmailId"`
	PageContext         string   `json:"pageContext"`
	RecentlyViewedIds   []string `json:"recentlyViewedIds"`
	RequestType         string   `json:"requestType"`
	Resolution          string   `json:"resolution"`
	ResponseFilterFlags struct {
		AddOnRequired           bool   `json:"addOnRequired"`
		ApplyAbsorption         bool   `json:"applyAbsorption"`
		BestCoupon              bool   `json:"bestCoupon"`
		CheckAvailibility       bool   `json:"checkAvailibility"`
		DealOfTheDayRequired    bool   `json:"dealOfTheDayRequired"`
		FlyfishSummaryRequired  bool   `json:"flyfishSummaryRequired"`
		FreeCancellationAvail   bool   `json:"freeCancellationAvail"`
		MmtPrime                bool   `json:"mmtPrime"`
		PersuasionSeg           string `json:"persuasionSeg"`
		PriceInfoReq            bool   `json:"priceInfoReq"`
		RoomLevelDetails        bool   `json:"roomLevelDetails"`
		ShortlistRequired       bool   `json:"shortlistRequired"`
		SoldOutInfoReq          bool   `json:"soldOutInfoReq"`
		SortPersuasion          bool   `json:"sortPersuasion"`
		StaticData              bool   `json:"staticData"`
		TopRatedCommentRequired bool   `json:"topRatedCommentRequired"`
		UnmodifiedAmenities     bool   `json:"unmodifiedAmenities"`
		WalletRequired          bool   `json:"walletRequired"`
	} `json:"responseFilterFlags"`
	RoomStayCandidates []struct {
		GuestCounts []struct {
			AgeQualifyingCode string        `json:"ageQualifyingCode"`
			Ages              []interface{} `json:"ages"`
			Count             string        `json:"count"`
		} `json:"guestCounts"`
	} `json:"roomStayCandidates"`
	TrendingNow bool   `json:"trendingNow"`
	VisitNumber string `json:"visitNumber"`
	VisitorID   string `json:"visitorId"`
}
