package request

type MobLandingRequest struct {
	DeviceDetails  DeviceDetails     `json:"deviceDetails,omitempty"`
	ExpData        string            `json:"expData,omitempty"`
	RequestDetails RequestDetails    `json:"requestDetails,omitempty"`
	SearchCriteria MobSearchCriteria `json:"searchCriteria,omitempty"`
	RequiredApis   RequiredApis      `json:"requiredApis,omitempty"`
	FeatureFlags   FeatureFlags      `json:"featureFlags,omitempty"`
	CorrelationKey string            `json:"correlationKey,omitempty"`
}

type MobSearchCriteria struct {
	CheckIn            string                 `json:"checkIn,omitempty"`
	CheckOut           string                 `json:"checkOut,omitempty"`
	CountryCode        string                 `json:"countryCode,omitempty"`
	CityCode           string                 `json:"cityCode,omitempty"`
	LocationId         string                 `json:"locationId,omitempty"`
	LocationType       string                 `json:"locationType,omitempty"`
	Currency           string                 `json:"currency,omitempty"`
	Limit              int                    `json:"limit,omitempty"`
	PersonalizedSearch bool                   `json:"personalizedSearch,omitempty"`
	NearBySearch       bool                   `json:"nearBySearch,omitempty"`
	RoomStayCandidates []MobRoomStayCandidate `json:"roomStayCandidates,omitempty"`
	CollectionCriteria CollectionCriteria     `json:"collectionCriteria,omitempty"`
	Language           string                 `json:"language,omitempty"`
	VcId               string                 `json:"vcId,omitempty"`
}

type MobRoomStayCandidate struct {
	AdultCount int   `json:"adultCount,omitempty"`
	Rooms      int   `json:"rooms,omitempty"`
	ChildAges  []int `json:"childAges,omitempty"`
}

type RequiredApis struct {
	CardRequired bool `json:"cardRequired,omitempty"`
}
