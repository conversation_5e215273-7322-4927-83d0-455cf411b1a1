package request

type FetchCollectionRequestCG struct {
	DeviceDetails  DeviceDetails  `json:"deviceDetails,omitempty"`
	RequestDetails RequestDetails `json:"requestDetails,omitempty"`
	FeatureFlags   FeatureFlags   `json:"featureFlags,omitempty"`
	SearchCriteria SearchCriteria `json:"searchCriteria,omitempty"`
	ExpData        string         `json:"expData,omitempty"`
	CorrelationKey string         `json:"CorrelationKey,omitempty"`
	ImageDetails   ImageDetails   `json:"imageDetails,omitempty"`
}

type DeviceDetails struct {
	AppVersion    string `json:"appVersion,omitempty"`
	DeviceId      string `json:"deviceId,omitempty"`
	DeviceType    string `json:"deviceType,omitempty"`
	BookingDevice string `json:"bookingDevice,omitempty"`
	NetworkType   string `json:"networkType,omitempty"`
}

type RequestDetails struct {
	SrLat        float32  `json:"srLat,omitempty"`
	SrLng        float32  `json:"srLng,omitempty"`
	FunnelSource string   `json:"funnelSource,omitempty"`
	PageContext  string   `json:"pageContext,omitempty"`
	Uuid         string   `json:"uuid,omitempty"`
	VisitorId    string   `json:"visitorId,omitempty"`
	VisitNumber  int      `json:"visitNumber,omitempty"`
	IdContext    string   `json:"idContext,omitempty"`
	Requestor    string   `json:"requestor,omitempty"`
	Region       string   `json:"region,omitempty"`
	LoggedIn     bool     `json:"loggedIn,omitempty"`
	Profile      string   `json:"profile,omitempty"`
	Brand        string   `json:"brand,omitempty"`
	RequestId    string   `json:"requestId,omitempty"`
	Channel      string   `json:"channel,omitempty"`
	CardIds      []string `json:"cardIds,omitempty"`
	Premium      bool     `json:"premium,omitempty"`
}

type ImageDetails struct {
	Types      []string        `json:"types,omitempty"`
	Categories []ImageCategory `json:"categories,omitempty"`
}

type ImageCategory struct {
	Type        string  `json:"type,omitempty"`
	Count       int32   `json:"count,omitempty"`
	Height      float32 `json:"height,omitempty"`
	Width       float32 `json:"width,omitempty"`
	ImageFormat string  `json:"imageFormat,omitempty"`
}

type FeatureFlags struct {
	Locus      bool `json:"locus,omitempty"`
	BestCoupon bool `json:"bestCoupon,omitempty"`
	Coupon     bool `json:"coupon,omitempty"`
}

type SearchCriteria struct {
	CheckIn            string              `json:"checkIn,omitempty"`
	CheckOut           string              `json:"checkOut,omitempty"`
	CountryCode        string              `json:"countryCode,omitempty"`
	CityCode           string              `json:"cityCode,omitempty"`
	LocationId         string              `json:"locationId,omitempty"`
	LocationType       string              `json:"locationType,omitempty"`
	Lat                float32             `json:"lat,omitempty"`
	Lng                float32             `json:"lng,omitempty"`
	Currency           string              `json:"currency,omitempty"`
	Limit              int                 `json:"limit,omitempty"`
	PersonalizedSearch bool                `json:"personalizedSearch,omitempty"`
	NearBySearch       bool                `json:"nearBySearch,omitempty"`
	RoomStayCandidates []RoomStayCandidate `json:"roomStayCandidates,omitempty"`
	CollectionCriteria CollectionCriteria  `json:"collectionCriteria,omitempty"`
	Language           string              `json:"language,omitempty"`
	VcId               string              `json:"vcId,omitempty"`
}

type CollectionCriteria struct {
	LuxeCardRequired           bool   `json:"luxeCardRequired,omitempty"`
	PropertyTypeCards          bool   `json:"propertyTypeCards,omitempty"`
	StaticFilterCardsRequired  bool   `json:"staticFilterCardsRequired,omitempty"`
	InspiredCardsRequired      bool   `json:"inspiredCardsRequired,omitempty"`
	ValueStayCardsRequired     bool   `json:"valueStayCardsRequired,omitempty"` //if value stays is requested we will send this flag as true
	OffbeatCitiesCardsRequired bool   `json:"offbeatCitiesCardsRequired,omitempty"`
	CollectionRequired         bool   `json:"collectionRequired,omitempty"`
	AthenaCategory             string `json:"athenaCategory,omitempty"`
}

type RoomStayCandidate struct {
	AdultCount int   `json:"adultCount,omitempty"`
	ChildAges  []int `json:"childAges,omitempty"`
}
