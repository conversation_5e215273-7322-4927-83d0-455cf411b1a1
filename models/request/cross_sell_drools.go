package request

//CrossSellRequest is new request for stream API
type CrossSellDrools struct {
	AccomodationType        []TrendValue `json:"accomodationType,omitempty"`
	StarRating              []TrendValue `json:"starRating,omitempty"`
	Price                   []TrendValue `json:"price,omitempty"`
	Area                    []TrendValue `json:"area,omitempty"`
	Priority                int          `json:"priority,omitempty"`
	DomIntl                 string       `json:"domIntl,omitempty"`
	Destination             string       `json:"destination,omitempty"`
	HydraSegment            string       `json:"hydraSegment,omitempty"`
	LOS                     int          `json:"los,omitempty"`
	AdultCount              int          `json:"adultCount,omitempty"`
	NoOfChildren            int          `json:"noOfChildren,omitempty"`
	AdvancedPurchase        int          `json:"advancedPurchase,omitempty"`
	AdditionalFilters       string       `json:"additionalFilters,omitempty"`
	Heading                 string       `json:"heading,omitempty"`
	SubHeading              string       `json:"subHeading,omitempty"`
	UpdatedAccomodationType []TrendValue `json:"UpdatedAccomodationType,omitempty"`
	UpdatedStarRating       []TrendValue `json:"UpdatedStarRating,omitempty"`
	UpdatedPrice            []TrendValue `json:"UpdatedPrice,omitempty"`
	UpdatedArea             []TrendValue `json:"UpdatedArea,omitempty"`
	RuleID                  string       `json:"ruleId,omitempty"`
	DefaultHeading          string       `json:"defaultHeading,omitempty"`
	DefaultSubHeading       string       `json:"defaultSubHeading,omitempty"`
	IdContext       		string       `json:"idContext,omitempty"`
	SiteDomain       		string       `json:"siteDomain,omitempty"`
	NearBy       			bool       	 `json:"nearBy,omitempty"`

}
