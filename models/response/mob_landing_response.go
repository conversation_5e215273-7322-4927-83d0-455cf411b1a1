package response

type MobLandingResponse struct {
	Response MobLandingData `json:"response,omitempty"`
}

func (a *MobLandingResponse) GetResponse() MobLandingData {
	return a.Response
}

func (a *MobLandingResponse) SetResponse(response MobLandingData) {
	a.Response = response
}

type Media struct {
	URL       string `json:"url,omitempty"`
	MediaType string `json:"mediaType,omitempty"`
}

func (m *Media) GetURL() string {
	return m.URL
}

func (m *Media) SetURL(url string) {
	m.URL = url
}

func (m *Media) GetMediaType() string {
	return m.MediaType
}

func (m *Media) SetMediaType(mediaType string) {
	m.MediaType = mediaType
}

type Coupon struct {
	Code                string  `json:"code,omitempty"`
	Description         string  `json:"description,omitempty"`
	SpecialPromo        bool    `json:"specialPromo,omitempty"`
	Type                string  `json:"type,omitempty"`
	CouponAmount        float64 `json:"couponAmount,omitempty"`
	AutoApplicable      bool    `json:"autoApplicable,omitempty"`
	BnplAllowed         bool    `json:"bnplAllowed,omitempty"`
	Disabled            bool    `json:"disabled,omitempty"`
	BankOffer           bool    `json:"bankOffer,omitempty"`
	NoCostEmiApplicable bool    `json:"noCostEmiApplicable,omitempty"`
}

func (c *Coupon) GetCode() string {
	return c.Code
}

func (c *Coupon) SetCode(code string) {
	c.Code = code
}

func (c *Coupon) GetDescription() string {
	return c.Description
}

func (c *Coupon) SetDescription(description string) {
	c.Description = description
}

func (c *Coupon) GetSpecialPromo() bool {
	return c.SpecialPromo
}

func (c *Coupon) SetSpecialPromo(specialPromo bool) {
	c.SpecialPromo = specialPromo
}

func (c *Coupon) GetType() string {
	return c.Type
}

func (c *Coupon) SetType(typ string) {
	c.Type = typ
}

func (c *Coupon) GetCouponAmount() float64 {
	return c.CouponAmount
}

func (c *Coupon) SetCouponAmount(couponAmount float64) {
	c.CouponAmount = couponAmount
}

func (c *Coupon) GetAutoApplicable() bool {
	return c.AutoApplicable
}

func (c *Coupon) SetAutoApplicable(autoApplicable bool) {
	c.AutoApplicable = autoApplicable
}

func (c *Coupon) GetBnplAllowed() bool {
	return c.BnplAllowed
}

func (c *Coupon) SetBnplAllowed(bnplAllowed bool) {
	c.BnplAllowed = bnplAllowed
}

func (c *Coupon) GetDisabled() bool {
	return c.Disabled
}

func (c *Coupon) SetDisabled(disabled bool) {
	c.Disabled = disabled
}

func (c *Coupon) GetBankOffer() bool {
	return c.BankOffer
}

func (c *Coupon) SetBankOffer(bankOffer bool) {
	c.BankOffer = bankOffer
}

func (c *Coupon) GetNoCostEmiApplicable() bool {
	return c.NoCostEmiApplicable
}

func (c *Coupon) SetNoCostEmiApplicable(noCostEmiApplicable bool) {
	c.NoCostEmiApplicable = noCostEmiApplicable
}

type PriceDetail struct {
	Price                  float64 `json:"price,omitempty"`
	PriceWithTax           float64 `json:"priceWithTax,omitempty"`
	DiscountedPrice        float64 `json:"discountedPrice,omitempty"`
	DiffPercentage         float64 `json:"diffPercentage,omitempty"`
	DiffPercentageWithTax  float64 `json:"diffPercentageWithTax,omitempty"`
	DiscountedPriceWithTax float64 `json:"discountedPriceWithTax,omitempty"`
	TotalTax               float64 `json:"totalTax,omitempty"`
	Coupon                 Coupon  `json:"coupon,omitempty"`
}

func (p *PriceDetail) GetPrice() float64 {
	return p.Price
}

func (p *PriceDetail) SetPrice(price float64) {
	p.Price = price
}

func (p *PriceDetail) GetPriceWithTax() float64 {
	return p.PriceWithTax
}

func (p *PriceDetail) SetPriceWithTax(priceWithTax float64) {
	p.PriceWithTax = priceWithTax
}

func (p *PriceDetail) GetDiscountedPrice() float64 {
	return p.DiscountedPrice
}

func (p *PriceDetail) SetDiscountedPrice(discountedPrice float64) {
	p.DiscountedPrice = discountedPrice
}

func (p *PriceDetail) GetDiscountedPriceWithTax() float64 {
	return p.DiscountedPriceWithTax
}

func (p *PriceDetail) SetDiscountedPriceWithTax(discountedPriceWithTax float64) {
	p.DiscountedPriceWithTax = discountedPriceWithTax
}

func (p *PriceDetail) GetTotalTax() float64 {
	return p.TotalTax
}

func (p *PriceDetail) SetTotalTax(totalTax float64) {
	p.TotalTax = totalTax
}

func (p *PriceDetail) GetCoupon() Coupon {
	return p.Coupon
}

func (p *PriceDetail) SetCoupon(coupon Coupon) {
	p.Coupon = coupon
}

type Style struct {
	TextColor string `json:"textColor,omitempty"`
}

func (s *Style) GetTextColor() string {
	return s.TextColor
}

func (s *Style) SetTextColor(textColor string) {
	s.TextColor = textColor
}

type Data struct {
	ID             string `json:"id,omitempty"`
	PersuasionType string `json:"persuasionType,omitempty"`
	Iconurl        string `json:"iconurl,omitempty"`
	Text           string `json:"text,omitempty"`
	HasAction      bool   `json:"hasAction,omitempty"`
	Icontype       string `json:"icontype,omitempty"`
	Style          Style  `json:"style,omitempty"`
	PersuasionKey  string `json:"persuasionKey,omitempty"`
	Horizontal     bool   `json:"horizontal,omitempty"`
	HTML           bool   `json:"html,omitempty"`
}

func (d *Data) GetID() string {
	return d.ID
}

func (d *Data) SetID(id string) {
	d.ID = id
}

func (d *Data) GetPersuasionType() string {
	return d.PersuasionType
}

func (d *Data) SetPersuasionType(persuasionType string) {
	d.PersuasionType = persuasionType
}

func (d *Data) GetIconurl() string {
	return d.Iconurl
}

func (d *Data) SetIconurl(iconurl string) {
	d.Iconurl = iconurl
}

func (d *Data) GetText() string {
	return d.Text
}

func (d *Data) SetText(text string) {
	d.Text = text
}

func (d *Data) GetHasAction() bool {
	return d.HasAction
}

func (d *Data) SetHasAction(hasAction bool) {
	d.HasAction = hasAction
}

func (d *Data) GetIcontype() string {
	return d.Icontype
}

func (d *Data) SetIcontype(icontype string) {
	d.Icontype = icontype
}

func (d *Data) GetStyle() Style {
	return d.Style
}

func (d *Data) SetStyle(style Style) {
	d.Style = style
}

func (d *Data) GetPersuasionKey() string {
	return d.PersuasionKey
}

func (d *Data) SetPersuasionKey(persuasionKey string) {
	d.PersuasionKey = persuasionKey
}

func (d *Data) GetHorizontal() bool {
	return d.Horizontal
}

func (d *Data) SetHorizontal(horizontal bool) {
	d.Horizontal = horizontal
}

func (d *Data) GetHTML() bool {
	return d.HTML
}

func (d *Data) SetHTML(html bool) {
	d.HTML = html
}

type CardLeftPane10 struct {
	Data         []Data `json:"data,omitempty"`
	Placeholder  string `json:"placeholder,omitempty"`
	Template     string `json:"template,omitempty"`
	TemplateType string `json:"templateType,omitempty"`
}

func (c *CardLeftPane10) GetData() []Data {
	return c.Data
}

func (c *CardLeftPane10) SetData(data []Data) {
	c.Data = data
}

func (c *CardLeftPane10) GetPlaceholder() string {
	return c.Placeholder
}

func (c *CardLeftPane10) SetPlaceholder(placeholder string) {
	c.Placeholder = placeholder
}

func (c *CardLeftPane10) GetTemplate() string {
	return c.Template
}

func (c *CardLeftPane10) SetTemplate(template string) {
	c.Template = template
}

func (c *CardLeftPane10) GetTemplateType() string {
	return c.TemplateType
}

func (c *CardLeftPane10) SetTemplateType(templateType string) {
	c.TemplateType = templateType
}

type LocationDetail struct {
	ID          string `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Type        string `json:"type,omitempty"`
	CountryID   string `json:"countryId,omitempty"`
	CountryName string `json:"countryName,omitempty"`
}

func (l *LocationDetail) GetID() string {
	return l.ID
}

func (l *LocationDetail) SetID(id string) {
	l.ID = id
}

func (l *LocationDetail) GetName() string {
	return l.Name
}

func (l *LocationDetail) SetName(name string) {
	l.Name = name
}

func (l *LocationDetail) GetType() string {
	return l.Type
}

func (l *LocationDetail) SetType(typ string) {
	l.Type = typ
}

func (l *LocationDetail) GetCountryID() string {
	return l.CountryID
}

func (l *LocationDetail) SetCountryID(countryID string) {
	l.CountryID = countryID
}

func (l *LocationDetail) GetCountryName() string {
	return l.CountryName
}

func (l *LocationDetail) SetCountryName(countryName string) {
	l.CountryName = countryName
}

type HotelPersuasions struct {
	CardLeftPane10 CardLeftPane10 `json:"CARD_LEFT_PANE_10,omitempty"`
	LocationDetail LocationDetail `json:"locationDetail,omitempty"`
	AppDeeplink    string         `json:"appDeeplink,omitempty"`
	SeoURL         string         `json:"seoUrl,omitempty"`
}

func (h *HotelPersuasions) GetCardLeftPane10() CardLeftPane10 {
	return h.CardLeftPane10
}

func (h *HotelPersuasions) SetCardLeftPane10(cardLeftPane10 CardLeftPane10) {
	h.CardLeftPane10 = cardLeftPane10
}

func (h *HotelPersuasions) GetLocationDetail() LocationDetail {
	return h.LocationDetail
}

func (h *HotelPersuasions) SetLocationDetail(locationDetail LocationDetail) {
	h.LocationDetail = locationDetail
}

func (h *HotelPersuasions) GetAppDeeplink() string {
	return h.AppDeeplink
}

func (h *HotelPersuasions) SetAppDeeplink(appDeeplink string) {
	h.AppDeeplink = appDeeplink
}

func (h *HotelPersuasions) GetSeoURL() string {
	return h.SeoURL
}

func (h *HotelPersuasions) SetSeoURL(seoURL string) {
	h.SeoURL = seoURL
}

type HotelListNew struct {
	ID               string      `json:"id,omitempty"`
	GiID             string      `json:"giId,omitempty"`
	Name             string      `json:"name,omitempty"`
	MtKey            string      `json:"mtKey,omitempty"`
	Media            []Media     `json:"media,omitempty"`
	PriceDetail      PriceDetail `json:"priceDetail,omitempty"`
	AppDeeplink      string      `json:"appDeeplink"`
	HotelPersuasions interface{} `json:"hotelPersuasions"`
	Tg               int64       `json:"tg,omitempty"`
	Gd               string      `json:"gd,omitempty"`
	CityName         string      `json:"-"`
}

func (h *HotelListNew) GetID() string {
	return h.ID
}

func (h *HotelListNew) SetID(id string) {
	h.ID = id
}

func (h *HotelListNew) GetGiID() string {
	return h.GiID
}

func (h *HotelListNew) SetGiID(giID string) {
	h.GiID = giID
}

func (h *HotelListNew) GetName() string {
	return h.Name
}

func (h *HotelListNew) SetName(name string) {
	h.Name = name
}

func (h *HotelListNew) GetMtKey() string {
	return h.MtKey
}

func (h *HotelListNew) SetMtKey(mtKey string) {
	h.MtKey = mtKey
}

func (h *HotelListNew) GetMedia() []Media {
	return h.Media
}

func (h *HotelListNew) SetMedia(media []Media) {
	h.Media = media
}

func (h *HotelListNew) GetPriceDetail() PriceDetail {
	return h.PriceDetail
}

func (h *HotelListNew) SetPriceDetail(priceDetail PriceDetail) {
	h.PriceDetail = priceDetail
}

type TimerCard struct {
	BottomTitle     string `json:"bottomTitle,omitempty"`
	BottomSubtitle  string `json:"bottomSubtitle,omitempty"`
	TimerTextPrefix string `json:"timerTextPrefix,omitempty"`
	BgImageURL      string `json:"bgImageUrl,omitempty"`
	TimerRemaining  string `json:"timerRemaining,omitempty"`
}

func (t *TimerCard) GetBottomTitle() string {
	return t.BottomTitle
}

func (t *TimerCard) SetBottomTitle(bottomTitle string) {
	t.BottomTitle = bottomTitle
}

func (t *TimerCard) GetBottomSubtitle() string {
	return t.BottomSubtitle
}

func (t *TimerCard) SetBottomSubtitle(bottomSubtitle string) {
	t.BottomSubtitle = bottomSubtitle
}

func (t *TimerCard) GetTimerTextPrefix() string {
	return t.TimerTextPrefix
}

func (t *TimerCard) SetTimerTextPrefix(timerTextPrefix string) {
	t.TimerTextPrefix = timerTextPrefix
}

func (t *TimerCard) GetBgImageURL() string {
	return t.BgImageURL
}

func (t *TimerCard) SetBgImageURL(bgImageURL string) {
	t.BgImageURL = bgImageURL
}

func (t *TimerCard) GetTimerRemaining() string {
	return t.TimerRemaining
}

func (t *TimerCard) SetTimerRemaining(timerRemaining string) {
	t.TimerRemaining = timerRemaining
}

type ViewAllCard struct {
	IconURL   string `json:"iconUrl,omitempty"`
	Title     string `json:"title,omitempty"`
	SubTitle  string `json:"subTitle,omitempty"`
	CtaTitle  string `json:"ctaTitle,omitempty"`
	VoyCityID string `json:"voyCityID,omitempty"`
	Tg        int64  `json:"tg,omitempty"`
	Gd        string `json:"gd,omitempty"`
}

func (v *ViewAllCard) GetIconURL() string {
	return v.IconURL
}

func (v *ViewAllCard) SetIconURL(iconURL string) {
	v.IconURL = iconURL
}

func (v *ViewAllCard) GetTitle() string {
	return v.Title
}

func (v *ViewAllCard) SetTitle(title string) {
	v.Title = title
}

func (v *ViewAllCard) GetSubTitle() string {
	return v.SubTitle
}

func (v *ViewAllCard) SetSubTitle(subTitle string) {
	v.SubTitle = subTitle
}

func (v *ViewAllCard) GetCtaTitle() string {
	return v.CtaTitle
}

func (v *ViewAllCard) SetCtaTitle(ctaTitle string) {
	v.CtaTitle = ctaTitle
}

func (v *ViewAllCard) GetVoyCityID() string {
	return v.VoyCityID
}

func (v *ViewAllCard) SetVoyCityID(voyCityID string) {
	v.VoyCityID = voyCityID
}

type GenericCardData struct {
	Text      string `json:"text,omitempty"`
	SubText   string `json:"subText,omitempty"`
	IconUrl   string `json:"iconUrl,omitempty"`
	Duration  string `json:"duration,omitempty"`
	PromoCode string `json:"promoCode,omitempty"`
}

type InfoItem struct {
	IconUrl string `json:"iconUrl,omitempty"`
	Title   string `json:"title,omitempty"`
}

type LocationData struct {
	Title        string     `json:"title,omitempty"`
	Description  string     `json:"description,omitempty"`
	ImageUrl     string     `json:"imageUrl,omitempty"`
	InfoList     []InfoItem `json:"infoList,omitempty"`
	VideoUrl     string     `json:"videoUrl,omitempty"`
	ThumbnailUrl string     `json:"thumbnailUrl,omitempty"`
	Deeplink     string     `json:"deeplink,omitempty"`
}

type CardPayload struct {
	HotelListNew    []HotelListNew    `json:"hotelListNew,omitempty"`
	TimerCard       TimerCard         `json:"timerCard,omitempty"`
	ViewAllCard     ViewAllCard       `json:"viewAllCard,omitempty"`
	GenericCardData []GenericCardData `json:"genericCardData,omitempty"`
	LocationData    []LocationData    `json:"locationData,omitempty"`
}

func (c *CardPayload) GetHotelListNew() []HotelListNew {
	return c.HotelListNew
}

func (c *CardPayload) SetHotelListNew(hotelListNew []HotelListNew) {
	c.HotelListNew = hotelListNew
}

func (c *CardPayload) GetTimerCard() TimerCard {
	return c.TimerCard
}

func (c *CardPayload) SetTimerCard(timerCard TimerCard) {
	c.TimerCard = timerCard
}

func (c *CardPayload) GetViewAllCard() ViewAllCard {
	return c.ViewAllCard
}

func (c *CardPayload) SetViewAllCard(viewAllCard ViewAllCard) {
	c.ViewAllCard = viewAllCard
}

type CardInfo struct {
	ID          string      `json:"id,omitempty"`
	CardPayload CardPayload `json:"cardPayload,omitempty"`
}

func (c *CardInfo) GetID() string {
	return c.ID
}

func (c *CardInfo) SetID(id string) {
	c.ID = id
}

func (c *CardInfo) GetCardPayload() CardPayload {
	return c.CardPayload
}

func (c *CardInfo) SetCardPayload(cardPayload CardPayload) {
	c.CardPayload = cardPayload
}

type CardData struct {
	Sequence int      `json:"sequence,omitempty"`
	CardInfo CardInfo `json:"cardInfo,omitempty"`
}

func (c *CardData) GetSequence() int {
	return c.Sequence
}

func (c *CardData) SetSequence(sequence int) {
	c.Sequence = sequence
}

func (c *CardData) GetCardInfo() CardInfo {
	return c.CardInfo
}

func (c *CardData) SetCardInfo(cardInfo CardInfo) {
	c.CardInfo = cardInfo
}

type MobLandingData struct {
	CorrelationKey string     `json:"correlationKey,omitempty"`
	CardData       []CardData `json:"cardData,omitempty"`
}

func (r *MobLandingData) GetCorrelationKey() string {
	return r.CorrelationKey
}

func (r *MobLandingData) SetCorrelationKey(correlationKey string) {
	r.CorrelationKey = correlationKey
}

func (r *MobLandingData) GetCardData() []CardData {
	return r.CardData
}

func (r *MobLandingData) SetCardData(cardData []CardData) {
	r.CardData = cardData
}
