package response

import (
	"Hotels-Scion/models/request"
)

type FetchCollectionResponseCG struct {
	Response Response `json:"response,omitempty"`
}

type Response struct {
	FetchCollectionList []FetchCollectionCard `json:"fetchCollectionList,omitempty"`
	SuggestedFilters    FetchCollectionCard   `json:"suggestedFilters,omitempty"`
	CorrelationKey      string                `json:"correlationKey,omitempty"`
}

type FetchCollectionCard struct {
	CardType              string                       `json:"cardType,omitempty"`
	Heading               string                       `json:"heading,omitempty"`
	SubHeading            string                       `json:"subHeading,omitempty"`
	CollectionDescription string                       `json:"collectionDescription,omitempty"`
	Priority              string                       `json:"priority,omitempty"`
	CardList              []request.CardInfo           `json:"cardList,omitempty"`
	CardInfo              request.DataCard             `json:"cardInfo,omitempty"`
	AppliedFilterMap      *map[string][]request.Filter `json:"appliedFilterMap,omitEmpty"`
	Template              string                       `json:"template,omitempty"`
	BgImageUrl            string                       `json:"bgImageUrl,omitempty"`
	Cta                   request.Cta                  `json:"cta,omitempty"`
	CardId                string                       `json:"cardId,omitempty"`
	HotelList             []request.HotelHES           `json:"hotelList,omitempty"`
}
