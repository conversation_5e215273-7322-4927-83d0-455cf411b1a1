package response

import "encoding/json"

//this is part of offer data
type SimilarHotelData struct {
	Data    SimilarHotelDataInner `json:"data,omitempty"`
	DataKey string              `json:"dataKey,omitempty"`
}

type SimilarHotelDataInner struct {
	Heading    string           `json:"heading,omitempty"`
	SubHeading string           `json:"subHeading,omitempty"`
	Data       *json.RawMessage `json:"similarHotelData,omitempty"`
}
