package response

type HotelCosmosOfferData struct {
	Data    interface{} `json:"data,omitempty"`
	DataKey string      `json:"dataKey,omitempty"`
}

type HotelCosmosResponse struct {
	HeaderData    map[string]interface{}          `json:"headerData,omitempty"`
	UniqueIds     map[string][]string             `json:"uniqueIds,omitempty"`
	OfferData     map[string]HotelCosmosOfferData `json:"offerData,omitempty"`
	Status        string                          `json:"status"`
	StatusDetails string                          `json:"statusDetails"`
}
