package response

import "encoding/json"

type SearchHotelResponse struct {
	OfferData     OfferData `json:"offerData,omitempty"`
	Status        string    `json:"status"`
	StatusDetails string    `json:"statusDetails"`
}

//this is part of offer data
type SearchHotelPersonalisedData struct {
	Data SeachHotelDataInner `json:"data,omitempty"`
	DataKey             string              `json:"dataKey,omitempty"`
}

type SeachHotelDataInner struct {
	Heading    string           `json:"heading,omitempty"`
	SubHeading string           `json:"subHeading,omitempty"`
	Data       *json.RawMessage `json:"searchHotelData,omitempty"`
}
