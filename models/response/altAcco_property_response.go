package response

type GetPropertyTypeRsp struct {
	OfferData      map[string]AltAccoPropertyType `json:"offerData,omitempty"`
	Status        string    `json:"status"`
	StatusDetails string    `json:"statusDetails"`
}

type AltAccoProperty struct{
	PropertyType 	string		`json:"propertyType"`
	PropertyName 	string		`json:"propertyName"`
	PropertyDesc 	string		`json:"propertyDesc"`
	ThumbnailUrl 	string		`json:"thumbnailUrl"`
	ListThumbnailUrl string		`json:"listThumbnailUrl"`
	BgUrl 		 	string		`json:"bgUrl"`
	SequenceId   	string		`json:"sequenceId"`
	SequenceHeader 	string		`json:"sequenceHeader"`
	SequenceSubHeader string	`json:"sequenceSubHeader"`
	DesktopDeeplink  string		`json:"desktopDeeplink"`
	PropertyTypeList []string	`json:"propertyTypeList"`
	PropertyPersuasions []*PropertyPersuasions `json:"propertyPersuasions"`

}

type PropertyPersuasions struct {
	Title 			string	`json:"title"`
	Description 	string	`json:"desc"`
	IconUrl			string	`json:"iconUrl"`
}

type AltAccoData struct {
	Heading     string        `json:"heading,omitempty"`
	AltAccoData []*AltAccoProperty `json:"altAccoData,omitempty"`
}

type AltAccoPropertyType struct {
	Data    AltAccoData			`json:"data,omitempty"`
	DataKey string               `json:"dataKey,omitempty"`
}
