package response

import "Hotels-Scion/models/common"

type UniqueIds struct {
	CrossSellPersonalised []string `json:"Cross_Sell_Personalised,omitempty"`
}

type HotelAddress struct {
	Line1 string `json:"line1"`
	Line2 string `json:"line2"`
}

type CouponDetails struct {
	Description string `json:"description"`
}

type DisplayFare struct {
	DisplayPrice       float32       `json:"displayPrice,omitempty"`
	NonDiscountedPrice float32       `json:"nonDiscountedPrice,omitempty"`
	CDFDiscount        float32       `json:"-"`
	CouponDetails      CouponDetails `json:"-"`
}

type Hotel struct {
	Id              string       `json:"id"`
	Name            string       `json:"name"`
	Images          []string     `json:"images"`
	StarRating      int          `json:"starRating"`
	UserRating      float32      `json:"userRating"`
	CurrencyCode    string       `json:"currencyCode,omitempty"`
	Address         HotelAddress `json:"address,omitempty"`
	DisplayFare     DisplayFare  `json:"displayFare,omitempty"`
	AppDeeplink     string       `json:"appDeeplink,omitempty"`
	DesktopDeeplink string       `json:"desktopDeeplink,omitempty"`
}

type BestOfferDetails struct {
	Description string `json:"description"`
}

type CardDetail struct {
	Heading          string               `json:"heading"`
	Hotels           []*Hotel             `json:"hotels,omitempty"`
	SearchContext    common.SearchContext `json:"searchContext"`
	ViewMore         common.ViewMore      `json:"viewMore"`
	MoreDetails      string               `json:"moreDetails"`
	BestOfferDetails BestOfferDetails     `json:"offerDetails"`
}

type CrossSellPersonalisedData struct {
	Heading     string        `json:"heading,omitempty"`
	CardDetails []*CardDetail `json:"cardDetails,omitempty"`
	BookingIds  []string      `json:"bookingIds,omitempty"`
}

type CrossSellPersonalised struct {
	Data    CrossSellPersonalisedData `json:"data,omitempty"`
	DataKey string                    `json:"dataKey,omitempty"`
}

type HotelCrossSellResponse struct {
	UniqueIds     UniqueIds `json:"uniqueIds,omitempty"`
	OfferData     OfferData `json:"offerData,omitempty"`
	Status        string    `json:"status"`
	StatusDetails string    `json:"statusDetails"`
	PriceUpdated  bool      `json:"priceUpdated"` // to mark if price updated in cached data
}
