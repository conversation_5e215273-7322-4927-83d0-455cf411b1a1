package pdt

import (
	"Hotels-Scion/config"
	"Hotels-Scion/models/common"
	"Hotels-Scion/models/request"
	"github.com/linkedin/goavro"
	"time"
)

type PdtModelInterface interface {
	ToStringMap() map[string]interface{}
}

type CrossSellPDTModel struct {
	CorrelationKey       string               `structs:"correlation_key"`
	LocationId           string               `structs:"loc_id"`
	LocationType         string               `structs:"loc_type"`
	CountryCode          string               `structs:"country_code"`
	CheckIn              string               `structs:"checkin"`
	CheckOut             string               `structs:"checkout"`
	NoOfRooms            int                  `structs:"num_rooms"`
	AdultAges            []int                `structs:"adult_ages"`
	AdultCount           int                  `structs:"adult_count"`
	CardId               string               `structs:"card_id"`
	TemplateId           string               `structs:"template_id"`
	ChildAges            []int                `structs:"child_ages"`
	ChildCount           int                  `structs:"child_count"`
	EnrichmentType       string               `structs:"enrichment_type"`
	Errors               string               `structs:"errors"`
	FiltersRemoved       bool                 `structs:"filters_removed"`
	FiltersModified      bool                 `structs:"filters_modifed"`
	RequestedAreas       []string             `structs:"sc_areaidlist"`
	RequestedStarRatings []string             `structs:"sc_starrating"`
	RequestedPriceRange  []request.TrendValue `structs:"sc_pricefilter"`
	RequestedHotelIds    []string             `structs:"sc_hotelidlist"`
	AppliedFilterMap     []common.Filter      `structs:"updated_filters"`
	NoOfHotelsReq        int                  `structs:"num_hotels_req"`
	NoOfHotelsResp       int                  `structs:"num_hotels_resp"`
	Heading              string               `structs:"rule_desc"`
	RuleId               string               `structs:"rule_id"`
	DownstreamTime       int                  `structs:"downstream_time"`
	TopicName            string               `structs:"topic_name"`
	ServerTimeStamp      int64                `structs:"server_timestamp"`
}

func (m CrossSellPDTModel) ToStringMap() map[string]interface{} {
	avroCompatibleMap := map[string]interface{}{
		"correlation_key":            strUnion(m.CorrelationKey),
		"location_id":                strUnion(m.LocationId),
		"location_type":              strUnion(m.LocationType),
		"country_code":               strUnion(m.CountryCode),
		"checkin":                    strUnion(m.CheckIn),
		"checkout":                   strUnion(m.CheckOut),
		"num_rooms":                  intUnion(m.NoOfRooms),
		"adult_ages":                 arrUnion(m.AdultAges),
		"adult_count":                intUnion(m.AdultCount),
		"card_id":                    strUnion(m.CardId),
		"template_id":                strUnion(m.TemplateId),
		"child_ages":                 arrUnion(m.ChildAges),
		"child_count":                intUnion(m.ChildCount),
		"enrichment_type":            strUnion(m.EnrichmentType),
		"errors":                     strUnion(m.Errors),
		"filters_removed":            boolUnion(m.FiltersRemoved),
		"filters_modifed":            boolUnion(m.FiltersModified),
		"search_context_areaidlist":  arrUnionStr(m.RequestedAreas),
		"search_context_starrating":  arrUnionStr(m.RequestedStarRatings),
		"search_context_pricefilter": getPriceFilterDetails(m.RequestedPriceRange),
		"search_context_hotelidlist": arrUnionStr(m.RequestedHotelIds),
		"updated_filters":            getFilterList(m.AppliedFilterMap),
		"num_hotels_req":             intUnion(m.NoOfHotelsReq),
		"num_hotels_resp":            intUnion(m.NoOfHotelsResp),
		"rule_description":           strUnion(m.Heading),
		"rule_id":                    strUnion(m.RuleId),
		"downstream_time":            intUnion(m.DownstreamTime),
		"topic_name":                 config.AppConfig.KafkaConfig.CrossellTopicId,
		"server_timestamp":           time.Now().UnixNano() / 1e6,
	}
	return avroCompatibleMap
}

func getFilterList(filters []common.Filter) interface{} {
	if len(filters) > 0 {
		filterList := make([]interface{}, len(filters))
		for i, filterInfo := range filters {
			filterList[i] = getFilterDatum(filterInfo)
		}
		return goavro.Union("array", filterList)
	} else {
		return nullUnion()
	}
}

func getFilterDatum(inputFilter common.Filter) map[string]interface{} {
	datum := map[string]interface{}{
		"filter_group": string(inputFilter.FilterGroup),
	}
	if nil == inputFilter.FilterValue {
		datum["filter_value"] = nullUnion()
	} else {
		datum["filter_value"] = strUnion(*inputFilter.FilterValue)
	}
	if nil != inputFilter.FilterRange {
		datum["filter_range"] = goavro.Union("com.mmt.logging.updated_filters.updated_filters_item.filter_range", map[string]interface{}{
			"min_value": inputFilter.FilterRange.MinValue,
			"max_value": inputFilter.FilterRange.MaxValue,
		})
	} else {
		datum["filter_range"] = nullUnion()
	}
	return datum
}

func getPriceFilterDetails(priceBuckets []request.TrendValue) interface{} {
	if len(priceBuckets) > 0 {
		priceBucketList := make([]interface{}, len(priceBuckets))
		for i, trend := range priceBuckets {
			priceBucketList[i] = getPriceBucketDetail(trend)
		}
		return goavro.Union("array", priceBucketList)
	} else {
		return nullUnion()
	}
}

func getPriceBucketDetail(trend request.TrendValue) map[string]interface{} {
	datum := map[string]interface{}{
		"min_value": trend.MinValue,
		"max_value": trend.MaxValue,
	}
	return datum
}

func strUnion(str string) interface{} {
	return goavro.Union("string", str)
}

func intUnion(intValue int) interface{} {
	return goavro.Union("int", intValue)
}

func boolUnion(str bool) interface{} {
	return goavro.Union("boolean", str)
}

func arrUnion(arrValue []int) interface{} {
	if len(arrValue) > 0 {
		return goavro.Union("array", arrValue)
	}
	return nullUnion()
}

func arrUnionStr(arrValue []string) interface{} {
	if len(arrValue) > 0 {
		return goavro.Union("array", arrValue)
	}
	return nullUnion()
}

func nullUnion() interface{} {
	return goavro.Union("null", nil)
}
