package orchestrator

import "Hotels-Scion/models/response"

type ResponseErrors struct {
	ErrorList []ResponseError `json:"errorList"`
}

type ResponseError struct {
	ErrorCode    string `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

type CardPayloadResponse struct {
	AltAccoData []*response.AltAccoProperty `json:"altAccoData"`
}

type AltAccoDataResponse struct {
	CardPayload CardPayloadResponse `json:"cardPayload"`
	RspErrors	ResponseErrors	`json:"responseErrors"`
}