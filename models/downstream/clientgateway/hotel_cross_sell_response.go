package clientgateway

import (
	"Hotels-Scion/models/common"
	"Hotels-Scion/models/downstream/clientbackend"
)

// CrossSellStreamResponse is the response for the cross sell stream API
type CrossSellStreamResponse struct {
	Cards []Card `json:"cards"`
}

type CrossSellStreamResponseForCorp struct {
	Cards []CorpCard `json:"cards"`
}

type Card struct {
	CardID      string                    `json:"cardId"`
	TemplateID  string                    `json:"templateId"`
	ComponentID string                    `json:"componentId"`
	HeaderData  Header                    `json:"headerData,omitempty"`
	CardData    CrossSellSearchHotelsData `json:"cardData"`
}

type CorpCard struct {
	CardID      string                          `json:"cardId"`
	TemplateID  string                          `json:"templateId"`
	ComponentID string                          `json:"componentId"`
	HeaderData  Header                          `json:"headerData,omitempty"`
	CardData    CrossSellSearchPersonalizedData `json:"cardData"`
}

type Header struct {
	Header       string `json:"header"`
	Subheader    string `json:"subheader"`
	LobSubheader string `json:"lobSubheader,omitempty"`
}

type OfferExpiry struct {
	ExpiryTimestamp int64 `json:"expiryTs,omitempty"`
	ServerTimestamp int64 `json:"serverTs,omitempty"`
}

type Offer struct {
	ImageUrl        string       `json:"imgUrl,omitempty"`
	ListingDeeplink string       `json:"deeplink,omitempty"`
	OfferImg        string       `json:"offerImg,omitempty"`
	OfferMainImag   string       `json:"offerMainImag,omitempty"`
	OfferExpiry     *OfferExpiry `json:"expiry,omitempty"`
}

type DealPicker struct {
	Icon string `json:"icon,omitempty"`
	Text string `json:"text,omitempty"`
	City string `json:"city,omitempty"`
}

type ExclusiveFilterHotels struct {
	HotelList  []Hotel   `json:"hotels,omitempty"`
	TabDetails TabDetail `json:"tabDetails,omitempty"`
}

type CrossSellSearchHotelsData struct {
	HotelList                  []Hotel                          `json:"hotelList,omitempty"`
	TotalHotelCounts           int                              `json:"totalHotelCounts,omitempty"`
	CityName                   string                           `json:"cityName,omitempty"`
	CountryName                string                           `json:"countryName,omitempty"`
	CountryCode                string                           `json:"countryCode,omitempty"`
	CityCode                   string                           `json:"cityCode,omitempty"`
	CorrelationKey             string                           `json:"correlationKey,omitempty"`
	ListingDeepLink            string                           `json:"listingDeepLink,omitempty"`
	DeeplinkURL                string                           `json:"deeplinkUrl,omitempty"`
	FiltersRemoved             bool                             `json:"filtersRemoved,omitempty"`
	FilterMap                  map[string][]Filter              `json:"filterMap,omitempty"`
	FilterList                 []Filter                         `json:"filterList,omitempty"`
	CommonHotelCategories      []string                         `json:"commonHotelCategories,omitempty"`
	CommonHotelCategoryDetails map[string]CategoryDetails       `json:"commonHotelCategoryDetails,omitempty"`
	ResponseErrors             clientbackend.ResponseErrors     `json:"responseErrors,omitempty"`
	Placeholders               []PlaceHolderCategory            `json:"placeholders,omitempty"`
	FromCity                   string                           `json:"fromCity,omitempty"`
	OfferPersuasions           []OfferPersuasion                `json:"offerPersuasions,omitempty"`
	CardOffer                  *Offer                           `json:"offerCard,omitempty"`
	DealPicker                 DealPicker                       `json:"dealPicker,omitempty"`
	ExclusiveOfferTs           int64                            `json:"exclusiveOfferTs,omitempty"`
	LocusData                  LocusData                        `json:"locusData,omitempty"`
	CrossSellData              CrossSellData                    `json:"crossSellData,omitempty"`
	Tg                         int64                            `json:"tg,omitempty"`
	Gd                         string                           `json:"gd,omitempty"`
	Godata                     Godata                           `json:"-"`
	FilterToHotelMap           map[string]ExclusiveFilterHotels `json:"filterToHotelMap,omitempty"`
}

type CrossSellCardData struct {
	Data     Data   `json:"cardData,omitempty"`
	DeepLink string `json:"deepLink,omitempty"`
	Success  bool   `json:"success,omitempty"`
}

type Data struct {
	BenefitsData     []BenefitData    `json:"benefitsData,omitempty"`
	TabDetails       []TabDetail      `json:"tabDetails,omitempty"`
	CouponInfo       CouponInfo       `json:"couponInfo,omitempty"`
	TitleText        string           `json:"titleText,omitempty"`
	SubText          string           `json:"subText,omitempty"`
	IconUrl          string           `json:"iconUrl,omitempty"`
	WebViewUrl       string           `json:"webViewUrl,omitempty"`
	TagUrl           string           `json:"tagUrl,omitempty"`
	TimerData        *TimerData       `json:"timerData,omitempty"`
	BgLinearGradient BgLinearGradient `json:"bgLinearGradient,omitempty"`
	CardType         string           `json:"cardType,omitempty"`
	Items            []Item           `json:"items,omitempty"`
	IconTags         IconTags         `json:"iconTags,omitempty"`
	CtaList          []CtaList        `json:"ctaList,omitempty"`
}

type Item struct {
	Title    string `json:"title,omitempty"`
	SubTitle string `json:"subTitle,omitempty"`
	ImgUrl   string `json:"imgUrl,omitempty"`
	DeepLink string `json:"deeplink,omitempty"`
}

type TimerData struct {
	Prefix   string  `json:"prefix,omitempty"`
	Duration float64 `json:"duration,omitempty"`
	TxtColor string  `json:"txtColor,omitEmpty"`
	BgColor  string  `json:"bgColor,omitEmpty"`
}

type OfferPersuasion struct {
	Text     string `json:"text,omitEmpty"`
	ImageUrl string `json:"imageUrl,omitempty"`
}

type CrossSellSearchPersonalizedData struct {
	PersonalizedResponse []PersonalizedResponse       `json:"personalizedResponse,omitempty"`
	TotalHotelCounts     int                          `json:"totalHotelCounts,omitempty"`
	CityName             string                       `json:"cityName,omitempty"`
	CountryName          string                       `json:"countryName,omitempty"`
	CountryCode          string                       `json:"countryCode,omitempty"`
	CityCode             string                       `json:"cityCode,omitempty"`
	CorrelationKey       string                       `json:"correlationKey,omitempty"`
	FiltersRemoved       bool                         `json:"filtersRemoved,omitempty"`
	FilterMap            map[string][]Filter          `json:"filterMap,omitempty"`
	FilterList           []Filter                     `json:"filterList,omitempty"`
	ListingDeepLink      string                       `json:"listingDeepLink,omitempty"`
	DeeplinkURL          string                       `json:"deeplinkUrl,omitempty"`
	ResponseErrors       clientbackend.ResponseErrors `json:"responseErrors,omitempty"`
	Placeholders         []PlaceHolderCategory        `json:"placeholders,omitempty"`
	FromCity             string                       `json:"fromCity,omitempty"`
}

type PlaceHolderCategory struct {
	Key   string `json:"key,omitempty"`
	Icon  string `json:"icon,omitempty"`
	Value string `json:"value,omitempty"`
}

// Hotel contains hotel related info
type Hotel struct {
	ID                   string                           `json:"id"`
	GiID                 string                           `json:"giHotelId,omitempty"`
	Name                 string                           `json:"name"`
	PropertyType         string                           `json:"propertyType"`
	PropertyDesc         string                           `json:"propertyDesc"`
	LastBookedInfo       *LastBookedInfo                  `json:"lastBookedInfo,omitempty"`
	MainImages           []string                         `json:"mainImages"`
	StarRating           int                              `json:"starRating"`
	CurrencyCode         Currency                         `json:"currencyCode,omitempty"`
	Address              Address                          `json:"address,omitempty"`
	DisplayFare          DisplayFare                      `json:"displayFare,omitempty"`
	ReviewSummary        ReviewSummary                    `json:"reviewSummary,omitempty"`
	Categories           []string                         `json:"categories"`
	CategoryDetails      map[string]CategoryDetails       `json:"categoryDetails"`
	FlyfishReviewSummary FlyFishReviewSummary             `json:"flyfishReviewSummary"`
	MatchScore           float64                          `json:"matchScore"`
	ReasonToBelieve      []string                         `json:"reasonToBelieve"`
	BreakFastAvailable   bool                             `json:"breakFastAvailable"`
	FreeCancellation     bool                             `json:"freeCancellation"`
	AltAcco              bool                             `json:"altAcco"`
	AppDeepLink          string                           `json:"appDeepLink,omitempty"`
	MmtHotelCategory     string                           `json:"mmtHotelCategory,omitempty"`
	MmtHotelCategoryUrl  string                           `json:"mmtHotelCategoryUrl,omitempty"`
	ReviewDeeplink       string                           `json:"reviewDeeplinkUrl,omitempty"`
	SearchRoomDeeplink   string                           `json:"searchRoomDeeplinkUrl,omitempty"`
	Persuasions          []Persuasion                     `json:"persuasions,omitempty"`
	LoyaltyPersuasions   LoyaltyPersuasion                `json:"loyaltyPersuasion,omitempty"`
	MealPlanIncluded     ExtraMeal                        `json:"mealPlanIncluded,omitempty"`
	ExtraMeals           ExtraMeal                        `json:"extraMeals,omitempty"`
	CityName             string                           `json:"cityName,omitempty"`
	Distance             string                           `json:"distance,omitempty"`
	DistanceUnit         string                           `json:"distanceUnit,omitempty"`
	FromCity             string                           `json:"fromCity,omitempty"`
	FacilityHighlights   []string                         `json:"facilityHighlights,omitempty"`
	AltAccoRoomInfo      map[string]AltAccoRoomInfoObject `json:"altAccoRoomInfo,omitempty"`
	CountryName          string                           `json:"countryName,omitempty"`
	CountryCode          string                           `json:"countryCode,omitempty"`
	LocusData            LocusData                        `json:"locusData,omitempty"`
	GeoLocation          Location                         `json:"geoLocation,omitempty"`
	Tg                   int64                            `json:"tg,omitempty"`
	LocationPersuasion   []string                         `json:"locationPersuasion,omitempty"`
	Gd                   string                           `json:"gd,omitempty"`
	Godata               Godata                           `json:"-"`
	BottomPersuasion     HotelBottomPersuasion            `json:"bottomPersuasion,omitempty"`
	DeepLink             string                           `json:"dtDeepLink,omitempty"`
}

type ExtraMeal struct {
	Desc string `json:"desc,omitempty"`
	Code string `json:"code,omitempty"`
}

type Persuasion struct {
	ID          string `json:"id,omitempty"`
	Desc        string `json:"desc,omitempty"`
	IconUrl     string `json:"iconUrl,omitempty"`
	Placeholder string `json:"placeholder,omitempty"`
	Priority    int    `json:"priority,omitempty"`
	Theme       string `json:"theme,omitempty"`
	Type        string `json:"type,omitempty"`
	Text        string `json:"text,omitempty"`
	BgColor     string `json:"bgColor,omitempty"`
	TxtColor    string `json:"txtColor,omitempty"`
}

type LoyaltyPersuasion struct {
	Logo                  string `json:"logo,omitempty"`
	BenefitIconUrl        string `json:"benefitIconUrl,omitempty"`
	BorderColor           string `json:"borderColor,omitempty"`
	BenefitCountTextColor string `json:"benefitCountTextColor,omitempty"`
	BenefitCountText      string `json:"benefitCountText,omitempty"`
	BenefitText           string `json:"benefitText,omitempty"`
	BgColor               string `json:"bgColor,omitempty"`
	BenefitTextColor      string `json:"benefitTextColor,omitempty"`
}

type LocusData struct {
	LocusId   string `json:"locusId,omitempty"`
	LocusType string `json:"locusType,omitempty"`
	LocusName string `json:"locusName,omitempty"`
}

type CrossSellOffer struct {
	ImageUrl   string `json:"imageUrl,omitempty"`
	Text       string `json:"text,omitempty"`
	SubText    string `json:"subText,omitempty"`
	Font       string `json:"font,omitempty"`
	Icon       string `json:"icon,omitempty"`
	FooterDesc string `json:"footerDesc,omitempty"`
}

type CrossSellAnimation struct {
	BorderColor []string `json:"borderColor,omitempty"`
	LoopCount   int      `json:"loopCount,omitempty"`
}

type CrossSellCoupon struct {
	Code         string `json:"code,omitempty"`
	OverlayColor string `json:"overlayColor,omitempty"`
	CornerColor  string `json:"cornerColor,omitempty"`
	Delay        int    `json:"delay,omitempty"`
}

type CrossSellButton struct {
	ClickText string `json:"clickText,omitempty"`
	BgColor   string `json:"bgColor,omitempty"`
}

type CrossSellData struct {
	LogoUrl    string             `json:"logoUrl,omitempty"`
	Offer      CrossSellOffer     `json:"offer,omitempty"`
	Animation  CrossSellAnimation `json:"animation,omitempty"`
	BgGradient []string           `json:"bgGradient,omitempty"`
	Title      string             `json:"title,omitempty"`
	Subtitle   string             `json:"subtitle,omitempty"`
	Coupon     CrossSellCoupon    `json:"coupon,omitempty"`
	Button     CrossSellButton    `json:"button,omitempty"`
	Persuasion Persuasion         `json:"persuasion,omitempty"`
}

type Filter struct {
	FilterType    string             `json:"filterType,omitempty"`
	FilterValue   string             `json:"filterValue,omitempty"`
	FilterRange   common.FilterRange `json:"filterRange,omitempty"`
	IsRangeFilter bool               `json:"isRangeFilter,omitempty"`
	FilterName    string             `json:"filterName,omitempty"`
	HotelIDs      []string           `json:"hotelIds,omitempty"`
	DeeplinkUrl   string             `json:"deeplinkUrl,omitempty"`
	Currency      string             `json:"currency,omitempty"`
	Title         string             `json:"title,omitempty"`
}

type FlyFishReviewSummary struct {
	MMT Review `json:"MMT,omitempty"`
	TA  Review `json:"TA,omitempty"`
}

type Review struct {
	CumulativeRating  float64 `json:"cumulativeRating,omitempty"`
	TotalReviewsCount int     `json:"totalReviewsCount,omitempty"`
	TotalRatingCount  int     `json:"totalRatingCount,omitempty"`
	CrawledData       bool    `json:"crawledData,omitempty"`
}

type CategoryDetails struct {
	Title        string   `json:"title,omitempty"`
	IconURL      string   `json:"iconUrl,omitempty"`
	ItemIconType string   `json:"itemIconType,omitempty"`
	Data         []string `json:"data,omitempty"`
}

type AltAccoRoomInfoObject struct {
	BedCount       int    `json:"bedCount,omitempty"`
	BedRoomCount   string `json:"bedRoomCount,omitempty"`
	ParentRoomCode string `json:"parentRoomCode,omitempty"`
}

type WalletEntity struct {
	Status            string `json:"status,omitempty"`
	DefaultWalletBurn struct {
		BonusPercentage  int `json:"bonusPercentage,omitempty"`
		MaxBonus         int `json:"maxBonus,omitempty"`
		MinBookingAmount int `json:"minBookingAmount,omitempty"`
	} `json:"defaultWalletBurn,omitempty"`
}

type Location struct {
	Latitude  string `json:"latitude,omitempty"`
	Longitude string `json:"longitude,omitempty"`
}

type Meals struct {
	Desc string      `json:"desc,omitempty"`
	Code string      `json:"code,omitempty"`
	Cost interface{} `json:"cost,omitempty"`
}

type Currency struct {
	ID    string `json:"id,omitempty"`
	Value string `json:"value,omitempty"`
}

type Address struct {
	DisplayLocationName string   `json:"displayLocationName,omitempty"`
	Area                []string `json:"area,omitempty"`
	Line2               string   `json:"line2,omitempty"`
	Line1               string   `json:"line1,omitempty"`
}

type DisplayFare struct {
	TotalRoomCount        int                   `json:"totalRoomCount,omitempty"`
	DisplayPriceBreakDown DisplayPriceBreakDown `json:"displayPriceBreakDown,omitempty"`
	CorpMetaData          CorpMetaData          `json:"corpMetaData,omitempty"`
	OtherCoupons          [1]OtherCoupon        `json:"otherCoupons,omitempty"`
}

type ReviewSummary struct {
	Ota         string  `json:"ota,omitempty"`
	HotelRating float64 `json:"hotelRating,omitempty"`
	UgcScore    float64 `json:"ugcScore,omitempty"`
	ReviewCount int     `json:"reviewCount,omitempty"`
	RatingCount int     `json:"ratingCount,omitempty"`
}

type HotelBottomPersuasion struct {
	Text  string `json:"text,omitempty"`
	Image string `json:"image,omitempty"`
}

type CorpMetaData struct {
	ValidationPayload ValidationPayload `json:"validationPayload,omitempty"`
}

type ValidationPayload struct {
	WithinPolicy bool `json:"withinPolicy,omitempty"`
}

type DisplayPriceBreakDown struct {
	DisplayPrice                  float64    `json:"displayPrice,omitempty"`
	DisplayPriceAlternateCurrency float64    `json:"displayPriceAlternateCurrency,omitempty"`
	NonDiscountedPrice            float64    `json:"nonDiscountedPrice,omitempty"`
	SavingPerc                    float64    `json:"savingPerc,omitempty"`
	CdfDiscount                   float64    `json:"cdfDiscount,omitempty"`
	BlackDiscount                 float64    `json:"blackDiscount,omitempty"`
	CouponInfo                    CouponInfo `json:"couponInfo,omitempty"`
	PriceText                     string     `json:"priceText,omitempty"`
	PriceType                     string     `json:"priceType,omitempty"`
}

type CouponInfo struct {
	CouponCode         string `json:"couponCode,omitempty"`
	Type               string `json:"type,omitempty"`
	Description        string `json:"description,omitempty"`
	DiscountAmount     int    `json:"discountAmount,omitempty"`
	SpecialPromoCoupon bool   `json:"specialPromoCoupon,omitempty"`
}

// Other coupons hold the coupons that are specific to a bank
type OtherCoupon struct {
	CouponCode               string  `json:"couponCode,omitempty"`
	Message                  string  `json:"message,omitempty"`
	EffectivePriceWithCoupon float64 `json:"effectivePriceWithCoupon,omitempty"`
}

type DynamicPersuasion struct {
	Id          string `json:"id,omitempty"`
	Desc        string `json:"desc,omitempty"`
	Placeholder string `json:"placeholder,omitempty"`
	Priority    int    `json:"priority,omitempty"`
}

type CategoryIconData struct {
	Icon  string `json:"icon,omitempty"`
	Value string `json:"value,omitempty"`
}

type PersonalizedResponse struct {
	Section              string              `json:"section,omitempty"`
	Heading              string              `json:"heading,omitempty"`
	SubHeading           string              `json:"subHeading,omitempty"`
	Horizontal           bool                `json:"horizontal,omitempty"`
	OrderPriority        int                 `json:"orderPriority,omitempty"`
	Count                int                 `json:"count,omitempty"`
	Hotels               []Hotel             `json:"hotels,omitempty"`
	HeadingParameters    map[string][]string `json:"headingParameters,omitempty"`
	CardInsertionAllowed bool                `json:"cardInsertionAllowed,omitempty"`
	CategoryPersuasions  *CategoryPersuasion `json:"categoryPersuasions,omitempty"`
}

type LastBookedInfo struct {
	CheckInDate    int64  `json:"checkinDate,omitempty"`
	CheckOutDate   int64  `json:"checkoutDate,omitempty"`
	LastBookedDate string `json:"lastBookedDate,omitempty"`
	RatePlanId     string `json:"ratePlanId,omitempty"`
	RoomId         string `json:"roomId,omitempty"`
	RoomName       string `json:"roomName,omitempty"`
}

type CategoryPersuasion struct {
	Text string `json:"text,omitempty"`
	Icon string `json:"icon,omitempty"`
}

type CrossSellV2StreamResponse struct {
	CardsV2 []CardV2 `json:"cards"`
}

type CardV2 struct {
	CardID      string     `json:"cardId"`
	TemplateID  string     `json:"templateId"`
	ComponentID string     `json:"componentId"`
	HeaderData  Header     `json:"headerData,omitempty"`
	CardDataV2  CardDataV2 `json:"cardData,omitempty"`
}

type CardDataV2 struct {
	TabDetails       []TabDetail                          `json:"tabDetails,omitempty"`
	TabsData         map[string]CrossSellSearchHotelsData `json:"tabsData,omitempty"`
	Persuasion       ExclusivePersuasion                  `json:"persuasion,omitempty"`
	ResponseErrors   clientbackend.ResponseErrors         `json:"responseErrors,omitempty"`
	Intent           string                               `json:"intent,omitempty"`
	OmnitureKey      string                               `json:"omnitureKey,omitempty"`
	CtaList          []CtaList                            `json:"CtaList,omitempty"`
	BenefitCardData  *BenefitCardData                     `json:"benefitCardData,omitempty"`
	CardResponse     *CardResponse                        `json:"cardResponse,omitempty"`
	BgLinearGradient BgLinearGradient                     `json:"bgLinearGradient,omitempty"`
	CardType         string                               `json:"cardType,omitempty"`
	IconTags         IconTags                             `json:"iconTags,omitempty"`
}

type CardResponse struct {
	Items      []Item     `json:"cityConfig,omitempty"`
	CouponInfo CouponInfo `json:"couponInfo,omitempty"`
	Header     string     `json:"header,omitempty"`
	Text       string     `json:"text,omitempty"`
	Icon       string     `json:"icon,omitempty"`
	TagUrl     string     `json:"tagUrl,omitempty"`
	TimerData  *TimerData `json:"timerData,omitempty"`
}

type TabDetail struct {
	Id       string `json:"id"`
	Title    string `json:"title"`
	IsHotel  bool   `json:"isHotel"`
	Selected bool   `json:"selected"`
	Deeplink string `json:"deeplink"`
	Tg       int64  `json:"tg,omitempty"`
	Gd       string `json:"gd,omitempty"`
	Godata   Godata `json:"-"`
	IconUrl  string `json:"iconUrl,omitempty"`
}

type BenefitCardData struct {
	BenefitsData []BenefitData `json:"benefitsData,omitempty"`
	CouponInfo   CouponInfo    `json:"couponInfo,omitempty"`
	Header       string        `json:"header,omitempty"`
	Text         string        `json:"text,omitempty"`
	Icon         string        `json:"icon,omitempty"`
	TagUrl       string        `json:"tagUrl,omitempty"`
	TimerData    *TimerData    `json:"timerData,omitempty"`
	WebViewUrl   string        `json:"webViewUrl,omitempty"`
}

type BenefitData struct {
	Icon    string `json:"icon,omitempty"`
	Text    string `json:"text,omitempty"`
	SubText string `json:"subText,omitempty"`
}

type ExclusivePersuasion struct {
	BgColor  string `json:"bgColor,omitEmpty"`
	Icon     string `json:"icon,omitempty"`
	Text     string `json:"text,omitempty"`
	TxtColor string `json:"txtColor,omitEmpty"`
}

type CtaList struct {
	DeepLink string `json:"deepLink,omitempty"`
	Text     string `json:"text,omitempty"`
	Position string `json:"position,omitempty"`
}

type BgLinearGradient struct {
	Start     string `json:"start,omitempty"`
	End       string `json:"end,omitempty"`
	Direction string `json:"direction,omitempty"`
	Center    string `json:"center,omitempty"`
}

type Godata struct {
	N          string `json:"n,omitempty"`
	Cn         string `json:"cn,omitempty"`
	T          string `json:"t,omitempty"`
	Hn         string `json:"hn,omitempty"`
	MHotelId   string `json:"mHotelId,omitempty"`
	LocusType  string `json:"locusType,omitempty"`
	LocusId    string `json:"locusId,omitempty"`
	Vcid       string `json:"vcid,omitempty"`
	Cid        string `json:"cid,omitempty"`
	Oid        string `json:"oid,omitempty"`
	Cc         string `json:"cc,omitempty"`
	Checkin    string `json:"checkin,omitempty"`
	Checkout   string `json:"checkout,omitempty"`
	Vhid       string `json:"vhid,omitempty"`
	RoomString string `json:"roomString,omitempty"`
}

type IconTags struct {
	Text        string     `json:"text"`
	BgGradient  BgGradient `json:"bgGradient"`
	BorderColor string     `json:"borderColor"`
}

type BgGradient struct {
	Start string `json:"start"`
	End   string `json:"end"`
}
