package clientgateway

type HotelsMobLandingResponse struct {
	Response       Response `json:"response,omitempty"`
	CorrelationKey string   `json:"correlationKey,omitempty"`
}
type HotelCategory struct {
	FilterGroup         string `json:"filterGroup,omitempty"`
	FilterValue         string `json:"filterValue,omitempty"`
	QuantityFilter      bool   `json:"quantityFilter,omitempty"`
	TranslationRequired bool   `json:"translationRequired,omitempty"`
	RangeFilter         bool   `json:"rangeFilter,omitempty"`
	LastApplied         bool   `json:"lastApplied,omitempty"`
}
type FilterList struct {
	HotelCategory []HotelCategory `json:"HOTEL_CATEGORY,omitempty"`
}
type Filters struct {
	FilterList FilterList `json:"filterList,omitempty"`
}
type CardAction struct {
	Filters    Filters `json:"filters,omitempty"`
	Title      string  `json:"title,omitempty"`
	Slug       string  `json:"slug,omitempty"`
	Desc       string  `json:"desc,omitempty"`
	WebViewURL string  `json:"webViewUrl,omitempty"`
}

type SearchContext struct {
}
type Media struct {
	URL       string `json:"url,omitempty"`
	MediaType string `json:"mediaType,omitempty"`
}
type Coupon struct {
	Code                string  `json:"code,omitempty"`
	Description         string  `json:"description,omitempty"`
	SpecialPromo        bool    `json:"specialPromo,omitempty"`
	Type                string  `json:"type,omitempty"`
	CouponAmount        float64 `json:"couponAmount,omitempty"`
	AutoApplicable      bool    `json:"autoApplicable,omitempty"`
	BnplAllowed         bool    `json:"bnplAllowed,omitempty"`
	Disabled            bool    `json:"disabled,omitempty"`
	BankOffer           bool    `json:"bankOffer,omitempty"`
	NoCostEmiApplicable bool    `json:"noCostEmiApplicable,omitempty"`
}
type PriceDetail struct {
	Price                  float64 `json:"price,omitempty"`
	PriceWithTax           float64 `json:"priceWithTax,omitempty"`
	DiscountedPrice        float64 `json:"discountedPrice,omitempty"`
	DiscountedPriceWithTax float64 `json:"discountedPriceWithTax,omitempty"`
	TotalTax               float64 `json:"totalTax,omitempty"`
	Coupon                 Coupon  `json:"coupon,omitempty"`
	PricingKey             string  `json:"pricingKey,omitempty"`
	MyPartnerDiscount      float64 `json:"myPartnerDiscount,omitempty"`
	GroupPriceText         string  `json:"groupPriceText,omitempty"`
	PriceSuffix            string  `json:"priceSuffix,omitempty"`
	TaxesAndFeesText       string  `json:"taxesAndFeesText,omitempty"`
}
type Style struct {
	TextColor string `json:"textColor,omitempty"`
}

type MobData struct {
	ID                      string           `json:"id,omitempty"`
	PersuasionType          string           `json:"persuasionType,omitempty"`
	Text                    string           `json:"text,omitempty"`
	HasAction               bool             `json:"hasAction,omitempty"`
	Icontype                string           `json:"icontype,omitempty"`
	Style                   Style            `json:"style,omitempty"`
	MultiPersuasionCount    int              `json:"multiPersuasionCount,omitempty"`
	MultiPersuasionPriority int              `json:"multiPersuasionPriority,omitempty"`
	PersuasionKey           string           `json:"persuasionKey,omitempty"`
	Horizontal              bool             `json:"horizontal,omitempty"`
	HTML                    bool             `json:"html,omitempty"`
	BenefitsData            []BenefitData    `json:"benefitsData,omitempty"`
	TabDetails              []TabDetail      `json:"tabDetails,omitempty"`
	CouponInfo              CouponInfo       `json:"couponInfo,omitempty"`
	TitleText               string           `json:"titleText,omitempty"`
	SubText                 string           `json:"subText,omitempty"`
	IconUrl                 string           `json:"iconUrl,omitempty"`
	WebViewUrl              string           `json:"webViewUrl,omitempty"`
	TagUrl                  string           `json:"tagUrl,omitempty"`
	TimerData               *TimerData       `json:"timerData,omitempty"`
	BgLinearGradient        BgLinearGradient `json:"bgLinearGradient,omitempty"`
	CardType                string           `json:"cardType,omitempty"`
	Items                   []Item           `json:"items,omitempty"`
	IconTags                IconTags         `json:"iconTags,omitempty"`
	CtaList                 []CtaList        `json:"ctaList,omitempty"`
}

type CardSubtitle struct {
	Placeholder string    `json:"placeholder,omitempty"`
	Template    string    `json:"template,omitempty"`
	Data        []MobData `json:"data,omitempty"`
}

type PlaceholderCardM2 struct {
	Placeholder string `json:"placeholder,omitempty"`
	Template    string `json:"template,omitempty"`
	Data        []Data `json:"data,omitempty"`
}

type LocationDetail struct {
	ID          string `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Type        string `json:"type,omitempty"`
	CountryID   string `json:"countryId,omitempty"`
	CountryName string `json:"countryName,omitempty"`
}
type GeoLocation struct {
	Latitude  float64 `json:"latitude,omitempty"`
	Longitude float64 `json:"longitude,omitempty"`
}
type TrackingNode struct {
}
type Metadata struct {
	TagType string `json:"tagType,omitempty"`
}
type SrTrackingNode struct {
	ViewTrckgReqd  bool `json:"viewTrckgReqd,omitempty"`
	ClickTrckgReqd bool `json:"clickTrckgReqd,omitempty"`
}
type TrackingInfo struct {
	TrackingNode   TrackingNode   `json:"tracking_node,omitempty"`
	Metadata       Metadata       `json:"metadata,omitempty"`
	AdID           string         `json:"adId,omitempty"`
	CmpnID         string         `json:"cmpnId,omitempty"`
	AdrID          string         `json:"adrId,omitempty"`
	AdReqID        string         `json:"adReqId,omitempty"`
	SrTrackingNode SrTrackingNode `json:"srTrackingNode,omitempty"`
}
type CalendarCriteria struct {
	Available   bool   `json:"available,omitempty"`
	MaxDate     string `json:"maxDate,omitempty"`
	AdvanceDays int    `json:"advanceDays,omitempty"`
	Mlos        int    `json:"mlos,omitempty"`
}
type FacilitiesConfigMap struct {
	Cctvsur string `json:"CCTVSUR,omitempty"`
	Ac      string `json:"AC,omitempty"`
}
type FilteredReviewCount struct {
	Image                int `json:"image,omitempty"`
	Negative             int `json:"negative,omitempty"`
	Featured             int `json:"featured,omitempty"`
	PosWithContent       int `json:"posWithContent,omitempty"`
	ExperiencedTraveller int `json:"experiencedTraveller,omitempty"`
	Positive             int `json:"positive,omitempty"`
	NegWithContent       int `json:"negWithContent,omitempty"`
}
type Num1 struct {
	Count int `json:"count,omitempty"`
}
type Num2 struct {
	Count int `json:"count,omitempty"`
}
type Num3 struct {
	Count int `json:"count,omitempty"`
}
type Num4 struct {
	Count int `json:"count,omitempty"`
}
type Num5 struct {
	Count int `json:"count,omitempty"`
}
type RatingWiseCount struct {
	Num1 Num1 `json:"1,omitempty"`
	Num2 Num2 `json:"2,omitempty"`
	Num3 Num3 `json:"3,omitempty"`
	Num4 Num4 `json:"4,omitempty"`
	Num5 Num5 `json:"5,omitempty"`
}
type TopicRatings struct {
	Rating float64 `json:"rating,omitempty"`
	Title  string  `json:"title,omitempty"`
}
type ReviewSummaryUgc struct {
	FilteredReviewCount FilteredReviewCount `json:"filteredReviewCount,omitempty"`
	HotelRating         float64             `json:"hotelRating,omitempty"`
	ReviewCount         int                 `json:"reviewCount,omitempty"`
	RatingCount         int                 `json:"ratingCount,omitempty"`
	Ota                 string              `json:"ota,omitempty"`
	RatingWiseCount     RatingWiseCount     `json:"ratingWiseCount,omitempty"`
	TopicRatings        []TopicRatings      `json:"topicRatings,omitempty"`
}

type FacilitiesConfigMap0 struct {
	Cctvsur  string `json:"CCTVSUR,omitempty"`
	Inentmnt string `json:"INENTMNT,omitempty"`
	Ac       string `json:"AC,omitempty"`
	Rstrnt   string `json:"RSTRNT,omitempty"`
	Firstaid string `json:"FIRSTAID,omitempty"`
	Lndry    string `json:"LNDRY,omitempty"`
	Dining   string `json:"DINING,omitempty"`
}

type FacilitiesConfigMap1 struct {
	Cctvsur  string `json:"CCTVSUR,omitempty"`
	Inentmnt string `json:"INENTMNT,omitempty"`
	Rmsvc    string `json:"RMSVC,omitempty"`
	Ac       string `json:"AC,omitempty"`
	Crtkr    string `json:"CRTKR,omitempty"`
}
type FacilitiesConfigMap2 struct {
	Cctvsur  string `json:"CCTVSUR,omitempty"`
	Rmsvc    string `json:"RMSVC,omitempty"`
	Ac       string `json:"AC,omitempty"`
	Firstaid string `json:"FIRSTAID,omitempty"`
	Lng      string `json:"LNG,omitempty"`
	Trasst   string `json:"TRASST,omitempty"`
	Lndry    string `json:"LNDRY,omitempty"`
	Crtkr    string `json:"CRTKR,omitempty"`
}
type FacilitiesConfigMap3 struct {
	Inentmnt    string `json:"INENTMNT,omitempty"`
	Ac          string `json:"AC,omitempty"`
	Rstrnt      string `json:"RSTRNT,omitempty"`
	Bnqthall    string `json:"BNQTHALL,omitempty"`
	Trasst      string `json:"TRASST,omitempty"`
	Dining      string `json:"DINING,omitempty"`
	Whlch       string `json:"WHLCH,omitempty"`
	Cnfrnceroom string `json:"CNFRNCEROOM,omitempty"`
	Busns       string `json:"BUSNS,omitempty"`
	Postalservc string `json:"POSTALSERVC,omitempty"`
	Cctvsur     string `json:"CCTVSUR,omitempty"`
	Firstaid    string `json:"FIRSTAID,omitempty"`
	Lndry       string `json:"LNDRY,omitempty"`
	Crtkr       string `json:"CRTKR,omitempty"`
	Disdisinf   string `json:"DISDISINF,omitempty"`
}

type FacilitiesConfigMap4 struct {
	Cctvsur  string `json:"CCTVSUR,omitempty"`
	Ac       string `json:"AC,omitempty"`
	Firstaid string `json:"FIRSTAID,omitempty"`
	Lndry    string `json:"LNDRY,omitempty"`
	Whlch    string `json:"WHLCH,omitempty"`
}
type FacilitiesConfigMap5 struct {
	Cctvsur     string `json:"CCTVSUR,omitempty"`
	Inentmnt    string `json:"INENTMNT,omitempty"`
	Rmsvc       string `json:"RMSVC,omitempty"`
	Ac          string `json:"AC,omitempty"`
	Rstrnt      string `json:"RSTRNT,omitempty"`
	Bnqthall    string `json:"BNQTHALL,omitempty"`
	Trasst      string `json:"TRASST,omitempty"`
	Lndry       string `json:"LNDRY,omitempty"`
	Cnfrnceroom string `json:"CNFRNCEROOM,omitempty"`
	Disdisinf   string `json:"DISDISINF,omitempty"`
}

type FacilitiesConfigMap6 struct {
	Cctvsur     string `json:"CCTVSUR,omitempty"`
	Ac          string `json:"AC,omitempty"`
	Rstrnt      string `json:"RSTRNT,omitempty"`
	Bar         string `json:"BAR,omitempty"`
	Firstaid    string `json:"FIRSTAID,omitempty"`
	Lng         string `json:"LNG,omitempty"`
	Trasst      string `json:"TRASST,omitempty"`
	Pool        string `json:"POOL,omitempty"`
	Lndry       string `json:"LNDRY,omitempty"`
	Whlch       string `json:"WHLCH,omitempty"`
	Cnfrnceroom string `json:"CNFRNCEROOM,omitempty"`
}
type FacilitiesConfigMap7 struct {
	Cctvsur  string `json:"CCTVSUR,omitempty"`
	Ac       string `json:"AC,omitempty"`
	Rstrnt   string `json:"RSTRNT,omitempty"`
	Bar      string `json:"BAR,omitempty"`
	Firstaid string `json:"FIRSTAID,omitempty"`
	Lng      string `json:"LNG,omitempty"`
	Lndry    string `json:"LNDRY,omitempty"`
	Dining   string `json:"DINING,omitempty"`
}
type FacilitiesConfigMap8 struct {
	Cctvsur     string `json:"CCTVSUR,omitempty"`
	Inentmnt    string `json:"INENTMNT,omitempty"`
	Ac          string `json:"AC,omitempty"`
	Cafe        string `json:"CAFE,omitempty"`
	Rstrnt      string `json:"RSTRNT,omitempty"`
	Firstaid    string `json:"FIRSTAID,omitempty"`
	Spa         string `json:"SPA,omitempty"`
	Bnqthall    string `json:"BNQTHALL,omitempty"`
	Oacts       string `json:"OACTS,omitempty"`
	Dining      string `json:"DINING,omitempty"`
	Whlch       string `json:"WHLCH,omitempty"`
	Cnfrnceroom string `json:"CNFRNCEROOM,omitempty"`
}

type HotelListNew struct {
	ID                       string               `json:"id,omitempty"`
	GiID                     string               `json:"giId,omitempty"`
	Name                     string               `json:"name,omitempty"`
	PropertyType             string               `json:"propertyType,omitempty"`
	PropertyLabel            string               `json:"propertyLabel,omitempty"`
	StayType                 string               `json:"stayType,omitempty"`
	StarRating               int                  `json:"starRating,omitempty"`
	SoldOut                  bool                 `json:"soldOut,omitempty"`
	ShowCallToBook           bool                 `json:"showCallToBook,omitempty"`
	GroupBookingHotel        bool                 `json:"groupBookingHotel,omitempty"`
	GroupBookingPrice        bool                 `json:"groupBookingPrice,omitempty"`
	MaskedPrice              bool                 `json:"maskedPrice,omitempty"`
	IsGroupBookingForSimilar bool                 `json:"isGroupBookingForSimilar,omitempty"`
	AlternateDates           bool                 `json:"alternateDates,omitempty"`
	MultiRoomRecommendation  bool                 `json:"multiRoomRecommendation,omitempty"`
	IsAltAcco                bool                 `json:"isAltAcco,omitempty"`
	MtKey                    string               `json:"mtKey,omitempty"`
	TotalImageCount          int                  `json:"totalImageCount,omitempty"`
	TravellerImageCount      int                  `json:"travellerImageCount,omitempty"`
	Categories               []string             `json:"categories,omitempty"`
	LocationPersuasion       []string             `json:"locationPersuasion,omitempty"`
	Media                    []Media              `json:"media,omitempty"`
	PriceDetail              PriceDetail          `json:"priceDetail,omitempty"`
	HotelPersuasions         interface{}          `json:"hotelPersuasions"`
	LocationDetail           LocationDetail       `json:"locationDetail,omitempty"`
	GeoLocation              GeoLocation          `json:"geoLocation,omitempty"`
	FreeCancellationText     string               `json:"freeCancellationText,omitempty"`
	ShortList                bool                 `json:"shortList,omitempty"`
	TotalRoomCount           int                  `json:"totalRoomCount,omitempty"`
	TrackingInfo             TrackingInfo         `json:"trackingInfo,omitempty"`
	Sponsored                bool                 `json:"sponsored,omitempty"`
	NewType                  bool                 `json:"newType,omitempty"`
	PoiTag                   string               `json:"poiTag,omitempty"`
	MmtHotelCategory         string               `json:"mmtHotelCategory,omitempty"`
	AppDeeplink              string               `json:"appDeeplink,omitempty"`
	SeoURL                   string               `json:"seoUrl,omitempty"`
	CalendarCriteria         CalendarCriteria     `json:"calendarCriteria,omitempty"`
	SimilarHotelsRequired    bool                 `json:"similarHotelsRequired,omitempty"`
	FacilitiesConfigMap      FacilitiesConfigMap  `json:"facilitiesConfigMap,omitempty"`
	RoomName                 string               `json:"roomName,omitempty"`
	ReviewSummaryUgc         ReviewSummaryUgc     `json:"reviewSummaryUgc,omitempty"`
	FacilityHighlights       []string             `json:"facilityHighlights,omitempty"`
	MyBizAssured             bool                 `json:"myBizAssured,omitempty"`
	PerNightTitle            string               `json:"perNightTitle,omitempty"`
	LovedByIndians           bool                 `json:"lovedByIndians,omitempty"`
	ServiceApartment         bool                 `json:"serviceApartment,omitempty"`
	IsWishListed             bool                 `json:"isWishListed,omitempty"`
	FacilitiesConfigMap0     FacilitiesConfigMap0 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap1     FacilitiesConfigMap1 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap2     FacilitiesConfigMap2 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap3     FacilitiesConfigMap3 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap4     FacilitiesConfigMap4 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap5     FacilitiesConfigMap5 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap6     FacilitiesConfigMap6 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap7     FacilitiesConfigMap7 `json:"facilitiesConfigMap,omitempty"`
	FacilitiesConfigMap8     FacilitiesConfigMap8 `json:"facilitiesConfigMap,omitempty"`
}
type TimerCard struct {
	BottomTitle     string `json:"bottomTitle,omitempty"`
	BottomSubtitle  string `json:"bottomSubtitle,omitempty"`
	TimerTextPrefix string `json:"timerTextPrefix,omitempty"`
	BgImageURL      string `json:"bgImageUrl,omitempty"`
	TimerRemaining  string `json:"timerRemaining,omitempty"`
}
type ViewAllCard struct {
	IconURL   string `json:"iconUrl,omitempty"`
	Title     string `json:"title,omitempty"`
	SubTitle  string `json:"subTitle,omitempty"`
	CtaTitle  string `json:"ctaTitle,omitempty"`
	VoyCityID string `json:"voyCityID,omitempty"`
}
type CardPayload struct {
	SearchContext   SearchContext     `json:"searchContext,omitempty"`
	HotelListNew    []HotelListNew    `json:"hotelListNew,omitempty"`
	TimerCard       TimerCard         `json:"timerCard,omitempty"`
	ViewAllCard     ViewAllCard       `json:"viewAllCard,omitempty"`
	GenericCardData []GenericCardData `json:"genericCardData,omitempty"`
	LocationData    []LocationData    `json:"locationData,omitempty"`
}

type GenericCardData struct {
	Text      string `json:"text,omitempty"`
	SubText   string `json:"subText,omitempty"`
	IconUrl   string `json:"iconUrl,omitempty"`
	Duration  string `json:"duration,omitempty"`
	PromoCode string `json:"promoCode,omitempty"`
}

type LocationData struct {
	Title        string     `json:"title,omitempty"`
	Description  string     `json:"description,omitempty"`
	ImageUrl     string     `json:"imageUrl,omitempty"`
	InfoList     []InfoItem `json:"infoList,omitempty"`
	VideoUrl     string     `json:"videoUrl,omitempty"`
	ThumbnailUrl string     `json:"thumbnailUrl,omitempty"`
	Deeplink     string     `json:"deeplink,omitempty"`
}

type InfoItem struct {
	IconUrl string `json:"iconUrl,omitempty"`
	Title   string `json:"title,omitempty"`
}

type CardInfo struct {
	Index            int              `json:"index,omitempty"`
	SubType          string           `json:"subType,omitempty"`
	ID               string           `json:"id,omitempty"`
	TitleText        string           `json:"titleText,omitempty"`
	HasFilter        bool             `json:"hasFilter,omitempty"`
	CardAction       []CardAction     `json:"cardAction,omitempty"`
	CardPayload      CardPayload      `json:"cardPayload,omitempty"`
	HasAction        bool             `json:"hasAction,omitempty"`
	BgLinearGradient BgLinearGradient `json:"bgLinearGradient,omitempty"`
	TemplateID       string           `json:"templateId,omitempty"`
	Description      string           `json:"description,omitempty"`
	HasTooltip       bool             `json:"hasTooltip,omitempty"`
	IsClaimed        bool             `json:"isClaimed,omitempty"`
}

type CardData struct {
	Sequence int      `json:"sequence,omitempty"`
	CardInfo CardInfo `json:"cardInfo,omitempty"`
}
type ListPersonalizationResponse struct {
	CardData     []CardData `json:"cardData,omitempty"`
	ExperimentID int        `json:"experimentId,omitempty"`
	TrackText    string     `json:"trackText,omitempty"`
}
type PagemakerResponse struct {
}
type ExpData struct {
	Adc                                                          string `json:"ADC,omitempty"`
	Occfcnr                                                      string `json:"OCCFCNR,omitempty"`
	Hfc                                                          string `json:"HFC,omitempty"`
	Video                                                        string `json:"VIDEO,omitempty"`
	Apt                                                          string `json:"APT,omitempty"`
	Chpc                                                         string `json:"CHPC,omitempty"`
	Addon                                                        string `json:"ADDON,omitempty"`
	Rrr                                                          string `json:"RRR,omitempty"`
	Fbp                                                          string `json:"FBP,omitempty"`
	Fbs                                                          string `json:"FBS,omitempty"`
	Black                                                        string `json:"BLACK,omitempty"`
	Cv2                                                          string `json:"CV2,omitempty"`
	Hrnb                                                         string `json:"HRNB,omitempty"`
	Emi                                                          string `json:"EMI,omitempty"`
	Tft                                                          string `json:"TFT,omitempty"`
	Gec                                                          string `json:"GEC,omitempty"`
	Aip                                                          string `json:"AIP,omitempty"`
	Smc                                                          string `json:"SMC,omitempty"`
	Spcr                                                         string `json:"SPCR,omitempty"`
	Crf                                                          string `json:"CRF,omitempty"`
	Iao                                                          string `json:"IAO,omitempty"`
	Pah5                                                         string `json:"PAH5,omitempty"`
	Lsof                                                         string `json:"LSOF,omitempty"`
	Hpi                                                          string `json:"HPI,omitempty"`
	Eff                                                          string `json:"EFF,omitempty"`
	Aari                                                         string `json:"AARI,omitempty"`
	Rcpn                                                         string `json:"RCPN,omitempty"`
	Gbe                                                          string `json:"GBE,omitempty"`
	Mrs                                                          string `json:"MRS,omitempty"`
	Nlp                                                          string `json:"NLP,omitempty"`
	Nhl                                                          string `json:"NHL,omitempty"`
	Hafc                                                         string `json:"HAFC,omitempty"`
	Bnpl                                                         string `json:"BNPL,omitempty"`
	Mmrver                                                       string `json:"MMRVER,omitempty"`
	Pdo                                                          string `json:"PDO,omitempty"`
	Dpcr                                                         string `json:"DPCR,omitempty"`
	Cgc                                                          string `json:"CGC,omitempty"`
	Fltrprcbkt                                                   string `json:"FLTRPRCBKT,omitempty"`
	St                                                           string `json:"ST,omitempty"`
	Soc                                                          string `json:"SOC,omitempty"`
	Mlos                                                         string `json:"MLOS,omitempty"`
	DetailV3                                                     string `json:"detailV3,omitempty"`
	Addv                                                         string `json:"ADDV,omitempty"`
	His                                                          string `json:"HIS,omitempty"`
	Pah                                                          string `json:"PAH,omitempty"`
	BnplNewVariant                                               string `json:"bnplNewVariant,omitempty"`
	Hscfs                                                        string `json:"HSCFS,omitempty"`
	Sou                                                          string `json:"SOU,omitempty"`
	Srrp                                                         string `json:"SRRP,omitempty"`
	UnificationReviewV2                                          string `json:"unificationReviewV2,omitempty"`
	Hstv2                                                        string `json:"HSTV2,omitempty"`
	NewUserPfIh                                                  string `json:"new_user_pf_ih,omitempty"`
	SecrecyFlowHourly                                            string `json:"secrecy_flow_hourly,omitempty"`
	GiHhVariant                                                  string `json:"gi_hh_variant,omitempty"`
	MultiExp                                                     string `json:"multi_exp,omitempty"`
	PriceSortingFixes                                            string `json:"priceSortingFixes,omitempty"`
	DwebTransitDh                                                string `json:"dweb_transit_dh,omitempty"`
	MmtBackendHotelDefaultListingDefaultUnifiedRankingGRPC       string `json:"mmt.backend.hotel.default.listing.default.unifiedRankingGRPC,omitempty"`
	GiPwaHotelDefaultDefaultDefaultMwebAppBannerSticky           string `json:"gi.pwa.hotel.default.default.default.mweb_app_banner_sticky,omitempty"`
	LocationPersuasion                                           string `json:"location_persuasion,omitempty"`
	NewUserComm                                                  string `json:"new_user_comm,omitempty"`
	DetailsBudget                                                string `json:"details_budget,omitempty"`
	IosTransitionImprovement                                     string `json:"ios_transition_improvement,omitempty"`
	MwebTruecallerLoginHotels                                    string `json:"mweb_truecaller_login_hotels,omitempty"`
	DetailsSoldout                                               string `json:"details_soldout,omitempty"`
	HUnifAutos                                                   string `json:"h_unif_autos,omitempty"`
	LastViewed                                                   string `json:"last_viewed,omitempty"`
	UnificationSrpFilterV2                                       string `json:"unificationSrpFilterV2,omitempty"`
	UnifiedHotelLandingWeb                                       string `json:"unified_hotel_landing_web,omitempty"`
	Aaqb                                                         string `json:"aaqb,omitempty"`
	MaxCheckin                                                   string `json:"maxCheckin,omitempty"`
	BlockUserWhenMandatory                                       string `json:"blockUserWhenMandatory,omitempty"`
	MwebSrpUnif                                                  string `json:"mweb_srp_unif,omitempty"`
	ShowPreviousFilters                                          string `json:"show_previous_filters,omitempty"`
	LocUnif                                                      string `json:"loc_unif,omitempty"`
	DtHotelListingNew                                            string `json:"dt_hotel_listing_new,omitempty"`
	RegionAutosuggestResults                                     string `json:"regionAutosuggestResults,omitempty"`
	DetailsAltacco                                               string `json:"details_altacco,omitempty"`
	DsH2H                                                        string `json:"DS_H2H,omitempty"`
	DsShs                                                        string `json:"DS_SHS,omitempty"`
	UnifiedHourlyListingWeb                                      string `json:"unified_hourly_listing_web,omitempty"`
	Bnpl0                                                        string `json:"BNPL0,omitempty"`
	StreasVideo                                                  string `json:"streas_video,omitempty"`
	DwebReviewUnif                                               string `json:"dweb_review_unif,omitempty"`
	AaSkipSelectRoomExp                                          string `json:"aa_skip_select_room_exp,omitempty"`
	GiDhRanking                                                  string `json:"gi_dh_ranking,omitempty"`
	DwebDetailsIntlUnif                                          string `json:"dweb_details_intl_unif,omitempty"`
	ThankuRevamp                                                 string `json:"thanku_revamp,omitempty"`
	GiDiscNewUser                                                string `json:"giDisc_new_user,omitempty"`
	AutosuggestExp                                               string `json:"Autosuggest_exp,omitempty"`
	PricerIntlV2                                                 string `json:"pricerIntlV2,omitempty"`
	SecrecyFlow                                                  string `json:"secrecy_flow,omitempty"`
	DetailsSoldoutDweb                                           string `json:"DETAILS_SOLDOUT_DWEB,omitempty"`
	Gsdet                                                        string `json:"gsdet,omitempty"`
	DetailsPremMweb                                              string `json:"DETAILS_PREM_MWEB,omitempty"`
	NumLinesHtlDesc                                              string `json:"numLinesHtlDesc,omitempty"`
	PriceFilterConfig                                            string `json:"price_filter_config,omitempty"`
	UpsellMweb                                                   string `json:"upsell_mweb,omitempty"`
	DhNewtheme                                                   string `json:"dh_newtheme,omitempty"`
	UnifiedMainListingApp                                        string `json:"unified_main_listing_app,omitempty"`
	GiIosHotelDefaultDefaultDefaultFrequentUsedFilters           string `json:"gi.ios.hotel.default.default.default.frequent_used_filters,omitempty"`
	NewTcsFlow                                                   string `json:"newTcsFlow,omitempty"`
	Losbenefitsbackend                                           string `json:"losbenefitsbackend,omitempty"`
	DSSimilarHotelSearch                                         string `json:"DS_SimilarHotelSearch,omitempty"`
	Spkg                                                         string `json:"SPKG,omitempty"`
	NearbyFixes                                                  string `json:"nearbyFixes,omitempty"`
	DwebLogin                                                    string `json:"dweb_login,omitempty"`
	EnableMergedPropertyType                                     string `json:"enableMergedPropertyType,omitempty"`
	MultiroomDweb                                                string `json:"multiroom_dweb,omitempty"`
	GiAppHotelDefaultListingDefaultHostelListingPricing          string `json:"gi.app.hotel.default.listing.default.hostelListingPricing,omitempty"`
	DetailsBudgetDweb                                            string `json:"DETAILS_BUDGET_DWEB,omitempty"`
	DetailsAltaccoDweb                                           string `json:"DETAILS_ALTACCO_DWEB,omitempty"`
	XPercentSellOn                                               string `json:"xPercentSellOn,omitempty"`
	GoCashUni                                                    string `json:"goCash_uni,omitempty"`
	NewBookAt1                                                   string `json:"new_bookAt1,omitempty"`
	DetailsV2Budget                                              string `json:"details_v2_budget,omitempty"`
	NearMeExp                                                    string `json:"near_me_exp,omitempty"`
	UniFlow                                                      string `json:"uni_flow,omitempty"`
	MWebHotelsPrefillDates                                       string `json:"mWebHotelsPrefillDates,omitempty"`
	MwebDetailUIRevamp                                           string `json:"mweb_detail_ui_revamp,omitempty"`
	GiBackendHotelDefaultDefaultDefaultInnerRadiusVariantNew     string `json:"gi.backend.hotel.default.default.default.inner_radius_variant_new,omitempty"`
	SeoDetailsSoldoutRedirect                                    string `json:"seo_details_soldout_redirect,omitempty"`
	RankingHourlyLeisure                                         string `json:"RankingHourlyLeisure,omitempty"`
	UserRatingIh                                                 string `json:"user_rating_ih,omitempty"`
	BnplLoginPersuasion                                          string `json:"bnpl_login_persuasion,omitempty"`
	ShowFoodMenu                                                 string `json:"ShowFoodMenu,omitempty"`
	MwebNewLoginWidget                                           string `json:"mwebNewLoginWidget,omitempty"`
	AugurTestShradhaL2R                                          string `json:"augur_test_shradha_l2r,omitempty"`
	TCSBannerReview                                              string `json:"TCSBannerReview,omitempty"`
	DatelessDweb                                                 string `json:"dateless_dweb,omitempty"`
	HotjarMweb                                                   string `json:"hotjar_mweb,omitempty"`
	GsDetail                                                     string `json:"gs_detail,omitempty"`
	GiDiscBool                                                   string `json:"giDiscBool,omitempty"`
	PwaVideo                                                     string `json:"pwa_video,omitempty"`
	GiDiscTest27                                                 string `json:"giDisc_test27,omitempty"`
	GoSuggestUni                                                 string `json:"goSuggest_uni,omitempty"`
	TaxExperiment                                                string `json:"taxExperiment,omitempty"`
	NumItemsHtlDesc                                              string `json:"numItemsHtlDesc,omitempty"`
	AlternateDweb                                                string `json:"alternate_dweb,omitempty"`
	TcsV2OnOldApps                                               string `json:"TcsV2OnOldApps,omitempty"`
	AutosuggestDweb                                              string `json:"autosuggest_dweb,omitempty"`
	NearMeUni                                                    string `json:"nearMe_uni,omitempty"`
	HourD0                                                       string `json:"hourD0,omitempty"`
	HourlyFunnelPwa                                              string `json:"hourly_funnel_pwa,omitempty"`
	DHPriceFilter                                                string `json:"DH_Price_Filter,omitempty"`
	ByPassSoldOutCache                                           string `json:"byPassSoldOutCache,omitempty"`
	GiBackendHotelDefaultDefaultDefaultDvidpersonalization       string `json:"gi.backend.hotel.default.default.default.dvidpersonalization,omitempty"`
	GiAppHotelDefaultLandingDefaultBusinessIdentification        string `json:"gi.app.hotel.default.landing.default.business_identification,omitempty"`
	DetailsV2Soldout                                             string `json:"details_v2_soldout,omitempty"`
	BudgetGostay                                                 string `json:"budget_gostay,omitempty"`
	UnifiedUserRating                                            string `json:"unifiedUserRating,omitempty"`
	HotelImageDownsize                                           string `json:"hotel_image_downsize,omitempty"`
	SeoDtlSoldoutCalendarOpen                                    string `json:"seo_dtl_soldout_calendar_open,omitempty"`
	GiBackendHotelDefaultDefaultDefaultBnplPremiumManthan        string `json:"gi.backend.hotel.default.default.default.bnplPremiumManthan,omitempty"`
	Prefillfilter                                                string `json:"Prefillfilter,omitempty"`
	ExplicitIntentIH                                             string `json:"Explicit_Intent_IH,omitempty"`
	HotelDeeplinkEnabled                                         string `json:"hotel_deeplink_enabled,omitempty"`
	GiBackendHotelDefaultDefaultDefaultAerospikeEnabled          string `json:"gi.backend.hotel.default.default.default.aerospikeEnabled,omitempty"`
	MultiExpV2                                                   string `json:"multi_exp_v2,omitempty"`
	NewUserCoachmarks                                            string `json:"new_user_coachmarks,omitempty"`
	CheckInD0                                                    string `json:"checkInD0,omitempty"`
	SinglePoi1                                                   string `json:"single_poi_1,omitempty"`
	ShowTimeRange                                                string `json:"showTimeRange,omitempty"`
	GiPwaHotelDefaultListingDefaultPwaUnifiedFilters             string `json:"gi.pwa.hotel.default.listing.default.pwa_unified_filters,omitempty"`
	TestAugur                                                    string `json:"test_augur,omitempty"`
	GiBackendHotelDefaultDefaultDefaultMmRanking                 string `json:"gi.backend.hotel.default.default.default.mmRanking,omitempty"`
	ShowIndiannness                                              string `json:"showIndiannness,omitempty"`
	BankDealPwa                                                  string `json:"bank_deal_pwa,omitempty"`
	HourlyIntroBanner                                            string `json:"Hourly_intro_banner,omitempty"`
	DwebTransit                                                  string `json:"dweb_transit,omitempty"`
	CallSingularityGI                                            string `json:"callSingularityGI,omitempty"`
	ForceUpgrade                                                 string `json:"forceUpgrade,omitempty"`
	GILastViewed                                                 string `json:"GILastViewed,omitempty"`
	DdAPI                                                        string `json:"ddAPI,omitempty"`
	UnifiedHourlyListingApp                                      string `json:"unified_hourly_listing_app,omitempty"`
	PricerV2Pax                                                  string `json:"pricerV2Pax,omitempty"`
	AutofillAutosuggestHtl                                       string `json:"autofill_autosuggest_htl,omitempty"`
	LoggedoutWebBnpl                                             string `json:"loggedoutWebBnpl,omitempty"`
	GiBackendHotelDefaultDefaultDefaultEnhancedDspersGi          string `json:"gi.backend.hotel.default.default.default.enhanced_dspers_gi,omitempty"`
	UnifiedRankingGRPC                                           string `json:"unifiedRankingGRPC,omitempty"`
	EmptyShopSolution                                            string `json:"emptyShopSolution,omitempty"`
	GiBackendHotelDefaultDefaultDefaultIsSaleEnable              string `json:"gi.backend.hotel.default.default.default.isSaleEnable,omitempty"`
	HotelListingCardv2Autoscroll                                 string `json:"hotel_listing_cardv2_autoscroll,omitempty"`
	BnplV2                                                       string `json:"bnpl_v2,omitempty"`
	GiDesktopHotelDefaultDetailDefaultSeoDetailToListing         string `json:"gi.desktop.hotel.default.detail.default.SeoDetailToListing,omitempty"`
	HourlyHotelsFunnel                                           string `json:"hourly_hotels_funnel,omitempty"`
	UtmMediumsRestrict                                           string `json:"utm_mediums_restrict,omitempty"`
	DetailsAltaccoMweb                                           string `json:"DETAILS_ALTACCO_MWEB,omitempty"`
	KidPrice                                                     string `json:"kid_price,omitempty"`
	Qb1Room                                                      string `json:"qb_1_room,omitempty"`
	MinCheckin                                                   string `json:"minCheckin,omitempty"`
	GoTribe3                                                     string `json:"goTribe3,omitempty"`
	DetailsPrem                                                  string `json:"details_prem,omitempty"`
	DetailsBudgetMwebF                                           string `json:"DETAILS_BUDGET_MWEB_F,omitempty"`
	FeaturedReviews                                              string `json:"featured_reviews,omitempty"`
	ThankuRevamp2                                                string `json:"thanku_revamp2,omitempty"`
	DetailsSoldoutMweb                                           string `json:"DETAILS_SOLDOUT_MWEB,omitempty"`
	ThankuRevamp1                                                string `json:"thanku_revamp1,omitempty"`
	GiBackendHotelDefaultListingDefaultSeolistingScrollLoad      string `json:"gi.backend.hotel.default.listing.default.seolisting_scroll_load,omitempty"`
	DsH2HSimilarityAlgoID                                        string `json:"dsH2HSimilarityAlgoId,omitempty"`
	StreaksVideo                                                 string `json:"streaks_video,omitempty"`
	AppUpgrade                                                   string `json:"appUpgrade,omitempty"`
	IosTransitionExperiment                                      string `json:"ios_transition_experiment,omitempty"`
	CharityPreselect                                             string `json:"charity_preselect,omitempty"`
	GiDhNewuserDiscount                                          string `json:"gi_dh_newuser_discount,omitempty"`
	GiBackendHotelDefaultDetailDefaultPremiumUsp                 string `json:"gi.backend.hotel.default.detail.default.premiumUsp,omitempty"`
	GiBackendHotelDefaultDefaultDefaultGiTopFilterCard           string `json:"gi.backend.hotel.default.default.default.gi_top_filter_card,omitempty"`
	NewUserReview                                                string `json:"new_user_review,omitempty"`
	DetailsBudgetMweb                                            string `json:"DETAILS_BUDGET_MWEB,omitempty"`
	DisableBillingDweb                                           string `json:"disable_billing_dweb,omitempty"`
	UpsellDweb                                                   string `json:"upsell_dweb,omitempty"`
	Icv2                                                         string `json:"ICV2,omitempty"`
	FilterV2                                                     string `json:"filterV2,omitempty"`
	MmtBackendHotelDefaultDefaultDefaultTcsReviewPageFlow        string `json:"mmt.backend.hotel.default.default.default.tcsReviewPageFlow,omitempty"`
	DetailsPremDweb                                              string `json:"DETAILS_PREM_DWEB,omitempty"`
	HermesHeroImage                                              string `json:"hermes_hero_image,omitempty"`
	BankDealDweb                                                 string `json:"bank_deal_dweb,omitempty"`
	HotelsCharityShow                                            string `json:"Hotels_charity_show,omitempty"`
	PromocodePwa                                                 string `json:"promocode_pwa,omitempty"`
	LightWeightBizcrawl                                          string `json:"light_weight_bizcrawl,omitempty"`
	Streetview                                                   string `json:"streetview,omitempty"`
	AutosuggestChild                                             string `json:"autosuggest_child,omitempty"`
	ShowInsWidget                                                string `json:"showInsWidget,omitempty"`
	AugurTestDefaultonly                                         string `json:"augur_test_defaultonly,omitempty"`
	Mw2AHotelsBottomsheetTypage                                  string `json:"Mw2a_hotels_bottomsheet_typage,omitempty"`
	VoyagerLightweight                                           string `json:"voyager_lightweight,omitempty"`
	GiPwaHotelDefaultDefaultDefaultMwebDayuseDetailsUnif         string `json:"gi.pwa.hotel.default.default.default.mweb_dayuse_details_unif,omitempty"`
	UnifiedHotelLandingApp                                       string `json:"unified_hotel_landing_app,omitempty"`
	CentralizedCache                                             string `json:"centralized_cache,omitempty"`
	DhNewthemeBE                                                 string `json:"dh_newtheme_BE,omitempty"`
	GiPwaHotelDefaultDetailDefaultPropLayoutPwa                  string `json:"gi.pwa.hotel.default.detail.default.propLayoutPwa,omitempty"`
	GiAppHotelDefaultDetailDefaultHostelDetailsPricing           string `json:"gi.app.hotel.default.detail.default.hostelDetailsPricing,omitempty"`
	BnplZeroVariant                                              string `json:"bnplZeroVariant,omitempty"`
	AutosuggestMweb                                              string `json:"autosuggest_mweb,omitempty"`
	AapropertyConfig                                             string `json:"aaproperty_config,omitempty"`
	ValueOverview                                                string `json:"valueOverview,omitempty"`
	MwebReviewUnif                                               string `json:"mweb_review_unif,omitempty"`
	PwaLogin                                                     string `json:"pwa_login,omitempty"`
	CollectionUnif                                               string `json:"collection_unif,omitempty"`
	CollectionUni                                                string `json:"collection_uni,omitempty"`
	GiBackendHotelDefaultDefaultDefaultWalletExpIh               string `json:"gi.backend.hotel.default.default.default.wallet_exp_ih,omitempty"`
	HermesBnplCheckin                                            string `json:"hermes_bnpl_checkin,omitempty"`
	SrpRevampBe                                                  string `json:"srp_revamp_be,omitempty"`
	ExternalRating                                               string `json:"external_rating,omitempty"`
	LosBenefits                                                  string `json:"losBenefits,omitempty"`
	CpPreApply                                                   string `json:"cpPreApply,omitempty"`
	GiBackendHotelDefaultDefaultDefaultHostelFunnelEnabled       string `json:"gi.backend.hotel.default.default.default.hostel_funnel_enabled,omitempty"`
	Gidhl2R                                                      string `json:"GIDHL2R,omitempty"`
	HydraforCF                                                   string `json:"hydraforCF,omitempty"`
	HotelsCharityAmount                                          string `json:"hotels_charity_amount,omitempty"`
	DwebNewPayment                                               string `json:"dweb_new_payment,omitempty"`
	PdtLoggingEnable                                             string `json:"pdtLoggingEnable,omitempty"`
	SrpCleanup                                                   string `json:"srp_cleanup,omitempty"`
	Qb1Room0                                                     string `json:"qb_1room,omitempty"`
	AugurShradhaTestdefault                                      string `json:"augur_shradha_testdefault,omitempty"`
	GiAppHotelDefaultLandingDefaultBusinessIdentificationEnabled string `json:"gi.app.hotel.default.landing.default.business_identification_enabled,omitempty"`
	AutosuggestratingDweb                                        string `json:"autosuggestrating_dweb,omitempty"`
	BlackRevamp                                                  string `json:"blackRevamp,omitempty"`
	AutosuggestExp1                                              string `json:"autosuggest_exp1,omitempty"`
	FoodDt                                                       string `json:"food_dt,omitempty"`
	Aastar                                                       string `json:"aastar,omitempty"`
	SecrecyHourly                                                string `json:"secrecy_hourly,omitempty"`
	GiDiscInt                                                    string `json:"giDiscInt,omitempty"`
	AutoSuggestSearchAnimation                                   string `json:"autoSuggestSearchAnimation,omitempty"`
	DwebVideos                                                   string `json:"dweb_videos,omitempty"`
	CrossRsMweb                                                  string `json:"cross_rs_mweb,omitempty"`
	HotelPerformanceMatrix                                       string `json:"hotel_performance_matrix,omitempty"`
	FlexibleCheckin                                              string `json:"flexible_checkin,omitempty"`
	BusinessIdentifyEnable                                       string `json:"business_identify_enable,omitempty"`
	FoodPwa                                                      string `json:"food_pwa,omitempty"`
	Streaksvideo                                                 string `json:"streaksvideo,omitempty"`
	GiHotelLandingNew                                            string `json:"gi_hotel_landing_new,omitempty"`
	PricerV2                                                     string `json:"pricerV2,omitempty"`
	ShowDisabledBnplDetails                                      string `json:"showDisabledBnplDetails,omitempty"`
	EnableGr                                                     string `json:"enable_gr,omitempty"`
	MetaSkipRS                                                   string `json:"Meta_skipRS,omitempty"`
	DwebDetailsUnif                                              string `json:"dweb_details_unif,omitempty"`
	AreaSearch                                                   string `json:"area_search,omitempty"`
	PwaRecentSearch                                              string `json:"pwa_recent_search,omitempty"`
	LastUsedFilterSheet                                          string `json:"lastUsedFilterSheet,omitempty"`
	UpsellReview                                                 string `json:"upsell_review,omitempty"`
	BnplLogoutExp                                                string `json:"bnpl_logout_exp,omitempty"`
	GiAppHotelDefaultListingDefaultHostelLandingPricing          string `json:"gi.app.hotel.default.listing.default.hostelLandingPricing,omitempty"`
	UnificationSrpFilterV2MasterResponse                         string `json:"unificationSrpFilterV2MasterResponse,omitempty"`
	GiPwaHotelDefaultListingDefaultAppDownloadPopup              string `json:"gi.pwa.hotel.default.listing.default.AppDownloadPopup,omitempty"`
	TestSIBIAA                                                   string `json:"test_SIBI_AA,omitempty"`
	LoggedoutWebBnpl0                                            string `json:"loggedout_web_bnpl,omitempty"`
	QnaExpMweb                                                   string `json:"qna_exp_mweb,omitempty"`
	AutosuggestNewHotstore                                       string `json:"autosuggest_new_hotstore,omitempty"`
	DetailsV2Altacco                                             string `json:"details_v2_altacco,omitempty"`
	HourlyDetailUnification                                      string `json:"hourlyDetailUnification,omitempty"`
	GidiscNewuser                                                string `json:"gidisc_newuser,omitempty"`
	AmenitiesPersonalization                                     string `json:"amenities_personalization,omitempty"`
	UnifiedMainListingWeb                                        string `json:"unified_main_listing_web,omitempty"`
	NewToDh                                                      string `json:"new_to_dh,omitempty"`
	HourlyMweb2AppPwa                                            string `json:"hourly_mweb2app_pwa,omitempty"`
	DwebTransitIh                                                string `json:"dweb_transit_ih,omitempty"`
	Dspersv1Newprice                                             string `json:"dspersv1_newprice,omitempty"`
	DisableDonation                                              string `json:"DisableDonation,omitempty"`
	FlexiNew                                                     string `json:"flexi_new,omitempty"`
	DatelessPwa                                                  string `json:"dateless_pwa,omitempty"`
	FlyerWebLoggout                                              string `json:"flyer_web_loggout,omitempty"`
	ExplicitIntentNewUser                                        string `json:"explicit_intent_new_user,omitempty"`
	HUnifAutosMweb                                               string `json:"h_unif_autos_mweb,omitempty"`
	SinglePoi                                                    string `json:"single_poi,omitempty"`
	AutoSuggestRevampV2                                          string `json:"autoSuggestRevampV2,omitempty"`
	BeUpsellReviewHotel                                          string `json:"be_upsell_review_hotel,omitempty"`
	PwaVerifyOtpNewWidget                                        string `json:"pwa_verify_otp_new_widget,omitempty"`
	RegionAutosuggestResultsDweb                                 string `json:"regionAutosuggestResultsDweb,omitempty"`
	HotelRecentSearchAPI                                         string `json:"hotel_recent_search_api,omitempty"`
	GetawaysUni                                                  string `json:"getaways_uni,omitempty"`
	SecrecyMain                                                  string `json:"secrecy_main,omitempty"`
	GiBackendHotelDefaultDefaultDefaultRtbwindowcheck            string `json:"gi.backend.hotel.default.default.default.rtbwindowcheck,omitempty"`
	Similarprop                                                  string `json:"similarprop,omitempty"`
	Apeintl                                                      string `json:"APEINTL,omitempty"`
	UnificationDetailV2Dummy                                     string `json:"unificationDetailV2Dummy,omitempty"`
	Bpg                                                          string `json:"BPG,omitempty"`
	IHPriceFilter                                                string `json:"IH_Price_Filter,omitempty"`
	Dhqb                                                         string `json:"dhqb,omitempty"`
	GiDesktopHotelDefaultDefaultDefaultDwebAppBannerSticky       string `json:"gi.desktop.hotel.default.default.default.dweb_app_banner_sticky,omitempty"`
	HCompose                                                     string `json:"h_compose,omitempty"`
	ShowChildBed                                                 string `json:"showChildBed,omitempty"`
	ShowRSInlineFilter                                           string `json:"showRSInlineFilter,omitempty"`
	Pflow                                                        string `json:"pflow,omitempty"`
	RankingHourly                                                string `json:"RankingHourly,omitempty"`
	BookAt1UserRestriction                                       string `json:"book_at_1_user_restriction,omitempty"`
	CrossRsDweb                                                  string `json:"cross_rs_dweb,omitempty"`
	GiBackendHotelDefaultListingDefaultUnifiedRankingGRPC        string `json:"gi.backend.hotel.default.listing.default.unifiedRankingGRPC,omitempty"`
	CharityNew                                                   string `json:"charity_new,omitempty"`
	DetailsV2Prem                                                string `json:"details_v2_prem,omitempty"`
	WalletExp                                                    string `json:"wallet_exp,omitempty"`
	Rtbapwindowcheck                                             string `json:"rtbapwindowcheck,omitempty"`
	GetawayVisibility                                            string `json:"getaway_visibility,omitempty"`
	GiBackendHotelDefaultDefaultDefaultTest27                    string `json:"gi.backend.hotel.default.default.default.test27,omitempty"`
	GiBackendHotelDefaultDefaultDefaultPremiumDSD                string `json:"gi.backend.hotel.default.default.default.premiumDSD,omitempty"`
	SeoDtlSoRdrct                                                string `json:"seoDtlSoRdrct,omitempty"`
	GiBackendHotelDefaultDefaultDefaultPolarisGi                 string `json:"gi.backend.hotel.default.default.default.polaris_gi,omitempty"`
	GiPwaHotelDefaultListingDefaultPwaListingNewcard             string `json:"gi.pwa.hotel.default.listing.default.PwaListingNewcard,omitempty"`
	UnificationDetailV2                                          string `json:"unificationDetailV2,omitempty"`
	NewPolarisEnabled                                            string `json:"NewPolarisEnabled,omitempty"`
	MwebNewPaymentsPage                                          string `json:"mwebNewPaymentsPage,omitempty"`
	StreaksHermes                                                string `json:"streaks_hermes,omitempty"`
	HotelListingPrefetch                                         string `json:"hotel_listing_prefetch,omitempty"`
	WebAutosuggest                                               string `json:"web_autosuggest,omitempty"`
	HourlyTimesheet                                              string `json:"Hourly_timesheet,omitempty"`
	DetailsBudgetDwebF                                           string `json:"DETAILS_BUDGET_DWEB_F,omitempty"`
	HotelListingCardv2                                           string `json:"hotel_listing_cardv2,omitempty"`
	IsWindowBasedPagination                                      string `json:"isWindowBasedPagination,omitempty"`
	MultiroomMweb                                                string `json:"multiroom_mweb,omitempty"`
	GsdetIos                                                     string `json:"gsdet_ios,omitempty"`
	MwebDetailsUnif                                              string `json:"mweb_details_unif,omitempty"`
	CoupleFriendlyIntent                                         string `json:"Couple_Friendly_Intent,omitempty"`
	Maxchildage                                                  string `json:"maxchildage,omitempty"`
	QuickBook                                                    string `json:"quickBook,omitempty"`
	DwebPrefilldate                                              string `json:"dwebPrefilldate,omitempty"`
	AutosuggestratingMweb                                        string `json:"autosuggestrating_mweb,omitempty"`
	MwebShowPreviousFilters                                      string `json:"mweb_show_previous_filters,omitempty"`
	EnableLocationRecommendation                                 string `json:"enableLocationRecommendation,omitempty"`
	Alc                                                          string `json:"ALC,omitempty"`
	HtlInlineEnable                                              string `json:"htlInlineEnable,omitempty"`
	GocashPreApply                                               string `json:"gocashPreApply,omitempty"`
}
type Response struct {
	ListPersonalizationResponse ListPersonalizationResponse `json:"listPersonalizationResponse,omitempty"`
	CompletedRequests           []string                    `json:"completedRequests,omitempty"`
	CurrentTimeStamp            int                         `json:"currentTimeStamp,omitempty"`
	PagemakerResponse           PagemakerResponse           `json:"pagemakerResponse,omitempty"`
	ExpData                     ExpData                     `json:"expData,omitempty"`
	VariantKey                  string                      `json:"variantKey,omitempty"`
}
