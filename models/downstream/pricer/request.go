package pricer

import (
	"encoding/xml"
)

type PricerRequest struct {
	XMLName             xml.Name `xml:"MMTHotelSearchRequest"`
	Text                string   `xml:",chardata"`
	ExpectedTarrifCount string   `xml:"expectedTarrifCount"`
	POS                 struct {
		Text           string `xml:",chardata"`
		CorrelationKey string `xml:"CorrelationKey"`
		Requestor      struct {
			Text      string `xml:",chardata"`
			Channel   string `xml:"channel,attr"`
			IdContext string `xml:"idContext,attr"`
			Type      string `xml:"type,attr"`
		} `xml:"Requestor"`
		Source struct {
			Text          string `xml:",chardata"`
			ApplicationID string `xml:"applicationID,attr"`
			ISOCurrency   string `xml:"iSOCurrency,attr"`
		} `xml:"Source"`
		SourceIp string `xml:"SourceIp"`
	} `xml:"POS"`
	ResultTransformer struct {
		Text                       string `xml:",chardata"`
		GuestRecommendationEnabled struct {
			Text               string `xml:",chardata"`
			MaxRecommendations string `xml:"maxRecommendations,attr"`
		} `xml:"GuestRecommendationEnabled"`
		PriceBreakupEnabled string `xml:"PriceBreakupEnabled"`
	} `xml:"ResultTransformer"`
	SearchCriteria struct {
		Text      string `xml:",chardata"`
		Criterion struct {
			Text string `xml:",chardata"`
			Area struct {
				Text        string `xml:",chardata"`
				CityCode    string `xml:"CityCode"`
				CountryCode string `xml:"CountryCode"`
			} `xml:"Area"`
			HotelRef struct {
				Text      string `xml:",chardata"`
				IdContext string `xml:"idContext,attr"`
				ID        string `xml:"id,attr"`
			} `xml:"HotelRef"`
			MMTFreeCancellationOff string `xml:"MMTFreeCancellationOff"`
			RoomStayCandidates     struct {
				Text              string `xml:",chardata"`
				RoomStayCandidate struct {
					Text        string `xml:",chardata"`
					GuestCounts struct {
						Text       string `xml:",chardata"`
						GuestCount struct {
							Text              string `xml:",chardata"`
							AgeQualifyingCode string `xml:"ageQualifyingCode,attr"`
							Count             string `xml:"count,attr"`
						} `xml:"GuestCount"`
					} `xml:"GuestCounts"`
				} `xml:"RoomStayCandidate"`
			} `xml:"RoomStayCandidates"`
			StayDateRanges struct {
				Text          string `xml:",chardata"`
				StayDateRange struct {
					Text  string `xml:",chardata"`
					End   string `xml:"end,attr"`
					Start string `xml:"start,attr"`
				} `xml:"StayDateRange"`
			} `xml:"StayDateRanges"`
		} `xml:"Criterion"`
	} `xml:"SearchCriteria"`
}
