package singularity

type CrossSellRequest struct {
	DeviceId          string      `json:"deviceId"`
	BookingDevice     string      `json:"bookingDevice"`
	PEmailId          string      `json:"pEmailId"`
	VisitorId         string      `json:"visitorId"`
	UUID              string      `json:"UUID"`
	PageContext       string      `json:"pageContext"`
	BookingId         string      `json:"bookingId"`
	ProfileType       string      `json:"profileType"`
	CorrelationKey    string      `json:"correlationKey"`
	BookingDetails    interface{} `json:"bookingDetails"`
	MmtAuth           string      `json:"mmtAuth"`
	NewApp            bool        `json:"newApp"`
	BookedCitySupport bool        `json:"isBookedCitySupport"`
}
