package singularity

import "Hotels-Scion/models/common"

type CardDetail struct {
	Heading string `json:"heading"`
	Hotel   []struct {
		HotelID         string                 `json:"hotelId"`
		ReasonToBelieve common.ReasonToBelieve `json:"reasonToBelieve"`
		MatchScore      float64                `json:"matchScore"`
		Tag             interface{}            `json:"tag"`
		SourceModel     string                 `json:"sourceModel"`
		SimilarHotels   interface{}            `json:"similar_hotels"`
	} `json:"hotel"`
	SearchContext common.SearchContext `json:"searchContext"`
	ViewMore      common.ViewMore      `json:"viewMore"`
	MoreDetails   string               `json:"moreDetails"`
}

type CrossSellResponse struct {
	Error       interface{}  `json:"error"`
	Status      string       `json:"status"`
	BookingIds  []string     `json:"bookingIds"`
	Heading     string       `json:"heading"`
	CardDetails []CardDetail `json:"cardDetails"`
}
