package clientbackend

type Summary struct {
	CumulativeRating float32 `json:"cumulativeRating"`
}

type HotelXsellStaticDetailResponse struct {
	HotelResult struct {
		Addr1      string   `json:"addr1"`
		Addr2      string   `json:"addr2"`
		Addr3      string   `json:"addr3"`
		ID         string   `json:"id"`
		Name       string   `json:"name"`
		StarRating int      `json:"starRating"`
		CityName   string   `json:"cityName"`
		Images     []string `json:"images"`
	} `json:"hotelResult"`
	FlyfishSummary map[string]Summary `json:"flyfishSummary"`
}

type CrossellStaticDataResponse struct {
	StaticDetailResponse map[string]HotelXsellStaticDetailResponse `json:"staticDetailResponse"`
	ErrorEntity          interface{}                               `json:"errorEntity"`
}
