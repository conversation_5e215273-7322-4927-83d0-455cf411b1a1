package clientbackend

type ResponseErrors struct {
	ErrorList []ResponseError `json:"errorList,omitempty"`
}

type ResponseError struct {
	ErrorCode    string `json:"errorCode"`
	ErrorMessage string `json:"errorMessage"`
}

type CouponInfo struct {
	CouponCode     string  `json:"couponCode"`
	Type           string  `json:"type"`
	Description    string  `json:"description"`
	DiscountAmount float64 `json:"discountAmount"`
}

type DisplayPriceBreakDown struct {
	DisplayPrice       float32    `json:"displayPrice"`
	NonDiscountedPrice float32    `json:"nonDiscountedPrice"`
	CDFDiscount        float32    `json:"cdfDiscount"`
	CouponInfo         CouponInfo `json:"couponInfo"`
}

type DisplayFare struct {
	DisplayPriceBreakDown DisplayPriceBreakDown `json:"displayPriceBreakDown"`
}

type SearchHotelEntity struct {
	ID          string      `json:"id"`
	Name        string      `json:"name"`
	DisplayFare DisplayFare `json:"displayFare"`
}

type SearchHotelsResponse struct {
	StaticHotelCounts     int                 `json:"staticHotelCounts"`
	TotalHotelCounts      int                 `json:"totalHotelCounts"`
	NoMoreAvailableHotels bool                `json:"noMoreAvailableHotels"`
	HotelList             []SearchHotelEntity `json:"hotelList"`
	ResponseErrors        ResponseErrors      `json:"responseErrors"`
	CorrelationKey        string              `json:"correlationKey"`
	Currency              string              `json:"currency"`
	CurrencyConvFactorINR int                 `json:"currencyConvFactorINR"`
}
