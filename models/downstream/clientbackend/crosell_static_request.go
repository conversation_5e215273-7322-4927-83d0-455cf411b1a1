package clientbackend

type ImageCategory struct {
	Category string `json:"category"`
	Count    int    `json:"count"`
	Height   int    `json:"height"`
	Width    int    `json:"width"`
}

type CrossellStaticDataRequest struct {
	CityID          string          `json:"cityId"`
	HotelIds        []string        `json:"hotelIds"`
	ResponseFilters []string        `json:"responseFilters"`
	ImageCategories []ImageCategory `json:"imageCategories"`
}
