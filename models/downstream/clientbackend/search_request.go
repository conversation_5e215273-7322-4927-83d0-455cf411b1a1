package clientbackend

import (
	"Hotels-Scion/models/common"
)

type GuestCount struct {
	Count             string   `json:"count"`
	AgeQualifyingCode string   `json:"ageQualifyingCode"`
	Ages              []string `json:"ages"`
}

type RoomStayCandidates struct {
	GuestCounts []GuestCount `json:"guestCounts"`
}

type TrafficSource struct {
	Source string `json:"source"`
	Type   string `json:"type"`
}
type ResponseFilterFlags struct {
	PriceInfoReq           bool `json:"priceInfoReq"`
	BestCoupon             bool `json:"bestCoupon"`
	WalletRequired         bool `json:"walletRequired"`
	MmtPrime               bool `json:"mmtPrime"`
	FlyfishSummaryRequired bool `json:"flyfishSummaryRequired"`
	CheckAvailibility      bool `json:"checkAvailibility"`
	DoubleBlackUser        bool `json:"doubleBlackUser"`
	BlackUser              bool `json:"blackUser"`
	NewCorp                bool `json:"newCorp"`
	Locus                  bool `json:"locus"`
	CityTaxExclusive       bool `json:"cityTaxExclusive"`
	PersuasionRequired     bool `json:"persuasionRequired,omitempty"`
	ThemesCardRequired     bool `json:"premiumThemesCardRequired,omitempty"`
	PersuasionsEngineHit   bool `json:"persuasionsEngineHit,omitempty"`
}

type SearchHotelsRequest struct {
	Brand                 string                                   `json:"brand,omitempty"`
	MmtAuth               string                                   `json:"mmtAuth,omitempty"`
	RequestType           string                                   `json:"requestType,omitempty"`
	IDContext             string                                   `json:"idContext,omitempty"`
	DeviceID              string                                   `json:"deviceId,omitempty"`
	Channel               string                                   `json:"channel,omitempty"`
	DeviceType            string                                   `json:"deviceType,omitempty"`
	AppVersion            string                                   `json:"appVersion,omitempty"`
	ExpiryRequired        bool                                     `json:"expiryRequired,omitempty"`
	PageContext           string                                   `json:"pageContext,omitempty"`
	CommonPageContext     string                                   `json:"commonPageContext,omitempty"`
	CityCode              string                                   `json:"cityCode,omitempty"`
	CountryCode           string                                   `json:"countryCode,omitempty"`
	BookingDevice         string                                   `json:"bookingDevice,omitempty"`
	Checkin               string                                   `json:"checkin,omitempty"`
	Checkout              string                                   `json:"checkout,omitempty"`
	LoggedIn              bool                                     `json:"loggedIn"`
	RoomStayCandidates    []RoomStayCandidates                     `json:"roomStayCandidates,omitempty"`
	ResponseFilterFlags   ResponseFilterFlags                      `json:"responseFilterFlags,omitempty"`
	NumberOfCoupons       int                                      `json:"numberOfCoupons"`
	NumberOfAddons        int                                      `json:"numberOfAddons"`
	VisitorID             string                                   `json:"visitorId,omitempty"`
	Limit                 int                                      `json:"limit"`
	ImageCategory         []ImageCategory                          `json:"imageCategory,omitempty"`
	ImageType             []string                                 `json:"imageType,omitempty"`
	ExperimentData        string                                   `json:"experimentData,omitempty"`
	CorrelationKey        string                                   `json:"correlationKey,omitempty"`
	FirstTimeUser         bool                                     `json:"firstTimeUser"`
	GreenHornNewHotelFlag bool                                     `json:"greenHornNewHotelFlag"`
	ProfileType           string                                   `json:"profileType,omitempty"`
	HotelIDList           []string                                 `json:"hotelIdList,omitempty"`
	AdvancedFiltering     bool                                     `json:"advancedFiltering"`
	TrafficSource         TrafficSource                            `json:"trafficSource,omitempty"`
	WizardUser            bool                                     `json:"wizardUser"`
	TrendingNow           bool                                     `json:"trendingNow"`
	CollectionRequired    bool                                     `json:"collectionRequired"`
	UUID                  string                                   `json:"UUID,omitempty"`
	AthenaCategory        string                                   `json:"athenaCategory,omitempty"`
	Currency              string                                   `json:"currency,omitempty"`
	CdfContextID          string                                   `json:"cdfContextId,omitempty"`
	LocationID            string                                   `json:"locationId,omitempty"`
	LocationType          string                                   `json:"locationType,omitempty"`
	AppliedFilterMap      *map[common.Filter_Group][]common.Filter `json:"appliedFilterMap,omitEmpty"`
	MatchMakerRequest     *MatchMakerRequest                       `json:"matchMakerRequest,omitempty"`
	FlyfishSummaryRequest FlyfishSummaryRequest                    `json:"flyfishSummaryRequest,omitempty"`
	Requester             string                                   `json:"requester,omitempty"`
	NumberOfSoldOuts      int                                      `json:"numberOfSoldOuts,omitempty"`
	SiteDomain            string                                   `json:"siteDomain,omitempty"`
	FunnelSource          string                                   `json:"funnelSource,omitempty"`
	NoOfPersuasions       int                                      `json:"noOfPersuasions,omitempty"`
	SecureURL             string                                   `json:"secureURL,omitempty"`
	ImageCount            int                                      `json:"imageCount,omitempty"`
	Latitude              float32                                  `json:"latitude,omitempty"`
	Longitude             float32                                  `json:"longitude,omitempty"`
	PersonalizedSearch    bool                                     `json:"personalizedSearch,omitempty"`
	CardId                string                                   `json:"cardId,omitempty"`
	UserID                string                                   `json:"userId,omitempty"`
	Domain                string                                   `json:"domain,omitempty"`
	RecommendedPlaces     []common.RecommendedPlaces               `json:"recommendedPlaces,omitempty"`
	SelectedTabId         string                                   `json:"selectedTabId,omitempty"`
	UserSegments          []string                                 `json:"userSegments,omitempty"`
	CityName              string                                   `json:"cityName,omitempty"`
}

type SearchPersonalizedHotelsRequest struct {
	UserID                  string                                   `json:"userId,omitempty"`
	RequestType             string                                   `json:"requestType,omitempty"`
	Domain                  string                                   `json:"domain,omitempty"`
	IDContext               string                                   `json:"idContext,omitempty"`
	DeviceID                string                                   `json:"deviceId,omitempty"`
	Channel                 string                                   `json:"channel,omitempty"`
	DeviceType              string                                   `json:"deviceType,omitempty"`
	AppVersion              string                                   `json:"appVersion,omitempty"`
	PageContext             string                                   `json:"pageContext,omitempty"`
	CityCode                string                                   `json:"cityCode,omitempty"`
	CountryCode             string                                   `json:"countryCode,omitempty"`
	BookingDevice           string                                   `json:"bookingDevice,omitempty"`
	Checkin                 string                                   `json:"checkin,omitempty"`
	Checkout                string                                   `json:"checkout,omitempty"`
	LoggedIn                bool                                     `json:"loggedIn"`
	RoomStayCandidates      []RoomStayCandidates                     `json:"roomStayCandidates,omitempty"`
	ResponseFilterFlags     ResponseFilterFlags                      `json:"responseFilterFlags,omitempty"`
	NumberOfCoupons         int                                      `json:"numberOfCoupons"`
	VisitorID               string                                   `json:"visitorId,omitempty"`
	Limit                   int                                      `json:"limit"`
	ImageCategory           []ImageCategory                          `json:"imageCategory,omitempty"`
	ImageType               []string                                 `json:"imageType,omitempty"`
	ExperimentData          string                                   `json:"experimentData,omitempty"`
	CorrelationKey          string                                   `json:"correlationKey,omitempty"`
	FirstTimeUser           bool                                     `json:"firstTimeUser"`
	IsGreenHornNewHotelFlag bool                                     `json:"isGreenHornNewHotelFlag"`
	ProfileType             string                                   `json:"profileType,omitempty"`
	AdvancedFiltering       bool                                     `json:"advancedFiltering"`
	TrafficSource           TrafficSource                            `json:"trafficSource,omitempty"`
	Currency                string                                   `json:"currency,omitempty"`
	CdfContextID            string                                   `json:"cdfContextId,omitempty"`
	LocationID              string                                   `json:"locationId,omitempty"`
	LocationType            string                                   `json:"locationType,omitempty"`
	AppliedFilterMap        *map[common.Filter_Group][]common.Filter `json:"appliedFilterMap,omitEmpty"`
	MatchMakerRequest       *MatchMakerRequest                       `json:"matchMakerRequest,omitempty"`
	Requester               string                                   `json:"requester,omitempty"`
	SecureURL               string                                   `json:"secureURL,omitempty"`
	ImageCount              int                                      `json:"imageCount,omitempty"`
	FlyfishSummaryRequest   FlyfishSummaryRequest                    `json:"flyfishSummaryRequest,omitempty"`
	FunnelSource            string                                   `json:"funnelSource,omitempty"`
}

type FlyfishSummaryRequest struct {
	Filter struct {
		Otas       []string   `json:"otas,omitempty"`
		SubConcept SubConcept `json:"subConcept,omitempty"`
	} `json:"filter,omitempty"`
}

type SubConcept struct {
	TagTypes []string `json:"tagTypes,omitempty"`
}

type MatchMakerRequest struct {
	SelectedTags []Tag        `json:"selectedTags,omitempty"`
	LatLng       []LatLng     `json:"latLng,omitempty"`
	Hotels       []InputHotel `json:"hotels,omitempty"`
}

type InputHotel struct {
	HotelID string `json:"hotelId,omitempty"`
}

type Tag struct {
	AreaID   string `json:"tagAreaId,omitempty"`
	AreaName string `json:"tagDescription,omitempty"`
}

type LatLng struct {
	Name  string `json:"name,omitempty"`
	PoiId string `json:"poiId,omitempty"`
}
