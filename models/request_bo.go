package models

type RequestBO struct {
	ServerIp               string
	DeviceId               string
	BookingDevice          string
	LoginEmailId           string
	VisitorId              string
	Uuid                   string
	PageContext            string
	BookingId              string
	ProfileType            string
	CorrelationKey         string
	CrossSellBookingDetail interface{}
	MmtAuth                string
	AppVersion             string
	Channel                string
	SkipValidation         bool
	IdContext              string
	RequestType            string
	DeviceType             string
	CrossSellUniqueIds     []string
	ExpData                ExperimentData
	ExpDataStr             string
	OSType                 string
	Locus                  bool
	LocationName           string
	LocationType           string
	Checkin                string
	Checkout               string
	AthenaCategory         string
	CollectionRequired     bool
	CardHeading            string
	TrendingNow            bool
	CountryCode			   string
}
