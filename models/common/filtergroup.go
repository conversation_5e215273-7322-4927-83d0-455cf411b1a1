package common

import "errors"

type Filter_Group string

const (
	STAR_RATING         = Filter_Group("STAR_RATING")
	PROPERTY_TYPE       = Filter_Group("PROPERTY_TYPE")
	USER_RATING         = Filter_Group("USER_RATING")
	HOTEL_PRICE         = Filter_Group("HOTEL_PRICE")
	MMT_OFFERING        = Filter_Group("MMT_OFFERING")
	HOTEL_PRICE_BUCKET  = Filter_Group("HOTEL_PRICE_BUCKET")
	DRIVING_DISTANCE_KM = Filter_Group("DRIVING_DISTANCE_KM")
	HOTEL_CATEGORY      = Filter_Group("HOTEL_CATEGORY")
	PACKAGE_RATE        = Filter_Group("PACKAGE_RATE")
)

func (fg Filter_Group) IsValid() error {
	switch fg {
	case STAR_RATING, PROPERTY_TYPE, USER_RATING, HOTEL_PRICE, HOTEL_PRICE_BUCKET, MMT_OFFERING, DRIVING_DISTANCE_KM, HOTEL_CATEGORY, PACKAGE_RATE:
		return nil
	}
	return errors.New("Invalid filter group")
}
