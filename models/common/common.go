package common

type ReasonToBelieve []struct {
	ReasonToBook       string      `json:"reasonToBook"`
	Attribute          string      `json:"attribute"`
	UsedattributTagMap interface{} `json:"usedattributTagMap"`
}

type ViewMore struct {
	Title    string `json:"title"`
	SubTitle string `json:"subTitle"`
	DeepLink string `json:"deepLink"`
}

type RoomStayParams struct {
	GuestCounts []struct {
		Count             string `json:"count"`
		AgeQualifyingCode string `json:"ageQualifyingCode"`
		Ages              []int  `json:"ages"`
	} `json:"guestCounts"`
}

type LocusData struct {
	LocusId   string `json:"locusId"`
	LocusType string `json:"locusType"`
	LocusName string `json:"locusName"`
}
type SearchContext struct {
	CityCode       string           `json:"cityCode"`
	CityName       string           `json:"cityName"`
	CountryCode    string           `json:"countryCode"`
	Checkin        string           `json:"checkin"`
	Checkout       string           `json:"checkout"`
	BookedDeepLink interface{}      `json:"bookedDeepLink"`
	LocusData      LocusData        `json:"locusData"`
	IsBooked       bool             `json:"isBooked"`
	RoomStayParams []RoomStayParams `json:"roomStayParams"`
}

type FilterRange struct {
	MinValue int `json:"minValue" structs:"min_value"`
	MaxValue int `json:"maxValue,omitempty" structs:"max_value"`
}

type Filter struct {
	FilterGroup   Filter_Group `json:"filterGroup,omitempty" structs:"filter_group"`
	FilterValue   *string      `json:"filterValue,omitempty" structs:"filter_value"`
	FilterRange   *FilterRange `json:"filterRange,omitempty" structs:"filter_range"`
	IsRangeFilter bool         `json:"isRangeFilter,omitempty" structs:"-"`
}

type RecommendedPlaces struct {
	CityCode     string `json:"cityCode,omitempty"`
	AreaCode     string `json:"areaCode,omitempty"`
	AreaName     string `json:"areaName,omitempty"`
	CardType     string `json:"cardType,omitempty"`
	CountryCode  string `json:"countryCode,omitempty"`
	VisitorCount int64  `json:"visitorCount,omitempty"`
}
