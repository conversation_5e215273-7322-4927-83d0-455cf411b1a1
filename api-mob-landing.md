# Mobile Landing API

## Overview
The Mobile Landing API provides brand-aware mobile landing page data for hotel recommendations. It supports both MMT and GoIbibo brands with intelligent routing and mobile-optimized content delivery.

**Endpoint:** `POST /hotels-scion/api/v2/mob-landing`

## Architecture

### Type
- **Brand-aware mobile landing API** with request-response pattern
- **Pattern:** Request-Response with brand routing logic
- **Special Logic:** Brand-based routing (MMT vs GoIbibo)
- **Response:** Mobile-optimized landing page data

### Key Features
- Brand-based routing (MMT vs GoIbibo)
- Mobile-optimized content delivery
- Comprehensive request validation
- Feature flag support
- Device-specific optimizations

## Downstream Services

| Service | URL | Purpose |
|---------|-----|---------|
| **ClientGateway Mobile Landing (MMT)** | `http://hotels-clientgateway-oth.ecs.mmt/clientbackend/cg/mob-landing/%s/2` | MMT mobile landing data |
| **ClientGateway Mobile Landing (GoIbibo)** | `http://hotels-gi-clientgateway-oth.ecs.mmt/clientbackend-gi/cg/mob-landing/%s/2` | GoIbibo mobile landing data |
| **ClientGateway Search Hotels** | `http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/searchHotels` | Hotel search functionality |
| **HES Cards Data** | `http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/getCard` | Hotel card information |

## Request Format

### Headers
```
Content-Type: application/json
```

### Request Body
```json
{
  "deviceDetails": {
    "deviceId": "device-123",
    "deviceType": "ANDROID",
    "bookingDevice": "MOBILE",
    "appVersion": "8.5.1"
  },
  "expData": "exp_123",
  "requestDetails": {
    "requestor": "SCION",
    "requestId": "req-123",
    "channel": "MOBILE",
    "brand": "MMT",
    "region": "IN",
    "funnelSource": "HOTEL_LANDING",
    "idContext": "context-123",
    "profile": "PERSONAL",
    "pageContext": "HOTEL_LANDING",
    "visitorId": "visitor-123",
    "loggedIn": false,
    "visitNumber": 1,
    "premium": false,
    "cardIds": ["DAILYSTEALDEAL", "LUXE_CARD"]
  },
  "searchCriteria": {
    "checkIn": "2024-01-15",
    "checkOut": "2024-01-17",
    "countryCode": "IN",
    "cityCode": "CTBOM",
    "locationId": "CTBOM",
    "locationType": "city",
    "currency": "INR",
    "limit": 10,
    "personalizedSearch": false,
    "nearBySearch": false,
    "roomStayCandidates": [
      {
        "adultCount": 2,
        "rooms": 1,
        "childAges": []
      }
    ],
    "collectionCriteria": {
      "luxeCardRequired": false,
      "collectionRequired": false,
      "staticFilterCardsRequired": false,
      "inspiredCardsRequired": false,
      "valueStayCardsRequired": false,
      "athenaCategory": "string",
      "offbeatCitiesCardsRequired": false,
      "propertyTypeCards": false
    },
    "language": "eng",
    "vcId": "vc-123"
  },
  "requiredApis": {
    "cardRequired": true
  },
  "featureFlags": {
    "locus": false,
    "coupon": false,
    "bestCoupon": false
  },
  "correlationKey": "uuid-string"
}
```

## Response Format

### Headers
```
Content-Type: application/json
```

### Response Body
```json
{
  "response": {
    "correlationKey": "uuid-string",
    "cardData": [
      {
        "sequence": 1,
        "cardInfo": {
          "id": "DAILYSTEALDEAL",
          "cardPayload": {
            "hotelListNew": [
              {
                "id": "hotel-123",
                "giId": "gi-123",
                "name": "Taj Mahal Palace",
                "mtKey": "mt-123",
                "media": [
                  {
                    "url": "https://imgak.mmtcdn.com/hotels/123/image1.jpg",
                    "mediaType": "image"
                  }
                ],
                "priceDetail": {
                  "price": 15000.0,
                  "priceWithTax": 16500.0,
                  "discountedPrice": 12000.0,
                  "diffPercentage": 20.0,
                  "diffPercentageWithTax": 27.3,
                  "discountedPriceWithTax": 13200.0,
                  "totalTax": 1500.0,
                  "coupon": {
                    "code": "SAVE20",
                    "description": "Get 20% off on luxury stays",
                    "specialPromo": false,
                    "type": "PERCENTAGE",
                    "couponAmount": 3000.0,
                    "autoApplicable": true,
                    "bnplAllowed": false,
                    "disabled": false,
                    "bankOffer": false,
                    "noCostEmiApplicable": false
                  }
                },
                "appDeeplink": "mmyt://htl/listing/?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17",
                "hotelPersuasions": {
                  "CARD_LEFT_PANE_10": {
                    "data": [
                      {
                        "id": "persuasion-1",
                        "persuasionType": "OFFER",
                        "iconurl": "https://imgak.mmtcdn.com/icons/offer.png",
                        "text": "Limited time offer",
                        "hasAction": true,
                        "icontype": "icon",
                        "style": {
                          "textColor": "#FF6B6B"
                        },
                        "persuasionKey": "limited_offer",
                        "horizontal": false,
                        "html": false
                      }
                    ],
                    "placeholder": "",
                    "template": "persuasion_template",
                    "templateType": "HOTEL"
                  },
                  "locationDetail": {
                    "id": "CTBOM",
                    "name": "Mumbai",
                    "type": "city",
                    "countryId": "IN",
                    "countryName": "India"
                  },
                  "appDeeplink": "mmyt://htl/listing/?hotelId=hotel-123",
                  "seoUrl": "https://www.makemytrip.com/hotels/hotel-details?hotelId=hotel-123"
                },
                "tg": 1234567890,
                "gd": "gd-string"
              }
            ],
            "timerCard": {
              "bottomTitle": "Daily Steal Deal",
              "bottomSubtitle": "Limited time offer",
              "timerTextPrefix": "Offer ends in",
              "bgImageUrl": "https://imgak.mmtcdn.com/offers/daily-steal-bg.jpg",
              "timerRemaining": "23:59:59"
            },
            "viewAllCard": {
              "iconUrl": "https://imgak.mmtcdn.com/icons/view-all.png",
              "title": "View All Deals",
              "subTitle": "Explore more offers",
              "ctaTitle": "View All",
              "voyCityId": "CTBOM",
              "tg": 1234567890,
              "gd": "gd-string"
            },
            "genericCardData": [
              {
                "text": "Free Breakfast",
                "subText": "Complimentary breakfast included",
                "iconUrl": "https://imgak.mmtcdn.com/icons/breakfast.png",
                "duration": "Daily",
                "promoCode": "BREAKFAST"
              },
              {
                "text": "Free WiFi",
                "subText": "High-speed internet access",
                "iconUrl": "https://imgak.mmtcdn.com/icons/wifi.png",
                "duration": "24/7",
                "promoCode": "WIFI"
              }
            ],
            "locationData": [
              {
                "title": "Mumbai City Guide",
                "description": "Explore the best of Mumbai",
                "imageUrl": "https://imgak.mmtcdn.com/locations/mumbai-guide.jpg",
                "infoList": [
                  {
                    "iconUrl": "https://imgak.mmtcdn.com/icons/attraction.png",
                    "title": "Gateway of India"
                  },
                  {
                    "iconUrl": "https://imgak.mmtcdn.com/icons/food.png",
                    "title": "Local Cuisine"
                  }
                ],
                "videoUrl": "https://imgak.mmtcdn.com/videos/mumbai-guide.mp4",
                "thumbnailUrl": "https://imgak.mmtcdn.com/thumbnails/mumbai-guide.jpg",
                "deeplink": "mmyt://destination/mumbai"
              }
            ]
          }
        }
      }
    ]
  }
}
```

## Data Flow

```
1. Client Request
   ↓
2. Brand Detection
   ├── Check RequestDetails.Brand
   ├── Route to MMT Backend OR GoIbibo Backend
   └── Transform Request Accordingly
   ↓
3. Request Transformation
   ├── Set Request Details
   ├── Set Device Details
   ├── Set Search Criteria
   └── Set Feature Flags
   ↓
4. Downstream API Call
   ├── MMT Mobile Landing OR GoIbibo Mobile Landing
   ├── Process Response
   └── Transform to Scion Format
   ↓
5. Response Processing
   ├── Map Hotel Data
   ├── Process Card Payload
   └── Build Final Response
   ↓
6. Return Response to Client
```

## Brand Routing Logic

### MMT Brand
```go
if strings.EqualFold(brand, "MMT") {
    return getMMTMobLandingResponse(request, c)
} else {
    return getGoibiboMobLandingResponse(request, c)
}
```

### Brand-Specific Features
- **MMT:** Enhanced MMT-specific features and branding
- **GoIbibo:** GoIbibo-specific content and offers
- **Default:** Falls back to GoIbibo backend

## Error Handling

### Common Error Scenarios
- **Invalid Request:** Returns 400 Bad Request
- **Validation Error:** Returns validation message
- **Downstream Service Failure:** Returns 500 with error details
- **Brand Not Supported:** Falls back to default brand

### Error Response Format
```json
{
  "error": "string",
  "status": "ERROR",
  "message": "Error description"
}
```

### Validation Errors
```json
{
  "error": "Validation error: deviceDetails is required",
  "status": "ERROR",
  "message": "Invalid request format"
}
```

## Performance Considerations

### Optimization Features
- **Brand Caching:** Brand-specific configurations cached
- **Request Validation:** Early validation to fail fast
- **Response Transformation:** Optimized data mapping
- **Device Optimization:** Mobile-specific optimizations

### Timeouts
- **Downstream Service Timeout:** 30 seconds (configurable)
- **Request Processing Timeout:** 60 seconds
- **Validation Timeout:** 5 seconds

## Usage Examples

### cURL Example
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/v2/mob-landing \
  -H 'Content-Type: application/json' \
  -d '{
    "deviceDetails": {
      "deviceId": "device-123",
      "deviceType": "ANDROID",
      "bookingDevice": "MOBILE",
      "appVersion": "8.5.1"
    },
    "requestDetails": {
      "brand": "MMT",
      "channel": "MOBILE",
      "pageContext": "HOTEL_LANDING"
    },
    "searchCriteria": {
      "checkIn": "2024-01-15",
      "checkOut": "2024-01-17",
      "countryCode": "IN",
      "cityCode": "CTBOM",
      "currency": "INR",
      "roomStayCandidates": [{"adultCount": 2, "rooms": 1}]
    },
    "requiredApis": {
      "cardRequired": true
    },
    "correlationKey": "req-123"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/hotels-scion/api/v2/mob-landing', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    deviceDetails: {
      deviceId: 'device-123',
      deviceType: 'ANDROID',
      bookingDevice: 'MOBILE',
      appVersion: '8.5.1'
    },
    requestDetails: {
      brand: 'MMT',
      channel: 'MOBILE',
      pageContext: 'HOTEL_LANDING'
    },
    searchCriteria: {
      checkIn: '2024-01-15',
      checkOut: '2024-01-17',
      countryCode: 'IN',
      cityCode: 'CTBOM',
      currency: 'INR',
      roomStayCandidates: [{ adultCount: 2, rooms: 1 }]
    },
    requiredApis: {
      cardRequired: true
    },
    correlationKey: 'req-123'
  })
});

const data = await response.json();
console.log('Mobile Landing:', data);
```

## Monitoring and Logging

### Key Metrics
- **Response Time:** API processing time
- **Success Rate:** Percentage of successful requests
- **Error Rate:** Percentage of failed requests
- **Brand Distribution:** MMT vs GoIbibo request distribution
- **Device Type Distribution:** Android vs iOS usage

### Logging
- **Request Logging:** Full request payload with correlation key
- **Response Logging:** Response status and timing
- **Brand Logging:** Brand routing decisions
- **Error Logging:** Detailed error information with stack traces
- **Performance Logging:** Downstream service call timings

## Rate Limiting

### Limits
- **Requests per minute:** 500 (configurable)
- **Concurrent requests:** 50 (configurable)
- **Downstream service calls:** Based on service-specific limits

### Throttling
- **429 Too Many Requests:** When rate limit exceeded
- **Retry-After header:** Indicates when to retry
- **Graceful degradation:** Returns cached data when possible

## Security

### Authentication
- **Device ID:** Device identification for tracking
- **Correlation tracking:** UUID-based request correlation
- **Request validation:** Comprehensive input validation

### Data Protection
- **HTTPS only:** All communications encrypted
- **PII handling:** Sensitive data masked in logs
- **Input sanitization:** All inputs validated and sanitized

## Supported Card Types

### Mobile Landing Cards
- **DAILYSTEALDEAL:** Daily steal deals and offers
- **LUXE_CARD:** Luxury hotel recommendations
- **COLLECTION_CARD:** Curated hotel collections
- **LOCATION_CARD:** Location-based recommendations

### Card Features
- **Hotel Lists:** Curated hotel recommendations
- **Timer Cards:** Time-limited offers
- **View All Cards:** Navigation to full listings
- **Generic Card Data:** Feature highlights and benefits
- **Location Data:** Destination information and guides

## Device Optimizations

### Android Optimizations
- **App Deeplinks:** Native app navigation
- **Image Optimization:** WebP format support
- **Performance:** Optimized for Android rendering

### iOS Optimizations
- **App Deeplinks:** iOS-specific deeplinks
- **Image Optimization:** Optimized for iOS display
- **Performance:** iOS-specific performance tuning

### Common Features
- **Responsive Design:** Adaptive to different screen sizes
- **Touch Optimization:** Touch-friendly interface elements
- **Offline Support:** Cached content for offline viewing 