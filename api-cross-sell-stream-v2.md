# Cross-Sell Stream V2 API

## Overview
The Cross-Sell Stream V2 API provides enhanced streaming of hotel cross-selling recommendations with advanced features including rule engine support, benefit cards, and extended trip recommendations. It's an improved version of the original cross-sell stream API with additional capabilities.

**Endpoint:** `POST /hotels-scion/api/v2/cross-sell-stream`

## Architecture

### Type
- **Enhanced streaming API** with chunked transfer encoding
- **Pattern:** Advanced cross-sell with rule engine support
- **Special Features:** Rule engine integration, benefit cards, extended trip cards
- **Response:** V2 streaming cross-sell data with enhanced features

### Key Features
- Rule engine integration for intelligent recommendations
- Benefit cards with enhanced UI elements
- Extended trip recommendations
- Real-time data streaming
- Parallel processing of multiple search events
- Client disconnect detection
- Advanced filtering and persuasion logic

## Downstream Services

| Service | URL | Purpose |
|---------|-----|---------|
| **ClientGateway Search Hotels** | `http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/searchHotels` | Hotel search functionality |
| **ClientBackend Cross-sell Static Details** | `http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/xsell/staticDetail` | Static hotel details for cross-selling |
| **Singularity Recommendation Engine** | `http://hotels-singularity.ecs.mmt/Hotels-Singularity/v1/getHotelFromPriorBooking` | AI-powered recommendations |
| **Web API Cross-sell Drools** | `http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/crossSell` | Cross-sell rule engine |
| **Couchbase Cache** | `cb-42-70.mmt.mmt,cb-42-71.mmt.mmt,cb-42-72.mmt.mmt,cb-42-73.mmt.mmt` | Caching layer for performance |

## Request Format

### Headers
```
Content-Type: application/json
Transfer-Encoding: chunked
profileType: BUSINESS (optional)
```

### Request Body
```json
{
  "brand": "string",
  "context": {
    "contextId": "string",
    "experimentData": "string",
    "pageContext": "string",
    "scope": "string",
    "funnelSource": "string"
  },
  "secureUrl": "string",
  "imageCount": 0,
  "searchEvent": [
    {
      "cardId": "HOTEL_XSELL_BENEFITS",
      "templateId": "string",
      "componentId": "string",
      "limit": 0,
      "isFilter": false,
      "priority": 0,
      "hydraSegIds": ["string"],
      "meta": {},
      "sc": {
        "lob": "string",
        "lobCategory": "string",
        "fromDateTime": {
          "str": "2024-01-15",
          "ts": 1705276800,
          "zone": "Asia/Kolkata"
        },
        "toDateTime": {
          "str": "2024-01-17",
          "ts": 1705449600,
          "zone": "Asia/Kolkata"
        },
        "pax": [
          {
            "count": 2,
            "details": {
              "adult": {
                "ages": [25, 30],
                "count": 2
              },
              "child": {
                "ages": [],
                "count": 0
              },
              "infant": {
                "ages": [],
                "count": 0
              }
            }
          }
        ],
        "from": {
          "lobCity": "string",
          "locus": {
            "city": "string",
            "areaId": "string",
            "areaName": "string",
            "poiId": "string",
            "poiName": "string",
            "type": "city",
            "id": "CTBOM",
            "cityName": "Mumbai"
          },
          "cityName": "Mumbai",
          "countryName": "India",
          "countryCode": "IN",
          "longitude": 72.8777,
          "latitude": 19.076
        },
        "to": {
          "lobCity": "string",
          "locus": {
            "city": "string",
            "areaId": "string",
            "areaName": "string",
            "poiId": "string",
            "poiName": "string",
            "type": "city",
            "id": "CTDEL",
            "cityName": "Delhi"
          },
          "cityName": "Delhi",
          "countryName": "India",
          "countryCode": "IN",
          "longitude": 77.209,
          "latitude": 28.6139
        },
        "timestamp": 1705276800,
        "rooms": 1,
        "funnelSource": "string",
        "personalizedSearch": false,
        "product": {
          "id": "string",
          "name": "string"
        }
      },
      "selectedTabId": "string",
      "appliedFilterMap": {},
      "funnelActivity": {},
      "enrichments": {
        "trends": {
          "areas": [],
          "price_bucket": [],
          "ap_bucket": [],
          "los_bucket": [],
          "pax": [],
          "pd_id": [],
          "star_rating": [],
          "accommodation_type": [],
          "cities": []
        },
        "userPreferences": {}
      },
      "tags": {
        "edge_type": "string",
        "source": "string"
      },
      "filterList": [],
      "filter": false,
      "persuasionRequired": false,
      "recentlyViewedHotels": [],
      "isLandingDiscoveryPage": false,
      "recommendedPlaces": []
    }
  ],
  "nearBy": false,
  "offerRequired": false,
  "user": {
    "location": {
      "locationData": {
        "currentLocation": {
          "latitude": 19.076,
          "longitude": 72.8777
        }
      }
    }
  },
  "imageCategories": [],
  "correlationKey": "uuid-string",
  "metaInfo": {
    "userName": "string",
    "bookingId": "string"
  }
}
```

## Response Format

### Headers
```
Content-Type: application/json
Transfer-Encoding: chunked
```

### Response Body
```json
{
  "cardsV2": [
    {
      "cardDataV2": {
        "tabsData": {
          "HOTELS": {
            "hotels": [
              {
                "id": "hotel-123",
                "name": "Taj Mahal Palace",
                "images": [
                  "https://imgak.mmtcdn.com/hotels/123/image1.jpg",
                  "https://imgak.mmtcdn.com/hotels/123/image2.jpg"
                ],
                "starRating": 5,
                "userRating": 4.5,
                "currencyCode": "INR",
                "address": {
                  "line1": "Apollo Bunder",
                  "line2": "Mumbai, Maharashtra"
                },
                "displayFare": {
                  "displayPrice": 15000.0,
                  "nonDiscountedPrice": 18000.0
                },
                "appDeeplink": "mmyt://htl/listing/?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17",
                "desktopDeeplink": "https://www.makemytrip.com/hotels/hotel-details?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17"
              }
            ],
            "filters": [
              {
                "filterGroup": "PRICE_RANGE",
                "filterValue": "10000-20000",
                "title": "₹10000 - ₹20000",
                "rangeFilter": true
              }
            ],
            "persuasions": [
              {
                "text": "Get 15% off on luxury stays",
                "imageUrl": "https://imgak.mmtcdn.com/offers/luxury-offer.png"
              }
            ],
            "placeholders": [
              {
                "type": "HOTEL",
                "title": "More hotels coming soon",
                "description": "We're adding more recommendations"
              }
            ]
          }
        },
        "header": {
          "heading": "Recommended Hotels",
          "subheading": "Based on your preferences"
        },
        "ctaList": [
          {
            "title": "View All Hotels",
            "deeplink": "https://www.makemytrip.com/hotels/hotel-listing"
          }
        ],
        "benefitCardData": {
          "benefits": [
            {
              "title": "Free Breakfast",
              "description": "Complimentary breakfast included",
              "icon": "https://imgak.mmtcdn.com/icons/breakfast.png"
            },
            {
              "title": "Free WiFi",
              "description": "High-speed internet access",
              "icon": "https://imgak.mmtcdn.com/icons/wifi.png"
            },
            {
              "title": "Free Cancellation",
              "description": "Cancel up to 24 hours before check-in",
              "icon": "https://imgak.mmtcdn.com/icons/cancellation.png"
            }
          ]
        },
        "bgLinearGradient": {
          "startColor": "#FF6B6B",
          "endColor": "#4ECDC4"
        }
      }
    }
  ]
}
```

## Data Flow

```
1. Client Request
   ↓
2. Handler Processing
   ↓
3. For each SearchEvent:
   ├── Check Card Type
   ├── Route to Appropriate Service
   └── Process in Parallel
   ↓
4. Service Layer
   ├── Rule Engine (if applicable)
   ├── Cross-sell Service
   ├── ClientGateway APIs
   ├── Static Data Retrieval
   └── Price Data Processing
   ↓
5. V2 Response Processing
   ├── Enhanced Features
   ├── Benefit Cards
   ├── Linear Gradients
   └── Advanced UI Elements
   ↓
6. JSON Marshal
   ↓
7. Stream to Client
   ↓
8. Flush Response
```

## Card Type Routing

### Special Card Types
- **HOTEL_XSELL_BENEFITS:** Enhanced benefit cards with rule engine
- **HOTEL_EXTEND_YOUR_TRIP:** Extended trip recommendations
- **INTL_CASHBACK_CARD:** International cashback offers
- **GREAT_VALUE_PACKAGES_V2:** Value package deals

### Routing Logic
```go
if strings.EqualFold(sc.CardID, "HOTEL_XSELL_BENEFITS") ||
   strings.EqualFold(sc.CardID, "HOTEL_EXTEND_YOUR_TRIP") ||
   strings.EqualFold(sc.CardID, "INTL_CASHBACK_CARD") {
    // Use rule engine support
    go services.GetCrossSellV2StreamResponseWithRuleEngineSupport(...)
} else {
    // Use standard V2 response
    go services.GetCrossSellV2StreamResponse(...)
}
```

## Rule Engine Integration

### Rule Engine Features
- **Dynamic Pricing:** Real-time price optimization
- **Personalization:** User-specific recommendations
- **A/B Testing:** Experiment-based card selection
- **Business Rules:** Configurable business logic

### Rule Engine Request
```json
{
  "searchContext": {},
  "userProfile": {},
  "businessRules": {},
  "experimentData": "string"
}
```

## Error Handling

### Common Error Scenarios
- **Client Disconnect:** Detected via `http.CloseNotifier`
- **Invalid Request:** Returns 400 Bad Request
- **Downstream Service Failure:** Returns partial data with error indicators
- **Rule Engine Failure:** Falls back to standard processing
- **Timeout:** Configurable timeout for downstream calls

### Error Response Format
```json
{
  "cardsV2": [],
  "error": "string",
  "status": "ERROR",
  "message": "Error description"
}
```

## Performance Considerations

### Optimization Features
- **Parallel Processing:** Multiple search events processed concurrently
- **Caching:** Couchbase integration for frequently accessed data
- **Streaming:** Real-time data delivery without waiting for all results
- **Connection Management:** Efficient handling of client disconnections
- **Rule Engine Caching:** Cached rule engine results

### Timeouts
- **Downstream Service Timeout:** 30 seconds (configurable)
- **Rule Engine Timeout:** 15 seconds (configurable)
- **Client Connection Timeout:** Based on client configuration
- **Streaming Flush Interval:** Immediate after each response

## Usage Examples

### cURL Example
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/v2/cross-sell-stream \
  -H 'Content-Type: application/json' \
  -H 'Transfer-Encoding: chunked' \
  -d '{
    "brand": "MMT",
    "context": {
      "pageContext": "HOTEL_LANDING",
      "experimentData": "exp_123"
    },
    "searchEvent": [
      {
        "cardId": "HOTEL_XSELL_BENEFITS",
        "sc": {
          "from": {"locus": {"id": "CTBOM"}},
          "to": {"locus": {"id": "CTDEL"}},
          "fromDateTime": {"str": "2024-01-15"},
          "toDateTime": {"str": "2024-01-17"},
          "pax": [{"count": 2, "details": {"adult": {"count": 2, "ages": [25, 30]}}}]
        }
      }
    ],
    "correlationKey": "req-123"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/hotels-scion/api/v2/cross-sell-stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    brand: 'MMT',
    context: {
      pageContext: 'HOTEL_LANDING'
    },
    searchEvent: [{
      cardId: 'HOTEL_XSELL_BENEFITS',
      sc: {
        from: { locus: { id: 'CTBOM' } },
        to: { locus: { id: 'CTDEL' } },
        fromDateTime: { str: '2024-01-15' },
        toDateTime: { str: '2024-01-17' },
        pax: [{ count: 2, details: { adult: { count: 2, ages: [25, 30] } } }]
      }
    }],
    correlationKey: 'req-123'
  })
});

// Handle streaming response
const reader = response.body.getReader();
while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = new TextDecoder().decode(value);
  const data = JSON.parse(chunk);
  console.log('Cross-Sell V2:', data);
}
```

## Monitoring and Logging

### Key Metrics
- **Response Time:** Per search event processing time
- **Throughput:** Number of requests per second
- **Error Rate:** Percentage of failed requests
- **Rule Engine Performance:** Rule engine response times
- **Client Disconnect Rate:** Number of client disconnections

### Logging
- **Request Logging:** Full request payload with correlation key
- **Response Logging:** Response status and timing
- **Rule Engine Logging:** Rule engine decisions and performance
- **Error Logging:** Detailed error information with stack traces
- **Performance Logging:** Downstream service call timings

## Rate Limiting

### Limits
- **Requests per minute:** 1000 (configurable)
- **Concurrent connections:** 100 (configurable)
- **Rule Engine calls:** 500 per minute (configurable)
- **Downstream service calls:** Based on service-specific limits

### Throttling
- **429 Too Many Requests:** When rate limit exceeded
- **Retry-After header:** Indicates when to retry
- **Graceful degradation:** Returns cached data when possible

## Security

### Authentication
- **Header-based:** `profileType` header for user type
- **Correlation tracking:** UUID-based request correlation
- **Request validation:** Comprehensive input validation

### Data Protection
- **HTTPS only:** All communications encrypted
- **PII handling:** Sensitive data masked in logs
- **Input sanitization:** All inputs validated and sanitized

## Supported Card Types

### V2 Enhanced Cards
- **HOTEL_XSELL_BENEFITS:** Enhanced benefit cards with rule engine
- **HOTEL_EXTEND_YOUR_TRIP:** Extended trip recommendations
- **INTL_CASHBACK_CARD:** International cashback offers
- **GREAT_VALUE_PACKAGES_V2:** Value package deals

### Enhanced Features
- **Benefit Cards:** Detailed benefit information with icons
- **Linear Gradients:** Background gradient support
- **Advanced Filters:** Enhanced filtering capabilities
- **Dynamic Persuasions:** Real-time persuasion content
- **Rule Engine Integration:** Intelligent recommendation logic 