package restconnectors

import(
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/orchestrator"
	"Hotels-Scion/models/request/common"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
)

func GetAltAccoPropertyData(searchParams *common.SearchEvent, correlationKey string) orchestrator.AltAccoDataResponse {

	AltAccoDataRsp := orchestrator.AltAccoDataResponse{}
	req, _ := http.NewRequest("GET", config.AppConfig.URLs.WebApiAltAccoProperty, nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	countryCode := searchParams.CountryCode
	if len(countryCode) == 0{
		countryCode = config.PmsCommonConfig.DefaultCountryCode
	}
	cityCode := searchParams.CityCode
	if len(cityCode) == 0{
		cityCode = config.PmsCommonConfig.DefaultCityCode
	}
	deviceType := searchParams.DeviceType

	q:= req.URL.Query()
	q.Add("countryCode", countryCode)
	q.Add("cityCode", cityCode)
	q.Add("deviceType", deviceType)
	q.Add("correlationKey", correlationKey)
	req.URL.RawQuery = q.Encode()
	webApiHTTPClient := HTTPClientMap[constants.AltAccoPropertyAPI]
	resp, err := webApiHTTPClient.Do(req)

	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s",constants.AltAccoPropertyAPI, err.Error())
	}else{
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &AltAccoDataRsp)
	}
	return AltAccoDataRsp
}

func GetCityByLatLng(lat float32, lng float32) orchestrator.CityDetailsResponse {
	response := orchestrator.CityDetailsResponse{}
	req, _ := http.NewRequest("GET", config.AppConfig.URLs.WebApiCityDetails, nil)
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	q:= req.URL.Query()
	q.Add("lat", fmt.Sprintf("%f",lat))
	q.Add("lng", fmt.Sprintf("%f",lat))
	q.Add("locus","true")
	req.URL.RawQuery = q.Encode()
	webApiHTTPClient := HTTPClientMap[constants.AltAccoPropertyAPI]
	resp, err := webApiHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s",constants.CityDetailsApi, err.Error())
	}else{
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &response)
	}
	return response


}