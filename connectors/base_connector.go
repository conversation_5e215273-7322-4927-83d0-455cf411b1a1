package restconnectors

import (
	"Hotels-Scion/utils/logging"
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
)

var logger = logging.GetLogger()

type BaseConnector struct {
	POST (interface{})
}

func HttpPost(url string,apiName string, headers map[string]string, request *interface{}, response *interface{}) {

	bytesRepresentation, err := json.Marshal(request)

	req, _ := http.NewRequest("POST",url, bytes.NewBuffer(bytesRepresentation))

	for ky,val := range headers {
		req.Header.Set(ky, val)
	}

	cbHTTPClient := HTTPClientMap[apiName]
	resp, err := cbHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s",apiName, err.Error())
	}else{
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, response)
	}
}
