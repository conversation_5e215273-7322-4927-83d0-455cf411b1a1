package restconnectors

import (
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/clientbackend"
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
)

func PostCrosellStaticDataRequest(crossellStaticRequest *clientbackend.CrossellStaticDataRequest) clientbackend.CrossellStaticDataResponse {

	crossSellResponse := clientbackend.CrossellStaticDataResponse{}

	bytesRepresentation, err := json.Marshal(crossellStaticRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CBXsellStaticDetails, bytes.NewBuffer(bytesRepresentation))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	cbHTTPClient := HTTPClientMap[constants.CBXsellStaticHotelsAPI]
	resp, err := cbHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBXsellStaticHotelsAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &crossSellResponse)
	}

	return crossSellResponse
}

func PostSearchHotelsRequest(searchHotelsRequest *clientbackend.SearchHotelsRequest) clientbackend.SearchHotelsResponse {
	searchHotelsResponse := clientbackend.SearchHotelsResponse{}
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CBSearchHotels, bytes.NewBuffer(bytesRepresentation))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	cbHTTPClient := HTTPClientMap[constants.CBSearchHotelsAPI]
	resp, err := cbHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBSearchHotelsAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &searchHotelsResponse)
	}

	return searchHotelsResponse
}

func PostSearchHotelsRequestGeneric(b *[]byte) (*json.RawMessage, error) {

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CBSearchHotels, bytes.NewBuffer(*b))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	cbHTTPClient := HTTPClientMap[constants.CBSearchHotelsAPI]
	resp, err := cbHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBSearchHotelsAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		var rawJson *json.RawMessage
		json.Unmarshal(body, &rawJson)
		return rawJson, nil
	}

	return nil, err
}

func PostComparatorHotelsRequestGeneric(b *[]byte) (*json.RawMessage, error) {

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CBCommparator, bytes.NewBuffer(*b))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	cbHTTPClient := HTTPClientMap[constants.CBComparatorAPI]
	resp, err := cbHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBComparatorAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		var rawJson *json.RawMessage
		json.Unmarshal(body, &rawJson)
		return rawJson, nil
	}

	return nil, err
}

func PostFetchCollectionsRequest(searchHotelsRequest *clientbackend.SearchHotelsRequest) (*json.RawMessage, error) {
	var rawJson *json.RawMessage
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CBFetchCollections, bytes.NewBuffer(bytesRepresentation))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	cbHTTPClient := HTTPClientMap[constants.CBFetchCollectionsAPI]
	resp, err := cbHTTPClient.Do(req)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBFetchCollectionsAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		json.Unmarshal(body, &rawJson)
	}

	return rawJson, err
}
