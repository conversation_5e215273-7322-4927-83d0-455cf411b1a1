package restconnectors

import (
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/singularity"
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
)

func PostCrosellRequest(crossellRequest *singularity.CrossSellRequest) singularity.CrossSellResponse {

	crossSellResponse := singularity.CrossSellResponse{}

	bytesRepresentation, err := json.Marshal(crossellRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.SingularityCrossel, bytes.NewBuffer(bytesRepresentation))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	resp, err := HTTPClientMap[constants.SingularityAPI].Do(req)
	if err != nil {
		logger.Errorf("Error in getting singularity crosell response %v", err)
	}

	defer resp.Body.Close()
	body, _ := ioutil.ReadAll(resp.Body)

	_ = json.Unmarshal(body, &crossSellResponse)
	return crossSellResponse
}
