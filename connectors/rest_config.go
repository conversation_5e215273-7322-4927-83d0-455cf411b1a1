package restconnectors

import (
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"net"
	"net/http"
	"time"
)

var HTTPClientMap map[string]*http.Client

func InitHTTPClients() {

	HTTPClientMap = make(map[string]*http.Client)

	//custom and common dialer and transport
	dialer := &net.Dialer{
		Timeout: time.Duration(config.PmsCommonConfig.DialerTimeout) * time.Millisecond,
	}
	transport := &http.Transport{
		MaxIdleConnsPerHost: config.PmsCommonConfig.MaxIdleConnectionsPerHost,
		DialContext:         dialer.DialContext,
	}

	//create clients

	HTTPClientMap[constants.CBSearchHotelsAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CbSearchHotelsTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CBSearchPersonalizedHotels] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CbSearchPersonalizedHotelsTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CBXsellStaticHotelsAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CbXsellStaticHotelTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CBComparatorAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CbComparatorTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.SingularityAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.SingularityTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CBFetchCollectionsAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CBFetchCollectionsTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.AltAccoPropertyAPI] = &http.Client{
		Timeout: time.Duration(config.PmsCommonConfig.AltAccoConnectionTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CGXsellSearchContextHotelsAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CGSearchContextHotelTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CGSearchHotelsAPI] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CgSearchHotelsTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.CGLandingCardHotels] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.CgSearchHotelsTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.HesCardsData] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.HesCrossSellCardsDataTimeoutMillis) * time.Millisecond,
	}

	HTTPClientMap[constants.MobLanding] = &http.Client{
		Transport: transport,
		Timeout:   time.Duration(config.PmsCommonConfig.HesCrossSellCardsDataTimeoutMillis) * time.Millisecond,
	}
}
