package restconnectors

import (
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

type clientStruct struct{}

type ClientInterface interface {
	PostDroolsDataRequest(*request.CrossSellDrools) request.CrossSellDrools
	PostXSellSearchHotelsRequest(*clientbackend.SearchHotelsRequest, *gin.Context) clientgateway.CrossSellSearchHotelsData
	PostCrossSellCardsData(*clientbackend.SearchHotelsRequest, *gin.Context) clientgateway.CrossSellCardData
	PostXSellSearchHotelsRequestAsync(*clientbackend.SearchHotelsRequest, *gin.Context, chan clientgateway.CrossSellSearchHotelsData)
	PostXSellSearchPersonalizedRequestCorp(*clientbackend.SearchPersonalizedHotelsRequest, *gin.Context) clientgateway.CrossSellSearchPersonalizedData
	PostFetchCollectionRequest(*request.FetchCollectionRequestCG, *gin.Context) (response.FetchCollectionResponseCG, error)
	PostXSellLandingDiscoveryRequest(*clientbackend.SearchHotelsRequest, *gin.Context) clientgateway.CrossSellSearchHotelsData
	PostXSellLandingDiscoveryRequestAsync(*clientbackend.SearchHotelsRequest, *gin.Context) clientgateway.CrossSellSearchHotelsData
	PostMobLandingRequest(landingRequest *clientbackend.MobLandingRequest, ctx *gin.Context) clientgateway.HotelsMobLandingResponse
	PostMMTMobLandingRequest(landingRequest *clientbackend.MobLandingRequest, ctx *gin.Context) clientgateway.HotelsMobLandingResponse
}

var (
	ClientStruct ClientInterface = &clientStruct{}
)

// PostDroolsDataRequest : this is the connector for Drools Details
func (ci *clientStruct) PostDroolsDataRequest(crossellSearchContextRequest *request.CrossSellDrools) request.CrossSellDrools {

	crossSellResponse := request.CrossSellDrools{}

	bytesRepresentation, err := json.Marshal(crossellSearchContextRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CGSearchContextDrools, bytes.NewBuffer(bytesRepresentation))
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")

	cgHTTPClient := HTTPClientMap[constants.CGXsellSearchContextHotelsAPI]
	resp, err := cgHTTPClient.Do(req)
	logger.Debugf("DroolsDataRequest %s", string(bytesRepresentation))
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CGXsellSearchContextHotelsAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &crossSellResponse)
		logger.Debugf("DroolsDataResponse %s", string(body))
	}
	return crossSellResponse
}

func (ci *clientStruct) PostXSellSearchHotelsRequest(searchHotelsRequest *clientbackend.SearchHotelsRequest, c *gin.Context) clientgateway.CrossSellSearchHotelsData {
	searchHotelsResponse := clientgateway.CrossSellSearchHotelsData{}
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	url := config.AppConfig.URLs.CGSearchHotels
	if searchHotelsRequest.Brand == "GI" {
		url = config.AppConfig.URLs.CGGiSearchHotels
	}

	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(bytesRepresentation))
	//set required headers
	auth := c.GetHeader("mmt-auth")
	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") {
			if len(auth) != 0 {
				req.Header["backup_auth"] = []string{"mmtAuth=" + "\"" + auth + "\""}
			}
		} else {
			if len(auth) != 0 {
				req.Header["mmt-auth"] = []string{auth}
			}
		}
	}
	startTime, resp := setHeadersAndPostRequest(constants.CGSearchHotelsAPI, searchHotelsRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CGSearchHotelsAPI, err.Error())
	} else if resp != nil && resp.Body != nil {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &searchHotelsResponse)
	}
	logger.Infof("Time taken to get search hotels response for correlationKey %s is %v", searchHotelsRequest.CorrelationKey, time.Since(startTime))
	return searchHotelsResponse
}

func (ci *clientStruct) PostCrossSellCardsData(searchHotelsRequest *clientbackend.SearchHotelsRequest, c *gin.Context) clientgateway.CrossSellCardData {
	xSellCardDataResponse := clientgateway.CrossSellCardData{}
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.HesCardsData, bytes.NewBuffer(bytesRepresentation))
	//set required headers
	auth := c.GetHeader("mmt-auth")
	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") {
			if len(auth) != 0 {
				req.Header["backup_auth"] = []string{"mmtAuth=" + "\"" + auth + "\""}
			}
		} else {
			if len(auth) != 0 {
				req.Header["mmt-auth"] = []string{auth}
			}
		}
	}
	startTime, resp := setHeadersAndPostRequest(constants.HesCardsData, searchHotelsRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.HesCardsData, err.Error())
	} else if resp != nil && resp.Body != nil {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &xSellCardDataResponse)

	}
	logger.Infof("Time taken to get search hotels response for correlationKey %s is %v", searchHotelsRequest.CorrelationKey, time.Since(startTime))
	return xSellCardDataResponse
}

func (ci *clientStruct) PostXSellSearchHotelsRequestAsync(searchHotelsRequest *clientbackend.SearchHotelsRequest, c *gin.Context,
	ch chan clientgateway.CrossSellSearchHotelsData) {
	searchHotelsResponse := clientgateway.CrossSellSearchHotelsData{}
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CGSearchHotels, bytes.NewBuffer(bytesRepresentation))

	auth := c.GetHeader("mmt-auth")
	deviceType := c.GetHeader("channel")

	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") {
			if len(auth) != 0 {
				req.Header["backup_auth"] = []string{"mmtAuth=" + "\"" + auth + "\""}
			}
		} else {
			if len(auth) != 0 {
				req.Header["mmt-auth"] = []string{auth}
			}
		}
	}
	startTime, resp := setHeadersAndPostRequest(constants.CGSearchHotelsAPI, searchHotelsRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CGSearchHotelsAPI, err.Error())
	} else {
		if resp != nil && resp.Body != nil {
			defer resp.Body.Close()
			body, _ := ioutil.ReadAll(resp.Body)
			_ = json.Unmarshal(body, &searchHotelsResponse)
		}
	}
	logger.Infof("Time taken to get search hotels response for correlationKey %s is %v", searchHotelsRequest.CorrelationKey, time.Since(startTime))
	ch <- searchHotelsResponse

}

func (ci *clientStruct) PostXSellLandingDiscoveryRequest(searchHotelsRequest *clientbackend.SearchHotelsRequest, c *gin.Context) clientgateway.CrossSellSearchHotelsData {
	searchHotelsResponse := clientgateway.CrossSellSearchHotelsData{}
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CGLandingCardHotels, bytes.NewBuffer(bytesRepresentation))
	//set required headers
	auth := c.GetHeader("mmt-auth")
	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") {
			if len(auth) != 0 {
				req.Header["backup_auth"] = []string{"mmtAuth=" + "\"" + auth + "\""}
			}
		} else {
			if len(auth) != 0 {
				req.Header["mmt-auth"] = []string{auth}
			}
		}
	}
	startTime, resp := setHeadersAndPostRequest(constants.CGSearchHotelsAPI, searchHotelsRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CGSearchHotelsAPI, err.Error())
	} else if resp != nil && resp.Body != nil {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &searchHotelsResponse)
	}
	logger.Infof("Time taken to get search hotels response for correlationKey %s is %v", searchHotelsRequest.CorrelationKey, time.Since(startTime))
	return searchHotelsResponse
}

func (ci *clientStruct) PostXSellLandingDiscoveryRequestAsync(searchHotelsRequest *clientbackend.SearchHotelsRequest, c *gin.Context) clientgateway.CrossSellSearchHotelsData {
	searchHotelsResponse := clientgateway.CrossSellSearchHotelsData{}
	bytesRepresentation, err := json.Marshal(searchHotelsRequest)

	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CGLandingCardHotels, bytes.NewBuffer(bytesRepresentation))

	auth := c.GetHeader("mmt-auth")
	deviceType := c.GetHeader("channel")

	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") {
			if len(auth) != 0 {
				req.Header["backup_auth"] = []string{"mmtAuth=" + "\"" + auth + "\""}
			}
		} else {
			if len(auth) != 0 {
				req.Header["mmt-auth"] = []string{auth}
			}
		}
	}
	startTime, resp := setHeadersAndPostRequest(constants.CGLandingCardHotels, searchHotelsRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CGSearchHotelsAPI, err.Error())
	} else {
		if resp != nil && resp.Body != nil {
			defer resp.Body.Close()
			body, _ := ioutil.ReadAll(resp.Body)
			_ = json.Unmarshal(body, &searchHotelsResponse)
		}
	}
	logger.Infof("Time taken to get landing card %s response for correlationKey %s is %v", searchHotelsRequest.CardId, searchHotelsRequest.CorrelationKey, time.Since(startTime))
	return searchHotelsResponse

}

func (ci *clientStruct) PostFetchCollectionRequest(fetchCollectionRequest *request.FetchCollectionRequestCG, c *gin.Context) (response.FetchCollectionResponseCG, error) {
	searchHotelsResponse := response.FetchCollectionResponseCG{}
	bytesRepresentation, err := json.Marshal(fetchCollectionRequest)

	url := fmt.Sprintf(config.AppConfig.URLs.CGFetchCollections, fetchCollectionRequest.DeviceDetails.BookingDevice)
	req, _ := http.NewRequest("POST", url, bytes.NewBuffer(bytesRepresentation))
	//set required headers
	auth := c.GetHeader("mmt-auth")
	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") {
			if len(auth) != 0 {
				req.Header["backup_auth"] = []string{"mmtAuth=" + "\"" + auth + "\""}
			}
		} else {
			if len(auth) != 0 {
				req.Header["mmt-auth"] = []string{auth}
			}
		}
	}
	startTime, resp := setHeadersAndPostRequest(constants.CBFetchCollectionsAPI, fetchCollectionRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBFetchCollectionsAPI, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &searchHotelsResponse)
		logger.Infof("Response for fetchCollection is %s", string(body))
	}
	logger.Infof("Time taken to get fetchCollection response for correlationKey %s is %v", fetchCollectionRequest.CorrelationKey, time.Since(startTime))
	return searchHotelsResponse, err
}

func (ci *clientStruct) PostXSellSearchPersonalizedRequestCorp(personalizedHotelsRequest *clientbackend.SearchPersonalizedHotelsRequest, c *gin.Context) clientgateway.CrossSellSearchPersonalizedData {
	searchHotelsResponse := clientgateway.CrossSellSearchPersonalizedData{}
	bytesRepresentation, err := json.Marshal(personalizedHotelsRequest)
	req, _ := http.NewRequest("POST", config.AppConfig.URLs.CBSearchPersonalizedHotels, bytes.NewBuffer(bytesRepresentation))
	//set required headers
	auth := c.GetHeader("mmt-auth")
	req.Header["mmt-auth"] = []string{auth}
	startTime, resp := setHeadersAndPostRequest(constants.CBSearchPersonalizedHotels, personalizedHotelsRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBSearchPersonalizedHotels, err.Error())
	} else {
		defer resp.Body.Close()
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &searchHotelsResponse)
	}
	logger.Infof("Time taken to get search personalized hotels response for correlationKey %s is %v", personalizedHotelsRequest.CorrelationKey, time.Since(startTime))
	return searchHotelsResponse
}

func setHeadersAndPostRequest(clientConstant string, correlationKey string, c *gin.Context, req *http.Request, err error) (time.Time, *http.Response) {
	req.Header.Set("Content-Type", "application/json")
	req.Header.Set("Accept", "application/json")
	q := req.URL.Query()
	q.Add("correlationKey", correlationKey)
	req.URL.RawQuery = q.Encode()
	uuid := c.GetHeader("uuid")
	if len(uuid) != 0 {
		req.Header["uuid"] = []string{uuid}
	}
	region := c.GetHeader("region")
	if len(region) != 0 {
		req.Header["region"] = []string{region}
	}
	language := c.GetHeader("language")
	if len(language) != 0 {
		req.Header["language"] = []string{language}
	}
	userCurrency := c.GetHeader("user-currency")
	logger.Infof("User-currency Header for CB search hotels request: %v", userCurrency)
	if len(userCurrency) != 0 {
		req.Header["user-currency"] = []string{userCurrency}
	}
	userCountry := c.GetHeader("user-country")
	if len(userCountry) != 0 {
		req.Header["user-country"] = []string{userCountry}
	}
	entity := c.GetHeader("entity-name")
	if len(entity) != 0 {
		req.Header["entity-name"] = []string{entity}
	}
	currency := c.GetHeader("currency")
	if len(currency) != 0 {
		req.Header["currency"] = []string{currency}
	}
	mcid := c.GetHeader("mcid")
	if len(mcid) != 0 {
		req.Header["mcid"] = []string{mcid}
	}
	tid := c.GetHeader("tid")
	if len(tid) != 0 {
		req.Header["tid"] = []string{tid}
	}
	mmtAuth := c.GetHeader("mmt-auth")
	if len(mmtAuth) != 0 {
		req.Header["mmt-auth"] = []string{mmtAuth}
	}
	giAuth := c.GetHeader("OAUTH-GOIBIBO")
	if len(giAuth) != 0 {
		req.Header["OAUTH-GOIBIBO"] = []string{giAuth}
	}
	userAgent := c.GetHeader("User-Agent")
	if len(userAgent) != 0 {
		req.Header["User-Agent"] = []string{userAgent}
	}
	host := c.GetHeader("Host")
	if len(host) != 0 {
		req.Header["Host"] = []string{host}
	}
	req.Header["request-source"] = []string{constants.REQUEST_SOURCE_SCION}

	logger.Infof("Header for CB search hotels request: %v", req.Header)
	cbHTTPClient := HTTPClientMap[clientConstant]
	startTime := time.Now()
	resp, err := cbHTTPClient.Do(req)
	return startTime, resp
}

func (ci *clientStruct) PostMobLandingRequest(mobLandingRequest *clientbackend.MobLandingRequest, c *gin.Context) clientgateway.HotelsMobLandingResponse {
	logger.Infof("Inside PostMobLandingRequest")
	mobLandingResponse := clientgateway.HotelsMobLandingResponse{}
	bytesRepresentation, err := json.Marshal(mobLandingRequest)
	//err, resp := findResponse(mobLandingRequest, c)
	req, _ := http.NewRequest("POST", findUrl(), bytes.NewBuffer(bytesRepresentation))

	//set required headers
	startTime, resp := setHeadersAndPostRequest(constants.MobLanding, mobLandingRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.CBSearchPersonalizedHotels, err.Error())
	} else {
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &mobLandingResponse)
	}
	logger.Infof("Time taken to get search personalized hotels response for correlationKey %s is %v", mobLandingRequest.CorrelationKey, time.Since(startTime))
	return mobLandingResponse
}

func findUrl() string { // remove hardcoding in step 2
	baseURL := "https://mapi.goibibo.com/clientbackend-gi/cg/mob-landing/android/2"
	params := url.Values{}
	params.Add("countryCode", "IN")
	params.Add("idContext", "B2C")
	params.Add("profile", "B2C")
	params.Add("currency", "inr")
	params.Add("language", "eng")
	params.Add("region", "in")
	params.Add("versioncode", "2063")
	params.Add("flavour", "android")
	fullURL := fmt.Sprintf("%s?%s", baseURL, params.Encode())
	return fullURL
}

func (ci *clientStruct) PostMMTMobLandingRequest(mmtMobLandingRequest *clientbackend.MobLandingRequest, c *gin.Context) clientgateway.HotelsMobLandingResponse {
	logger.Infof("Inside PostMMTMobLandingRequest")
	mmtMobLandingResponse := clientgateway.HotelsMobLandingResponse{}
	bytesRepresentation, err := json.Marshal(mmtMobLandingRequest)
	bookingDevice := "android" // your default
	if strings.TrimSpace(mmtMobLandingRequest.DeviceDetails.BookingDevice) != "" {
		bookingDevice = mmtMobLandingRequest.DeviceDetails.BookingDevice
	}

	// Build MMT URL with query parameters
	baseURL := config.AppConfig.URLs.CgMobLandingMMT
	baseURL = fmt.Sprintf(baseURL, bookingDevice)
	params := url.Values{}
	params.Add("region", "in")
	params.Add("language", "eng")
	params.Add("requestId", mmtMobLandingRequest.CorrelationKey)

	fullURL := fmt.Sprintf("%s&%s", baseURL, params.Encode())
	req, _ := http.NewRequest("POST", fullURL, bytes.NewBuffer(bytesRepresentation))

	startTime, resp := setHeadersAndPostRequest(constants.MobLanding, mmtMobLandingRequest.CorrelationKey, c, req, err)
	if err != nil {
		logger.Errorf("Error while getting api response for %s error %s", constants.MobLanding, err.Error())
	} else {
		body, _ := ioutil.ReadAll(resp.Body)
		_ = json.Unmarshal(body, &mmtMobLandingResponse)
	}
	logger.Infof("Time taken to get MMT mob landing response for correlationKey %s is %v", mmtMobLandingRequest.RequestDetails.RequestId, time.Since(startTime))
	return mmtMobLandingResponse
}
