# Hotels-Scion
Go Rest API skelton
Go is an open source programming language that makes it easy to build simple, reliable, and efficient software.

Steps to Run Hotel-Scion on local machine.

1. Download and install GoLand
2. brew install pkg-config
3. brew install librdkafka
4. Comment utils/kafka/producer.go file and comment kafka.InitKafkaProducer() line in main.go file, and kafka.KafkaProducer.SendMessage() in utils/pdt/pdt_logging.go or apply run-scion.diff 
5. Run command go build to build the project.
6. Run main funcion in main.go file to run the project.

# How to install Go
1- download the go software from below link - https://golang.org/
# Go version 1.16.5
https://golang.org/dl/go1.16.5.darwin-amd64.pkg


3- Steps to install GO and setup on local machine

3.1 After install this package
go1.16.5.darwin-amd64.pkg

3.2- open the terminal and put the below command to check the go version
~ go version

below command will give you list of enviornment variable, we need get the `GOPATH` value only
~ go env 

4- go to Hotels-Scion directory and build the project
~ go build

5- run the program
~ ./Hotels-Scion

6- below is the health check api
curl --location --request GET 'http://localhost:8080/hotels-scion/health'

# Installing Code Studio for Go

6- Download the <b> visual studio</b> code from below link as per os- https://code.visualstudio.com/

# Install plugin in visual studio for go lang
Installing 10 tools at
/Users/<USER>/go/bin in module mode.

gopkgs <br>
go-outline <br>
gotests <br>
gomodifytags <br>
impl <br>
goplay <br>
dlv <br>
dlv-dap <br>
staticcheck <br>
gopls <br>

# Profiling the server for heap or cpu dump
- https://<scion_FQDN>/debug/pprof/profile

- https://<scion_FQDN>/debug/pprof/heap

Hits to the above endpoints will generate file that can be analysed over http protocol
go tool pprof -http=:8082 <heap/profile file>