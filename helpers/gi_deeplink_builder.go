package helpers

import (
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"Hotels-Scion/utils"
	"Hotels-Scion/utils/logging"
	"encoding/json"
	"fmt"
)

var logger = logging.GetLogger()

func BuildGiDeeplinkForHotels(rsc *request.SearchEvent, data *clientgateway.CrossSellSearchHotelsData) {
	BuildGiDeepLinkForCity(rsc, data)
	if len(data.HotelList) != 0 {
		for i := 0; i < len(data.HotelList); i++ {
			hotel := &data.HotelList[i]
			BuildGiDeeplinkForHotel(hotel, rsc, data.CityCode)
		}
	}
}

func BuildGiDeeplinkForHotel(hotel *clientgateway.Hotel, rsc *request.SearchEvent, cityCode string) {
	hotel.AppDeepLink = ""

	hotel.Godata.N = hotel.Name
	hotel.Godata.Cn = hotel.CityName
	hotel.Godata.T = "hotel"
	hotel.Godata.Hn = hotel.Name
	hotel.Godata.MHotelId = hotel.ID
	hotel.Godata.LocusId = cityCode
	hotel.Godata.LocusType = "hotel"
	hotel.Godata.Vcid = rsc.SearchContext.To.LobCity
	hotel.Godata.Cc = hotel.CountryCode
	checkinTime := utils.GetTimeFromEpochAndZone(rsc.SearchContext.FromDateStr.Zone, rsc.SearchContext.FromDateStr.TS)
	hotel.Godata.Checkin = utils.GetTimeInFormattedString(checkinTime)
	checkoutTime := utils.GetTimeFromEpochAndZone(rsc.SearchContext.ToDateStr.Zone, rsc.SearchContext.ToDateStr.TS)
	hotel.Godata.Checkout = utils.GetTimeInFormattedString(checkoutTime)
	hotel.Godata.Vhid = hotel.GiID
	hotel.Godata.RoomString = "1-2-0"

	gd, err := json.Marshal(hotel.Godata)
	if err != nil {
		logger.Errorf("Error marshalling data for deeplink : %s", err.Error())
	} else {
		hotel.Tg = 207
		hotel.Gd = string(gd)
	}
	hotel.DeepLink = fmt.Sprintf(constants.HOTEL_LISTING_DT_DEEPLINK_GI, utils.GetTimeInDDMMYYYYFormattedString(checkinTime), utils.GetTimeInDDMMYYYYFormattedString(checkoutTime), BuildRoomString(rsc),
		cityCode, hotel.Godata.LocusType, cityCode, hotel.CountryCode, hotel.Name, hotel.ID, hotel.GiID, hotel.CurrencyCode.ID)

}

func BuildRoomString(rsc *request.SearchEvent) string {
	if len(rsc.SearchContext.Pax) != 0 {
		room := 0
		adult := 0
		child := 0
		for i := 0; i < len(rsc.SearchContext.Pax); i++ {
			paxDetail := &rsc.SearchContext.Pax[i].Details
			room++
			adult += paxDetail.Adult.Count
			child += paxDetail.Child.Count
		}
		roomString := fmt.Sprintf(constants.ROOM_STRING, room, adult, child)
		return roomString
	}
	return ""
}

func BuildGiDeepLinkForCity(rsc *request.SearchEvent, data *clientgateway.CrossSellSearchHotelsData) {

	data.Godata.LocusType = "city"
	data.Godata.LocusId = data.CityCode
	data.Godata.Cid = rsc.SearchContext.To.LobCity
	data.Godata.Cc = data.CountryCode
	data.Godata.Oid = rsc.SearchContext.To.LobCity
	data.Godata.N = data.CityName
	data.Godata.Cn = data.CityName
	checkinTime := utils.GetTimeFromEpochAndZone(rsc.SearchContext.FromDateStr.Zone, rsc.SearchContext.FromDateStr.TS)
	data.Godata.Checkin = utils.GetTimeInFormattedString(checkinTime)
	checkoutTime := utils.GetTimeFromEpochAndZone(rsc.SearchContext.ToDateStr.Zone, rsc.SearchContext.ToDateStr.TS)
	data.Godata.Checkout = utils.GetTimeInFormattedString(checkoutTime)
	data.Godata.T = "city"

	gd, err := json.Marshal(data.Godata)
	if err != nil {
		logger.Errorf("Error marshalling data for deeplink : %s", err.Error())
	} else {
		data.Tg = 202
		data.Gd = string(gd)
	}
	data.DeeplinkURL = fmt.Sprintf(constants.CITY_LISTING_DT_DEEPLINK_GI, utils.GetTimeInDDMMYYYYFormattedString(checkinTime), utils.GetTimeInDDMMYYYYFormattedString(checkoutTime), BuildRoomString(rsc),
		data.CityCode, "city", data.CityCode, data.CountryCode, data.CityName)

}

func BuildGiDeeplinkForDSDHotel(request *request.MobLandingRequest, scionHotel *response.HotelListNew, cgHotel *clientgateway.HotelListNew) {
	goData := &clientgateway.Godata{
		N:          cgHotel.Name,
		Cn:         cgHotel.LocationDetail.Name,
		T:          "hotel",
		Hn:         cgHotel.Name,
		MHotelId:   cgHotel.ID,
		LocusId:    request.SearchCriteria.CityCode,
		LocusType:  "hotel",
		Vcid:       request.SearchCriteria.VcId,
		Cc:         request.SearchCriteria.CountryCode,
		Checkin:    request.SearchCriteria.CheckIn,
		Checkout:   request.SearchCriteria.CheckOut,
		Vhid:       cgHotel.GiID,
		RoomString: "",
	}

	if len(request.SearchCriteria.RoomStayCandidates) > 0 {
		roomCount := 0
		adultCount := 0
		childCount := 0
		for _, room := range request.SearchCriteria.RoomStayCandidates {
			roomCount++
			adultCount += room.AdultCount
			childCount += len(room.ChildAges)
		}
		roomString := fmt.Sprintf(constants.ROOM_STRING, roomCount, adultCount, childCount)
		goData.RoomString = roomString
	}

	gd, err := json.Marshal(goData)
	if err != nil {
		logger.Errorf("Error marshalling DSD hotelData for deeplink : %s", err.Error())
	} else {
		scionHotel.Tg = 207
		scionHotel.Gd = string(gd)
	}
}

func BuildGiDeeplinkForDSDCity(request *request.MobLandingRequest, cityName string, cardPayload *response.CardPayload) {
	goData := &clientgateway.Godata{
		N:          cityName,
		Cn:         cityName,
		T:          "city",
		LocusId:    request.SearchCriteria.CityCode,
		LocusType:  "city",
		Cid:        request.SearchCriteria.VcId,
		Oid:        request.SearchCriteria.VcId,
		Cc:         request.SearchCriteria.CountryCode,
		Checkin:    request.SearchCriteria.CheckIn,
		Checkout:   request.SearchCriteria.CheckOut,
		RoomString: "",
	}

	if len(request.SearchCriteria.RoomStayCandidates) > 0 {
		roomCount := 0
		adultCount := 0
		childCount := 0
		for _, room := range request.SearchCriteria.RoomStayCandidates {
			roomCount++
			adultCount += room.AdultCount
			childCount += len(room.ChildAges)
		}
		roomString := fmt.Sprintf(constants.ROOM_STRING, roomCount, adultCount, childCount)
		goData.RoomString = roomString
	}

	gd, err := json.Marshal(goData)
	if err != nil {
		logger.Errorf("Error marshalling DSD hotelData for deeplink : %s", err.Error())
	} else {
		cardPayload.ViewAllCard.Tg = 202
		cardPayload.ViewAllCard.Gd = string(gd)
	}
}
