package helpers

import (
	"Hotels-Scion/models/request"
	"strings"
)

func ValidateMobLandingRequest(request *request.MobLandingRequest) string {
	var error string = ""
	if request.RequestDetails.RequestId == "" {
		error = "Invalid request id"
	}
	if request.RequestDetails.Brand == "" {
		error = "Invalid brand"
	}
	if request.RequestDetails.Channel == "" {
		error = "Invalid channel"
	}
	return error
}
func ValidateCrosselRequest(common *request.Common) string {
	var error string = ""
	if !isValidPageContext(common.PageContext) {
		error = "Invalid CrossSell Request: valid pageContext values are :HOME/MYTRIP/THANKYOU"
	}
	if common.BookingDevice == "" {
		error = "Invalid CrossSell Request: null or empty bookingDevice"
	}
	if common.IDContext == "" {
		error = "Invalid CrossSell Request: null or empty idContext"
	}
	if common.RequestType == "" {
		error = "Invalid CrossSell Request: null or empty request type"
	}
	if common.User.DeviceID == "" || common.User.VisitorID == "" {
		error = "Invalid CrossSell Request: invalid user Values(visitorId/deviceId)"
	}
	return error
}

func isValidPageContext(context string) bool {
	if context == "" {
		return false
	} else {
		cntxtArr := strings.Split(context, "_")
		ctx := strings.ToUpper(cntxtArr[len(cntxtArr)-1])
		if ctx == "THANKYOU" || ctx == "MYTRIP" || ctx == "HOME" {
			return true
		}
	}
	return false
}

func ValidateFetchCollectionRequest(common *request.Common) string {
	var error string = ""
	if !isValidPageType(common.PageType) {
		error = "Invalid fetchCollection Request: valid pageType values are :singleCollection/multiCollection/trendingNow"
	}
	if common.BookingDevice == "" {
		error = "Invalid fetchCollection Request: null or empty bookingDevice"
	}
	if common.IDContext == "" {
		error = "Invalid fetchCollection Request: null or empty idContext"
	}
	if common.RequestType == "" {
		error = "Invalid fetchCollection Request: null or empty request type"
	}
	if common.User.DeviceID == "" || common.User.VisitorID == "" {
		error = "Invalid fetchCollection Request: invalid user Values(visitorId/deviceId)"
	}
	return error
}

func ValidateScionFetchCollectionRequest(fetchCollectionRequest *request.FetchCollectionRequest) string {
	var error string = ""
	if fetchCollectionRequest.User == (request.UserDetails{}) {
		error = "Invalid fetchCollection Request: null or empty UserDetails"
	}
	if fetchCollectionRequest.Context == (request.Context{}) {
		error = "Invalid fetchCollection Request: null or empty Context"
	}
	if fetchCollectionRequest.User.DeviceInfo.Id == "" {
		error = "Invalid fetchCollection Request: null or empty bookingDevice"
	}
	if fetchCollectionRequest.Context.Scope == "" {
		error = "Invalid fetchCollection Request: null or empty scope"
	}
	if fetchCollectionRequest.Context.ContextId == "" {
		error = "Invalid fetchCollection Request: null or empty ContextId"
	}
	return error
}

func isValidPageType(pgType string) bool {
	if pgType == "" {
		return false
	} else {
		pgTypArr := strings.Split(pgType, "_")
		pgTyp := strings.ToLower(pgTypArr[len(pgTypArr)-1])
		if pgTyp == "singlecollection" || pgTyp == "multicollection" || pgTyp == "trendingnow" {
			return true
		}
	}
	return false
}

func Contains(list []string, item string) bool {
	for _, v := range list {
		if strings.EqualFold(v, item) {
			return true
		}
	}
	return false
}
