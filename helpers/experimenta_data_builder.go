package helpers

import (
	"Hotels-Scion/models"
	"encoding/json"
	"strings"
)

func BuildExperimentDataFromString(exp string) models.ExperimentData {
	expData := models.ExperimentData{}
	if exp != "" {
		experimentStr := strings.ReplaceAll(exp, "^\"|\"$", "")
		experimentMap := map[string]interface{}{}
		json.Unmarshal([]byte(experimentStr), &experimentMap)

		if len(experimentMap) != 0 {
			if experimentMap["MC"] == "T" {
				expData.MultiCity = true
			}
			if experimentMap["LCS"] == "T" {
				expData.Locus = true
			}
		}
	}

	return expData
}
