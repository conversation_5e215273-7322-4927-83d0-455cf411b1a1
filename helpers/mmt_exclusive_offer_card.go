package helpers

import (
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"Hotels-Scion/utils/logging"
	"github.com/gin-gonic/gin"
	"strings"
	"time"
)

func BuildMmtExclusiveCardRequest(sc *request.SearchEvent, req *clientbackend.SearchHotelsRequest) {
	if strings.EqualFold(sc.CardID, constants.MMT_EXCLUSIVE_OFFERS) {
		req.AppliedFilterMap = sc.AppliedFilterMap
		req.CardId = sc.CardID
	}
}
func BuildCrossSellCardRequest(sc *request.SearchEvent, req *clientbackend.SearchHotelsRequest) {
	if strings.EqualFold(sc.CardID, constants.MMT_CROSS_SELL_CARD) {
		req.AppliedFilterMap = sc.AppliedFilterMap
		req.CardId = sc.CardID
	}
}

func BuildMmtExclusiveCardResponse(sc *request.SearchEvent, tabsData *clientgateway.CrossSellSearchHotelsData, c *gin.Context) {
	if strings.EqualFold(sc.CardID, constants.MMT_EXCLUSIVE_OFFERS) {
		var cardOffer clientgateway.Offer
		var dealPicker clientgateway.DealPicker
		// channel node is considered as deviceType/client
		deviceType := c.GetHeader("channel")
		if len(deviceType) != 0 {
			cardOffer.ImageUrl = "https://promos.makemytrip.com/appfest/Hotels3/%s/ddd_htl_3.png"
		}
		cardOffer.ListingDeeplink = tabsData.DeeplinkURL
		cardOffer.OfferExpiry = getOfferExpiryData(sc, tabsData)
		tabsData.CardOffer = &cardOffer

		dealPicker.Text = "Deals in"
		dealPicker.City = sc.SearchContext.To.CityName
		dealPicker.Icon = "https://promos.makemytrip.com/GCC/Dazzling_Deals/Dealpicker/%s/dealpicker.png"
		tabsData.DealPicker = dealPicker
	}
}

func getOfferExpiryData(sc *request.SearchEvent, data *clientgateway.CrossSellSearchHotelsData) *clientgateway.OfferExpiry {
	expiryData := new(clientgateway.OfferExpiry)
	getServerTimestamp(expiryData, sc, data)
	getExpiryTimeStamp(expiryData)
	return expiryData
}

func getExpiryTimeStamp(expiryData *clientgateway.OfferExpiry) {
	endOfDayTime := getEndOfDayTime("Asia/Dubai")
	expiryData.ExpiryTimestamp = endOfDayTime.UnixNano() / 1000000
	logging.Logger.Debugf("MMT exclusive expiry timestamp as per UAE: %s", expiryData.ExpiryTimestamp)
}

func getServerTimestamp(expiryData *clientgateway.OfferExpiry, sc *request.SearchEvent, data *clientgateway.CrossSellSearchHotelsData) {
	expiryData.ServerTimestamp = data.ExclusiveOfferTs
	data.ExclusiveOfferTs = 0
	logging.Logger.Debugf("MMT exclusive server timestamp as per UAE: %s", expiryData.ServerTimestamp)
}

func getEndOfDayTime(location string) time.Time {
	var err error
	var locationUAE *time.Location
	if locationUAE, err = time.LoadLocation(location); err != nil {
		logging.Logger.Infof("Error while getting locationUAE .... %s", err.Error())
		panic(err)
	}
	now := time.Now()
	endOfDay := time.Date(now.Year(), now.Month(), now.Day(), 23, 59, 59, 999999999, locationUAE)
	return endOfDay
}

func GetSubheading(cardId string) string {
	if strings.EqualFold(cardId, constants.MMT_EXCLUSIVE_OFFERS) {
		return constants.HEADER_MMTEXCLUSIVE_HOTELS_SUBHEADING
	} else {
		return constants.HEADER_HOTELS_SUBHEADING
	}
}
