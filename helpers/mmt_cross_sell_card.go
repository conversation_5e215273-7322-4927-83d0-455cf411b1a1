package helpers

import (
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"github.com/gin-gonic/gin"
	"math"
	"strings"
)

func IsMmtCrossSellCardExists(sc *request.SearchEvent, c *gin.Context) bool {
	return strings.EqualFold(sc.CardID, constants.MMT_CROSS_SELL_CARD) && strings.EqualFold(sc.Tags.EdgeType, "opportunity") && len(c.<PERSON>Header("profileType")) != 0 &&
		len(c.GetHeader("language")) != 0 && len(c.<PERSON>eader("region")) != 0 &&
		strings.EqualFold(c.<PERSON>ead<PERSON>("profileType"), "PERSONAL") && strings.EqualFold(c.<PERSON>Header("language"), "eng") &&
		strings.EqualFold(c.<PERSON>Header("region"), "in")

}

func BuildCrossSellCardResponse(sc *request.SearchEvent, tabsData *clientgateway.CrossSellSearchHotelsData, c *gin.Context) {
	if strings.EqualFold(sc.CardID, constants.MMT_CROSS_SELL_CARD) {
		if IsMmtCrossSellCardExists(sc, c) {
			hydraSegmentListFromRequest := sc.HydraSegments
			// min Priority always have high preference
			minPriorityPossible := config.MaxIntValue
			for i := 0; i < len(hydraSegmentListFromRequest); i++ {
				hydraSegment := hydraSegmentListFromRequest[i]
				if val, ok := config.OfferCardSegmentToPriorityMap[hydraSegment]; ok {
					minPriorityPossible = int(math.Min(float64(minPriorityPossible), float64(val)))
				}
			}
			if minPriorityPossible != config.MaxIntValue {
				var cardOffer clientgateway.Offer
				cardOffer.ListingDeeplink = tabsData.DeeplinkURL
				cardOffer.ImageUrl = config.ImgUrl[config.OfferCardPriority[minPriorityPossible]]
				cardOffer.OfferImg = config.OfferImg[config.OfferCardPriority[minPriorityPossible]]
				cardOffer.OfferMainImag = config.OfferMainImag[config.OfferCardPriority[minPriorityPossible]]
				tabsData.CardOffer = &cardOffer
			}
		} else {
			// So It's DROP-OFF Case, So offerCard Should be Empty
			tabsData.CardOffer = nil
		}
	}
}

func FetchCardIdBasisOfHydraSegments(hydraSegments []string, pageContext string, cardId string) string {

	pageContextToCardIdHydraSegmentConfigMap := config.PageContextToCardIdHydraSegmentConfig
	minPriorityPossible := config.MaxIntValue
	cardIdToHydraSegmentMap := pageContextToCardIdHydraSegmentConfigMap[pageContext]
	if cardIdToHydraSegmentMap != nil {
		hydraSegmentConfigMap := cardIdToHydraSegmentMap[cardId]
		if len(hydraSegmentConfigMap) > 0 {
			// we have to compute the minPriorityPossible
			// Based on the hydraSegments which fetch the correct cardId that should be send to HES service to get Benefit Data
			hydraSegmentListFromRequest := hydraSegments
			// min Priority always have high preference
			for i := 0; i < len(hydraSegmentListFromRequest); i++ {
				hydraSegment := hydraSegmentListFromRequest[i]
				if val, ok := config.HydraSegmentToPriorityMap[hydraSegment]; ok {
					minPriorityPossible = int(math.Min(float64(minPriorityPossible), float64(val)))
				}
			}
			if minPriorityPossible != config.MaxIntValue {
				return config.CardPriorityToCardTypeMap[minPriorityPossible]
			}
		} else {
			// we don't have to compute the	minPriorityPossible and just return the defaultConfig cardId
			return config.CardIdDefaultConfigMap[cardId]
		}
	}
	return ""
}
