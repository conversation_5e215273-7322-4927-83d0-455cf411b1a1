package config

import (
	"Hotels-Scion/utils/logging"
	"Hotels-Scion/utils/pms"
	"io/ioutil"
)

var staticPmsProperty *pms.PropertyManager

var logger = logging.GetLogger()

func ReadPmsConfig() ([]byte, error) {
	staticPmsProperty = pms.New(AppConfig.URLs.PmsCommonConfigUrl)
	body, err := staticPmsProperty.Load()
	if err != nil {
		logger.Fatalf("Error while fetching from pms : %s", err.Error())
		return nil, err
	}
	byt, err := ioutil.ReadAll(body)
	return byt, err
}
