package config

var (
	TY_PAGE_CONTEXT           = "THANKYOU"
	CardPriorityToCardTypeMap = map[int]string{
		2:  "OFFERCARD",
		3:  "BENEFITCARD",
		4:  "BENEFITCARD",
		5:  "BENEFITCARD",
		6:  "BENEFITCARD",
		7:  "BENEFITCARD",
		8:  "OFFERCARD",
		9:  "OFFERCARD",
		10: "OFFERCARD",
		11: "OFFERCARD",
	}

	CardIdDefaultConfigMap = map[string]string{
		"HOTEL_XSELL_BENEFITS":   "BENEFITCARD",
		"HOTEL_EXTEND_YOUR_TRIP": "HOTEL_EXTEND_YOUR_TRIP",
		"INTL_CASHBACK_CARD":     "INTL_CASHBACK_CARD",
	}

	PageContextToCardIdHydraSegmentConfig = map[string]map[string][]string{
		"HOTELLANDING": {
			"HOTEL_XSELL_BENEFITS": {"r2015", "r2016", "r2017", "r2018", "r2023"},
		},
		"APP_LANDING": {
			"HOTEL_XSELL_BENEFITS": {"r2067", "r2068", "r2015", "r2016", "r2017", "r2018", "r2023", "r2011", "r2012", "r2013", "r2014"},
		},
		"APPLANDING": {
			"HOTEL_XSELL_BENEFITS": {"r2067", "r2068", "r2015", "r2016", "r2017", "r2018", "r2023", "r2011", "r2012", "r2013", "r2014"},
		},
		"THANKYOU": {
			"HOTEL_XSELL_BENEFITS":   {},
			"HOTEL_EXTEND_YOUR_TRIP": {},
		},
		"HOTEL_LANDING": {},
	}

	HydraSegmentToPriorityMap = map[string]int{
		"r2068": 2,
		"r2015": 3,
		"r2016": 4,
		"r2017": 5,
		"r2018": 6,
		"r2023": 7,
		"r2011": 8,
		"r2012": 9,
		"r2013": 10,
		"r2014": 11,
	}
)
