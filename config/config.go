package config

import (
	"Hotels-Scion/constants"
	"Hotels-Scion/utils/logging"
	"bytes"
	"encoding/json"
	"github.com/spf13/viper"
	"strings"
)

var (
	AppConfig   applicationConfig
	Environment = constants.Development
	validEnvs   = map[string]constants.Environment{
		"dev":     constants.Development,
		"qa":      constants.QA,
		"staging": constants.Staging,
		"prod":    constants.Production,
	}

	PmsCommonConfig pmsConfig

	log = logging.GetLogger()
)

func Initialize(configPath, env string) {

	if val, ok := validEnvs[strings.ToLower(env)]; ok {
		Environment = val
	} else {
		log.Warningf("Environment variable '%s' is unset or is invalid.Please set it to one of DEV, QA, STAGING, PROD. Using 'DEV' as default.", env)
		Environment = constants.Development
	}

	if err := readConfig("yaml", configPath,
		"configuration."+strings.ToLower(string(Environment)), &AppConfig, &PmsCommonConfig); err != nil {
		panic(err)
	}
}

func readConfig(cfgType string, pathOrURL, name string, appConfig interface{}, pmsConfig *pmsConfig) error {

	//read in properties from cofiguration-x.yaml
	v := viper.New()
	v.SetConfigName(name)
	v.SetConfigType(cfgType)
	v.AddConfigPath(pathOrURL)
	if err := v.ReadInConfig(); err != nil {
		return err
	}
	if err := v.Unmarshal(appConfig); err != nil {
		return err
	}
	log.Warningf("AppConfig at startup %s", prettyJson(v.AllSettings()))

	//read in properties from girgit
	pmsViper := viper.New()
	pmsViper.SetConfigType("properties")
	b, err := ReadPmsConfig()
	if err != nil {
		return err
	}
	pmsViper.ReadConfig(bytes.NewBuffer(b))
	if err := pmsViper.Unmarshal(pmsConfig); err != nil {
		return err
	}
	log.Warningf("PmsConfig at startup %s", prettyJson(pmsViper.AllSettings()))
	return nil
}

func prettyJson(settings map[string]interface{}) string {
	b, _ := json.MarshalIndent(settings, "", "  ")
	return string(b)
}
