package config

const (
	IF_to_IH_IMG_URL             = "https://promos.makemytrip.com/images/CDN_upload/banner_8%25.jpg"
	DF_to_DH_IMG_URL             = "https://promos.makemytrip.com/Hotels_product/cross_sell/homepage-img-6.png"
	BUS_EXCLUSIVE_OFFER_IMG      = "https://imgak.mmtcdn.com/offers/busGTDeal.webp"
	BUS_EXCLUSIVE_OFFER_MAIN_IMG = "https://imgak.mmtcdn.com/offers/extraOffer.webp"
	BUS_EXCLUSIVE_OFFER_IMG_URL  = "https://go-assets.ibcdn.com/u/MMT/images/1731578904582-mmt_hp_htl_xsell_bus.png"
	MaxIntValue                  = 100000000
)

var (
	ImgUrl = map[string]string{
		"IF_to_IH":      IF_to_IH_IMG_URL,
		"DF_to_DH":      DF_to_DH_IMG_URL,
		"BUS_EXCLUSIVE": BUS_EXCLUSIVE_OFFER_IMG_URL,
	}

	OfferImg = map[string]string{
		"BUS_EXCLUSIVE": BUS_EXCLUSIVE_OFFER_IMG,
	}

	OfferMainImag = map[string]string{
		"BUS_EXCLUSIVE": BUS_EXCLUSIVE_OFFER_MAIN_IMG,
	}

	OfferCardPriority = map[int]string{
		1: "IF_to_IH",
		2: "DF_to_DH",
		3: "BUS_EXCLUSIVE",
	}
	OfferCardSegmentToPriorityMap = map[string]int{
		"r2023": 1,
		"r2015": 1,
		"r2011": 2,
		"r2012": 2,
		"r2013": 2,
		"r1997": 2,
		"r1653": 3,
		"r1647": 3,
	}
)
