package config

type applicationConfig struct {
	Port                         int
	Couch                        couchConfig
	TTLInSeconds                 int
	CityTaxExclusiveCountries    string
	TrafficSource                trafficSource
	URLs                         urls
	HotelDetailDeeplinkURL       string
	HotelDetailAppDeeplinkURL    string
	HotelListingDeeplinkURL      string
	AltAccoPropertyHeading       string
	KafkaConfig                  kafkaConfig
	AvroConfig                   avroConfig
	Heading                      heading
	LocationDetailsFlag          bool
	AASufficientCityList         []string
	DefaultSearchContext         defaultSearchContext
	CityToZoneMap                map[string][]string
	CardsForPersuasion           []string
	CrosssellBookerHydraSegments map[string][]string
}

type couchConfig struct {
	Hosts                   string
	Bucket                  string
	Password                string
	KeyValueTimeoutInMillis int
}

type trafficSource struct {
	Source string
	Type   string
}

type urls struct {
	CBSearchHotels             string
	CBCommparator              string
	CBXsellStaticDetails       string
	SingularityCrossel         string
	PmsCommonConfigUrl         string
	CBFetchCollections         string
	CGFetchCollections         string
	WebApiAltAccoProperty      string
	CGSearchContextDrools      string
	HesCardsData               string
	CGSearchHotels             string
	CGGiSearchHotels           string
	WebApiCityDetails          string
	CBSearchPersonalizedHotels string
	CGLandingCardHotels        string
	CgMobLandingMMT            string
}

type heading struct {
	Defaultheading    string
	Defaultsubheading string
}

type pmsConfig struct {
	MaxIdleConnectionsPerHost               int
	DialerTimeout                           int
	CGSearchContextHotelTimeoutMillis       int
	CbSearchHotelsTimeoutMillis             int
	CbSearchPersonalizedHotelsTimeoutMillis int
	CbComparatorTimeoutMillis               int
	SingularityTimeoutMillis                int
	CbXsellStaticHotelTimeoutMillis         int
	CBFetchCollectionsTimeoutMillis         int
	HotelLimitForCollections                int
	AltAccoConnectionTimeoutMillis          int
	AltAccoSocketTimeout                    int
	DefaultAP                               int
	DefaultCityCode                         string
	DefaultCityName                         string
	DefaultCountryCode                      string
	DefaultLOS                              int
	DefaultRSC                              string
	CgSearchHotelsTimeoutMillis             int
	CgGiSearchHotelsTimeoutMillis           int
	HesCrossSellCardsDataTimeoutMillis      int
	CategoryPriorityList                    []string
	DynamicPersuasionData                   string
	CategoryIconsMap                        string
	OfferText                               string
	CouponCode                              string
	OfferPersuasionsMap                     string
}

type kafkaConfig struct {
	Brokers             string
	KafkaKeytabPath     string
	KafkaServiceName    string
	KafkaPrincipal      string
	KafkaSessionTimeout int
	CrossellTopicId     string
}

type avroConfig struct {
	CrossellTemplateId string
	AvroSchemaURL      string
}

type defaultSearchContext struct {
	LocationIdForFetchCollections string
	CountryIdForFetchCollections  string
	CheckinDateFromCurrent        int
	CheckoutDateFromCurrent       int
	RoomStayCandidates            int
	LocationType                  string
}
