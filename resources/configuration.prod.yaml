port: 8080
couch:
  hosts: cb-42-70.mmt.mmt,cb-42-71.mmt.mmt,cb-42-72.mmt.mmt,cb-42-73.mmt.mmt
  bucket: hotel-cosmos
  keyValueTimeoutInMillis: 200
ttlInSeconds: 14400
cityTaxExclusiveCountries: "TH,SG,ID,MY,IN"
trafficSource:
  source: "CROSSSELL"
  type: "CMP"
############## URLS #############
urls:
  cbSearchHotels: "http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/searchHotels?countryCode=IN&srcClient=CROSSSELL"
  cbCommparator: "http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/hotelComparator?countryCode=IN&srcClient=SCION"
  cbXsellStaticDetails: "http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/xsell/staticDetail?srcClient=SCION"
  singularityCrossel: "http://hotels-singularity.ecs.mmt/Hotels-Singularity/v1/getHotelFromPriorBooking?srcClient=SCION"
  pmsCommonConfigUrl: "http://fpm.mmt.mmt:8080/pms/client/getPropertyFile?id=commonProps&srcClient=SCION"
  cbFetchCollections: "http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/fetchCollections?srcClient=SCION"
  webApiAltAccoProperty: "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/altaccodata?srcClient=SCION"
  CGSearchContextDrools: "http://htlwebapi.ecs.mmt/hotels-entity/api/v2.0/crossSell?srcClient=SCION"
  cgSearchHotels: "http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/searchHotels?countryCode=IN&srcClient=SCION"
  cgGiSearchHotels: "http://hotels-gi-clientgateway-oth.ecs.mmt/clientbackend-gi/entity/api/searchHotels?countryCode=IN&srcClient=SCION"
  HesCardsData: "http://htlwebapi-oth.ecs.mmt/hotels-entity/api/v2.0/getCard?srcClient=SCION"
  webApiCityDetails: "http://htlwebapi.ecs.mmt/hotels-entity/api/v4.0/hotels/suggest/getcitydetails?srcClient=SCION"
  cbSearchPersonalizedHotels: "http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/searchPersonalizedHotels?srcClient=SCION"
  cgFetchCollections: "http://hotels-clientgateway-oth.ecs.mmt/clientbackend/cg/fetchCollections/%s/2?language=eng&region=in&currency=inr&srcClient=SCION"
  cgLandingCardHotels: "http://hotels-clientgateway-oth.ecs.mmt/clientbackend/entity/api/landingDiscovery?countryCode=IN&srcClient=SCION"
  cgMobLandingMMT: "http://hotels-clientgateway-oth.ecs.mmt/clientbackend/cg/mob-landing/%s/2?countryCode=IN&srcClient=SCION"

heading:
  defaultheading : "<NOP> properties available"
  defaultsubheading: "<NOP> properties available"
  

kafkaConfig:
  brokers: "kafka-hotels.mmt.mmt:9092"
  kafkaKeytabPath: "/etc/kerberos/hotels_scion.keytab"
  kafkaServiceName: "kafka"
  kafkaPrincipal: "hotels_entity"
  kafkaSessionTimeout: 6000
  crossellTopicId: "cross_sell_hotel_requests"

avroConfig:
  avroSchemaURL : "http://dpt-columbus.ecs.mmt/confluent/schemas/ids/%s"
  crossellTemplateId : "20990"


############## URLS #############
#Desktop hotel detail deeplink
hotelDetailDeeplinkURL: https://www.makemytrip.com/hotels/hotel-details?hotelId=%s&checkin=%s&checkout=%s&country=%s&city=%s&roomStayQualifier=%s&_uCurrency=%s&openDetail=true&checkAvailability=true
#App hotel detail deeplink
hotelDetailAppDeeplinkURL: mmyt://htl/listing/?hotelId=%s&checkin=%s&checkout=%s&country=%s&city=%s&roomStayQualifier=%s&currency=%s&openDetail=true&checkAvailability=true
#Desktop listing deeplink
hotelListingDeeplinkURL: https://www.makemytrip.com/hotels/hotel-listing/?checkin=%s&checkout=%s&city=%s&country=%s&homestay=true&roomStayQualifier=%s&checkAvailability=true
#Get AltAcco property heading
AltAccoPropertyHeading: Explore plush villas, chic apartments & cozy homestays

#locationDetails flag for collection
locationDetailsFlag: false

#Alt-Acco Sufficient City List
AASufficientCityList: CTGOI,CTXCR,CTSXR,CTDEL,CTBLR,CTXLK,CTXWA,CTHYDERA,CTMAA,CTKUU,CTXMH,CTSLV,CTXOO,CTCOK,CTCCU,CTXGH,CTGGN,CTDED,CTBOM,CTJAI,CTNOI,CTXLL,CTXZS,CTXMN,CTXRI,CTPONDI,CTUDR,CTXKS,CTIXB,CTKANG,CTXKO,CTGAU,CTXML,CTPNQ,CTXMS,CTXKT,CTXMK,CTATQ,CTJSA,CTXNT,CTVNS,CTXBH,CTKSL,CTJIBH,CTXVE,CTMUDI,CTJDH,CTIXZ,CTXGO,CTISK,CTXMY,CTXKL,CTTRV,CTSHL,CTMSBA,CTXCN,CTCJB,CTSKR,CTKOTAG,CTXDP,CTIXL,CTBANJ,CTXTH

#default Search Context entries in case empty SC received from Emperia
defaultSearchContext :
  LocationIdForFetchCollections : "CTGOI"
  CountryIdForFetchCollections : "IN"
  CheckinDateFromCurrent: 2
  CheckoutDateFromCurrent: 3
  RoomStayCandidates: 2
  LocationType: "city"

#Cards for which we need to return hotel persuasions
CardsForPersuasion : HOTEL_CROSS_SELL,HOTEL_XSELL

CrosssellBookerHydraSegments : {"Flyer":["r2011","r2012","r2013","r2015","r2016","r2017","r2018","r2023"],"Bus":["r1717","r2061"],"Train":["r1647","r1653"]}

#City to zone map for landing discovery handpicked properties card
CityToZoneMap : {"CTBOM":["ZNMUMBA"],"CTDEL":["ZNDELHI"],"CTBLR":["ZNBENGA"],"CTJAI":["ZNJAIPU"],"CTMAA":["ZNCHEN"],"CTHYDERA":["ZNHYD"],"CTCCU":["ZNKOLK"],"CTPNQ":["ZNPUN3744B525"],"RGNCR":["ZNDELHI"],"CTGGN":["ZNDELHI"],"CTXLK":["ZNMUMBA"],"CTAMD":["ZNAHMED"],"CTCOK":["ZNCOCH"],"CTLKO":["ZNLUC"],"CTAGR":["ZNAGRA"],"ATATQ":["ZNAMR"],"CTIXC":["ZNCHAN"],"CTVNS":["ZNVAR"],"CTIDR":["ZNINDO"],"CTSXR":["ZNSRI"],"CTCJB":["ZNCOI"],"RGBOM":["ZNMUMBA"],"RGCDRH":["ZNCHAN"],"RGGGN":["ZNDELHI"],"RGJDH":["ZNJOD"]}