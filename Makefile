#Variables
PROJECT_NAME=hotels-scion

RED=\033[0;31m
GREEN=\033[0;32m
YELLOW=\033[0;33m
NC=\033[0m # No Color

define print_status
	@echo "$(GREEN)$(1)$(NC)"
endef

define print_warning
	@echo "$(YELLOW)$(1)$(NC)"
endef

define print_error
	@echo "$(RED)$(1)$(NC)"
endef

default: dependencies build artifacts

.PHONY: dependencies build clean docker-build docker-clean

clean:
	rm -rf $(PROJECT_NAME)

dependencies:
	@echo Fetching dependencies...
	go mod tidy -v
	@echo Dependencies fetched.

build: dependencies
	@echo Building Server...
	go build -v -o $(PROJECT_NAME) -tags dynamic
	@echo Built Server.

onlybuild:
	@echo "Building Server(Not fetching dependencies)..."
	go build -v -o $(PROJECT_NAME) -tags dynamic
	@echo Built Server.

clean-build: clean dependencies
	@echo Building Server...
	go build -v -o $(PROJECT_NAME) -tags dynamic
	@echo Built Server.

artifacts: onlybuild
	@echo "Creating artifacts..."
	tar --transform "s|^|opt/hotels-scion/|" -czvf $(ASSEMBLY_PREFIX)-server-$(ASSEMBLY_SUFFIX).tar.gz $(PROJECT_NAME) resources/
	@echo "Created artifacts."


coverage:
	go test -coverprofile cover.out `go list ./...| grep -v "cmd\|dto\|mocks\|model\|models\|.*pb$$"` -cover

