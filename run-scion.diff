diff --git a/main.go b/main.go
index 7579dec..22e0848 100644
--- a/main.go
+++ b/main.go
@@ -7,7 +7,6 @@ import (
 	"Hotels-Scion/handlers"
 	"Hotels-Scion/utils/avro"
 	"Hotels-Scion/utils/cache"
-	"Hotels-Scion/utils/kafka"
 	"Hotels-Scion/utils/logging"
 	"io"
 	"os"
@@ -42,7 +41,7 @@ func main() {
 	config.Initialize(*confPath, *env)
 	cache.InitializeCouchDb()
 	restconnectors.InitHTTPClients()
-	kafka.InitKafkaProducer()
+	//kafka.InitKafkaProducer()
 	avro.InitializeAvroSchemas()
 	initializeHTTPServer()
 	log.Info("Started HOTELS SCION")
diff --git a/utils/kafka/producer.go b/utils/kafka/producer.go
index f421504..3222fb5 100644
--- a/utils/kafka/producer.go
+++ b/utils/kafka/producer.go
@@ -1,5 +1,5 @@
 package kafka
-
+/*
 import (
 	"Hotels-Scion/config"
 	"Hotels-Scion/utils/kafka/confluent"
@@ -79,3 +79,4 @@ func (w *ProducerWrapper) SendMessage(topic string, key string, value interface{
 func (w *ProducerWrapper) Close() {
 	w.Producer.Close()
 }
+*/
\ No newline at end of file
diff --git a/utils/pdt/pdt_logging.go b/utils/pdt/pdt_logging.go
index 17e1d23..7821fa4 100644
--- a/utils/pdt/pdt_logging.go
+++ b/utils/pdt/pdt_logging.go
@@ -3,7 +3,8 @@ package pdt
 import (
 	"Hotels-Scion/models/pdt"
 	"Hotels-Scion/utils/avro"
-	"Hotels-Scion/utils/kafka"
+	"fmt"
+
 	"Hotels-Scion/utils/logging"
 	"encoding/binary"
 	"encoding/json"
@@ -38,13 +39,14 @@ func Log(pdtModel pdt.PdtModelInterface, topic, template string) {
 	log.Debugf("Logging PDT data : %v", string(b))
 	binary, err := codec.BinaryFromNative(nil, mappedData)
 	request := append(hdr, binary...)
+	fmt.Println(request)
 
 	//e.Info("request", request)
 	if err != nil {
 		log.Error("Issue occurred while converting from native go to binary using avro schema " + err.Error())
 		return
 	}
-	err = kafka.KafkaProducer.SendMessage(topic, "", request)
+	//err = kafka.KafkaProducer.SendMessage(topic, "", request)
 	if err != nil {
 		log.Error("Error occurred while pushing PDT data : " + err.Error())
 		return
