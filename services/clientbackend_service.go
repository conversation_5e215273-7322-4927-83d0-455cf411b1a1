package services

import (
	"Hotels-Scion/config"
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/models"
	"Hotels-Scion/models/common"
	"Hotels-Scion/models/downstream/clientbackend"
	"encoding/json"
	"strings"
)

func getHotelsCrosellStaticData(ids []string, requestBO *models.RequestBO, cityCode string) clientbackend.CrossellStaticDataResponse {
	logger.Infof("getting static data, correlationKey %s", requestBO.CorrelationKey)
	req := clientbackend.CrossellStaticDataRequest{}
	req.HotelIds = ids
	req.CityID = cityCode
	req.ResponseFilters = []string{"images"}
	imgCat := clientbackend.ImageCategory{Category: "H", Count: 6, Height: 0, Width: 0}
	req.ImageCategories = []clientbackend.ImageCategory{imgCat}

	resp := restconnectors.PostCrosellStaticDataRequest(&req)
	return resp
}

func GetHotelSearchData(htlIds []string, requestBO *models.RequestBO, searchContext *common.SearchContext) clientbackend.SearchHotelsResponse {
	logger.Infof("getting search hotels data, correlationKey %s", requestBO.CorrelationKey)
	req := clientbackend.SearchHotelsRequest{}
	req.HotelIDList = htlIds
	req.CityCode = searchContext.CityCode
	req.CountryCode = searchContext.CountryCode
	req.RoomStayCandidates = getRoomStayCandidates(searchContext.RoomStayParams)
	req.Checkin = searchContext.Checkin
	req.Checkout = searchContext.Checkout
	req.MmtAuth = requestBO.MmtAuth
	req.UUID = requestBO.Uuid
	req.ProfileType = requestBO.ProfileType
	req.VisitorID = requestBO.VisitorId
	req.CorrelationKey = requestBO.CorrelationKey
	req.AppVersion = requestBO.AppVersion
	req.IDContext = requestBO.IdContext
	req.RequestType = requestBO.RequestType
	req.DeviceType = "Mobile"
	req.ExperimentData = "{\"PDO\":\"PN\"}"
	req.Limit = len(htlIds)
	if "PWA" == requestBO.OSType {
		req.BookingDevice = "msite"
		req.DeviceID = "PWA"
		req.Channel = "PWA"
	} else {
		req.BookingDevice = requestBO.BookingDevice
		req.DeviceID = requestBO.DeviceId
		req.Channel = requestBO.Channel
	}
	req.ResponseFilterFlags = clientbackend.ResponseFilterFlags{}
	if strings.Contains(config.AppConfig.CityTaxExclusiveCountries, searchContext.CountryCode) {
		req.ResponseFilterFlags.CityTaxExclusive = true
	}
	req.ResponseFilterFlags.WalletRequired = true
	req.ResponseFilterFlags.BestCoupon = true
	req.ResponseFilterFlags.PriceInfoReq = true

	req.TrafficSource = clientbackend.TrafficSource{}
	req.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	req.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	req.LoggedIn = true
	req.PageContext = "LISTING"
	req.FirstTimeUser = false

	return restconnectors.PostSearchHotelsRequest(&req)

}

func getRoomStayCandidates(roomStayParams []common.RoomStayParams) []clientbackend.RoomStayCandidates {
	rmStyArr := make([]clientbackend.RoomStayCandidates, len(roomStayParams))
	for i, rmstyPrm := range roomStayParams {
		rmsty := clientbackend.RoomStayCandidates{}
		gstCountsArr := make([]clientbackend.GuestCount, len(rmstyPrm.GuestCounts))
		for j, gstCountRstyPrm := range rmstyPrm.GuestCounts {
			gstCount := clientbackend.GuestCount{}
			gstCount.Count = gstCountRstyPrm.Count
			gstCount.AgeQualifyingCode = gstCountRstyPrm.AgeQualifyingCode
			gstCountsArr[j] = gstCount
		}
		rmsty.GuestCounts = gstCountsArr
		rmStyArr[i] = rmsty
	}
	return rmStyArr
}

func FetchHotelCollections(requestBO *models.RequestBO) (*json.RawMessage, error) {
	req := clientbackend.SearchHotelsRequest{}
	req.LocationID= requestBO.LocationName
	req.LocationType=requestBO.LocationType
	req.CountryCode=requestBO.CountryCode
	req.Checkin = requestBO.Checkin
	req.Checkout = requestBO.Checkout
	req.MmtAuth = requestBO.MmtAuth
	req.UUID = requestBO.Uuid
	req.ProfileType = requestBO.ProfileType
	req.VisitorID = requestBO.VisitorId
	req.CorrelationKey = requestBO.CorrelationKey
	req.AppVersion = requestBO.AppVersion
	req.IDContext = requestBO.IdContext
	req.RequestType = requestBO.RequestType
	req.FunnelSource= "COSMOS"
	req.DeviceType = "Mobile"
	req.ImageCategory = []clientbackend.ImageCategory{{Category: "H", Count: 1}}
	req.ImageType = []string{"professional"}
	if requestBO.ExpDataStr != "" {
		req.ExperimentData = requestBO.ExpDataStr
	} else {
		req.ExperimentData = "{\"PDO\":\"PN\"}"
	}
	req.Limit = config.PmsCommonConfig.HotelLimitForCollections
	if "PWA" == requestBO.OSType {
		req.BookingDevice = "msite"
		req.DeviceID = "PWA"
		req.Channel = "PWA"
	} else {
		req.BookingDevice = requestBO.BookingDevice
		req.DeviceID = requestBO.DeviceId
		req.Channel = requestBO.Channel
	}

	req.ResponseFilterFlags = clientbackend.ResponseFilterFlags{}
	req.ResponseFilterFlags.CityTaxExclusive = true
	req.ResponseFilterFlags.FlyfishSummaryRequired = true
	req.ResponseFilterFlags.WalletRequired = true
	req.ResponseFilterFlags.BestCoupon = true
	req.ResponseFilterFlags.PriceInfoReq = true

	req.TrafficSource = clientbackend.TrafficSource{}
	req.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	req.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	req.LoggedIn = true
	req.FirstTimeUser = false
	req.PageContext=requestBO.PageContext

	req.TrendingNow = requestBO.TrendingNow
	req.CollectionRequired = requestBO.CollectionRequired
	if requestBO.AthenaCategory != "" {
		req.AthenaCategory = requestBO.AthenaCategory
	} else {
		req.AthenaCategory = "all"
	}

	return restconnectors.PostFetchCollectionsRequest(&req)

}
