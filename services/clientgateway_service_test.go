package services

import (
	restconnectors "Hotels-Scion/connectors"
	common "Hotels-Scion/models/common"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"bytes"
	"encoding/json"
	"io/ioutil"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
)

var (
	getRequestDrools                   func(request *request.CrossSellDrools) request.CrossSellDrools
	getRequestSearchHotels             func(request *clientbackend.SearchHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchHotelsData
	getRequestSearchPersonalizedHotels func(request *clientbackend.SearchPersonalizedHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchPersonalizedData
	getFetchCollectionRequest          func(cg *request.FetchCollectionRequestCG, context *gin.Context) (response.FetchCollectionResponseCG, error)
)

type getClientMock struct{}

func (cm *getClientMock) PostXSellSearchHotelsRequestAsync(hotelsRequest *clientbackend.SearchHotelsRequest, context *gin.Context, data chan clientgateway.CrossSellSearchHotelsData) {
	//this function declaration is necessary to use getClientMock
	panic("To use getClientMock")
}

func (cm *getClientMock) PostFetchCollectionRequest(cg *request.FetchCollectionRequestCG, context *gin.Context) (response.FetchCollectionResponseCG, error) {
	return getFetchCollectionRequest(cg, context)
}

//We are mocking the client method "PostDroolsDataRequest"
func (cm *getClientMock) PostDroolsDataRequest(request *request.CrossSellDrools) request.CrossSellDrools {
	return getRequestDrools(request)
}

//We are mocking the client method "PostXSellSearchHotelsRequest"
func (cm *getClientMock) PostXSellSearchHotelsRequest(request *clientbackend.SearchHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchHotelsData {
	return getRequestSearchHotels(request, context)
}

//We are mocking the client method "PostXSellSearchHotelsRequest"
func (cm *getClientMock) PostXSellSearchPersonalizedRequestCorp(request *clientbackend.SearchPersonalizedHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchPersonalizedData {
	return getRequestSearchPersonalizedHotels(request, context)
}

func TestGetHotelSearchDataForCrossSell(t *testing.T) {
	getRequestDrools = func(request *request.CrossSellDrools) request.CrossSellDrools {

		json.Unmarshal(getBytes("droolresponse.json"), request)

		return *request
	}

	getRequestSearchHotels = func(request *clientbackend.SearchHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchHotelsData {
		return clientgateway.CrossSellSearchHotelsData{}
	}

	restconnectors.ClientStruct = &getClientMock{} //without this line, the real api is fired

	sEvent := request.SearchEvent{}
	json.Unmarshal(getBytes("searchevent.json"), &sEvent)

	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	r := http.Request{}
	h := http.Header{}
	r.Header = h
	c.Request = &r
	c.Request.Header.Set("uuid", "1234")
	c.Request.Header.Set("currency", "INR")
	c.Request.Header.Set("deviceid", "1234")
	c.Request.Header.Set("channel", "IOS")
	c.Request.Header.Set("profiletype", "BUSINESS")
	user := request.User{}
	crossSellReq := request.CrossSellRequest{}

	response, _, _, _ := getHotelSearchDataForCrossSell(&sEvent, c, "Listing", "{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", "http", 2, false, &user, false, crossSellReq)
	assert.NotNil(t, response)
	//assert.Nil(t, err)

}

func TestGetHotelSearchDataForCrossSellCorp(t *testing.T) {
	getRequestDrools = func(request *request.CrossSellDrools) request.CrossSellDrools {

		json.Unmarshal(getBytes("droolresponse.json"), request)

		return *request
	}

	getRequestSearchPersonalizedHotels = func(request *clientbackend.SearchPersonalizedHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchPersonalizedData {
		return clientgateway.CrossSellSearchPersonalizedData{}
	}

	restconnectors.ClientStruct = &getClientMock{} //without this line, the real api is fired

	sEvent := request.SearchEvent{}
	json.Unmarshal(getBytes("searchevent.json"), &sEvent)

	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	r := http.Request{}
	h := http.Header{}
	r.Header = h
	c.Request = &r
	c.Request.Header.Set("uuid", "1234")
	c.Request.Header.Set("currency", "INR")
	c.Request.Header.Set("deviceid", "1234")
	c.Request.Header.Set("channel", "IOS")
	c.Request.Header.Set("profiletype", "BUSINESS")
	user := request.User{}

	crossSellReq := request.CrossSellRequest{}

	response, _, _, _ := getHotelSearchDataForCrossSellCorp(&sEvent, c, "Listing", "{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", "http", 2, false, &user, true, crossSellReq)
	assert.NotNil(t, response)
	//assert.Nil(t, err)

}

func getBytes(path string) []byte {
	b, err := ioutil.ReadFile("testdata/" + path)
	if err != nil {
		panic(err)
	}
	return b
}

func TestGetCrossSellDroolRequest(t *testing.T) {
	sEvent := request.SearchEvent{}
	json.Unmarshal(getBytes("searchevent.json"), &sEvent)
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	buf := new(bytes.Buffer)
	c.Request, _ = http.NewRequest("POST", "/", buf)
	c.Request.Header.Set("profiletype", "test")
	dreq, _ := getCrossSellDroolRequest(&sEvent, c, true)

	assert.Equal(t, 3, len(dreq.Price), "Length of Price slice should be 3")
}

func TestCreateRequestForCrossSell(t *testing.T) {
	req := clientbackend.SearchHotelsRequest{}
	dres := request.CrossSellDrools{}
	json.Unmarshal(getBytes("droolresponse.json"), &dres)
	sEvent := request.SearchEvent{}
	filterVal := "test"
	filterRange := common.FilterRange{
		20,
		30,
	}
	appliedFilterMap := map[common.Filter_Group][]common.Filter{
		"test": {
			{"test", &filterVal, &filterRange, false},
		},
	}
	sEvent.AppliedFilterMap = &appliedFilterMap
	json.Unmarshal(getBytes("searchevent.json"), &sEvent)
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	r := http.Request{}
	h := http.Header{}
	r.Header = h
	c.Request = &r
	c.Request.Header.Set("uuid", "1234")
	user := request.User{}

	crossSellReq := request.CrossSellRequest{}
	crossSellReq.ImageCategory = []clientbackend.ImageCategory{
		{"H",
			5,
			388,
			523,
		},
	}
	createRequestForCrossSell(&sEvent, &req, "Listing", "{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", c, &dres, "http", 2, false, &user, crossSellReq)

	//test for room stay candidates
	assert.Equal(t, 1, len(req.RoomStayCandidates), "Length of RSC slice should be 1")
	//test for fly fsih summary request
	assert.NotNil(t, req.FlyfishSummaryRequest, "Fly fish summary request Should not be nil")
	//test for applied filter map
	assert.NotEmpty(t, req.AppliedFilterMap, "Applied filter map should not be empty")
	//test for match maker request
	assert.NotNil(t, req.MatchMakerRequest, "Must get match maker request")
	//test for hotel Ids
	assert.Equal(t, 3, len(req.MatchMakerRequest.Hotels), "Length of hotel ids should be 3")

	//empty drool response
	dres = request.CrossSellDrools{}
	createRequestForCrossSell(&sEvent, &req, "Listing", "{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", c, &dres, "http", 2, false, &user, crossSellReq)
	//assert.NotEmpty(t, req.AppliedFilterMap, "Applied filter map should not be empty")
}

func TestGetHotelSearchDataForCrossSellV2(t *testing.T) {
	getRequestDrools = func(request *request.CrossSellDrools) request.CrossSellDrools {

		json.Unmarshal(getBytes("droolresponse.json"), request)

		return *request
	}
	getRequestSearchHotels = func(request *clientbackend.SearchHotelsRequest, context *gin.Context) clientgateway.CrossSellSearchHotelsData {
		return clientgateway.CrossSellSearchHotelsData{}
	}
	restconnectors.ClientStruct = &getClientMock{} //without this line, the real api is fired
	sEvent := request.SearchEvent{}
	json.Unmarshal(getBytes("searchevent.json"), &sEvent)
	c, _ := gin.CreateTestContext(httptest.NewRecorder())
	r := http.Request{}
	h := http.Header{}
	r.Header = h
	c.Request = &r
	c.Request.Header.Set("uuid", "1234")
	c.Request.Header.Set("currency", "INR")
	c.Request.Header.Set("deviceid", "1234")
	c.Request.Header.Set("channel", "IOS")
	c.Request.Header.Set("profiletype", "B2C")
	user := request.User{}
	sEvent.SearchContext.FunnelSource = "homestay"
	crossSellReq := request.CrossSellRequest{}
	crossSellReq.ImageCategory = []clientbackend.ImageCategory{
		{"H",
			5,
			388,
			523,
		},
	}
	response_hotels, response_homestay, _, _ := getHotelSearchDataForCrossSellV2(&sEvent, c, "Listing",
		"{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", "http", 2, false, &user, false, crossSellReq)
	//assert.Nil(t, response_hotels)
	assert.NotNil(t, response_homestay)
	sEvent.SearchContext.FunnelSource = "hotels"
	sEvent.SearchContext.From.Locus.City = "NOTCTGOI"
	response_hotels, response_homestay, _, _ = getHotelSearchDataForCrossSellV2(&sEvent, c, "Listing",
		"{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", "http", 2, false, &user, false, crossSellReq)
	//assert.Nil(t, response_hotels)
	assert.NotNil(t, response_homestay)
	sEvent.SearchContext.From.Locus.City = "CTGOI"
	response_hotels, response_homestay, _, _ = getHotelSearchDataForCrossSellV2(&sEvent, c, "Listing",
		"{APE:18,PAH:5,PAH5:T,WPAH:F,BNPL:T,MRS:T,PDO:PN,MCUR:T}", "123test", "http", 2, false, &user, false, crossSellReq)
	assert.NotNil(t, response_hotels)
	assert.NotNil(t, response_homestay)
	//assert.Nil(t, err)
}
