package services

import (
	"Hotels-Scion/config"
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/constants"
	"Hotels-Scion/helpers"
	"Hotels-Scion/models"
	"Hotels-Scion/models/common"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/downstream/singularity"
	pdtModels "Hotels-Scion/models/pdt"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"Hotels-Scion/utils"
	"Hotels-Scion/utils/cache"
	"Hotels-Scion/utils/logging"
	"Hotels-Scion/utils/pdt"
	"encoding/json"
	"fmt"
	"reflect"
	"runtime/debug"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"

	guuid "github.com/google/uuid"
)

var logger = logging.GetLogger()

func GetCrossellResponse(requestBO *models.RequestBO) response.HotelCrossSellResponse {
	var resp response.HotelCrossSellResponse
	if len(requestBO.CrossSellUniqueIds) > 0 {
		cachedData := getResponseFromCache(requestBO)
		if len(cachedData.BookingIdResponseMap) > 0 {
			resp = prepareResponseFromCachedData(requestBO, &cachedData)
		} else {
			resp = prepareErrorResponse("No_Data_Found", "Data not found in cache")
		}
	} else if requestBO.PageContext != "" {
		if "HOME" == requestBO.PageContext || "MYTRIP" == requestBO.PageContext {
			cachedData := getResponseFromCache(requestBO)
			if !reflect.ValueOf(cachedData).IsNil() {
				resp = prepareResponseFromCachedData(requestBO, &cachedData)
			} else {
				resp = getNewCrossellResponse(requestBO)
			}
		} else {
			resp = getNewCrossellResponse(requestBO)
		}

	}

	return resp
}

func getNewCrossellResponse(requestBO *models.RequestBO) response.HotelCrossSellResponse {
	logger.Infof("handle new crosell request , correlationKey %s", requestBO.CorrelationKey)
	crossellResp := getSingularityCrossellResponse(requestBO)
	var uuids = make([]string, len(crossellResp.CardDetails))
	for i := range crossellResp.CardDetails {
		uuids[i] = guuid.New().String()
	}
	resp := prepareResponse(uuids)
	go getHotelsResponseAndUpdateCache(requestBO, &crossellResp)
	return resp
}

type result struct {
	id    string
	data  interface{}
	error string
}

func getHotelsResponseAndUpdateCache(requestBO *models.RequestBO, singularityResp *singularity.CrossSellResponse) {
	logger.Infof("Getting static and price data in async , correlationKey %s", requestBO.CorrelationKey)
	var staticDataWaitGrp sync.WaitGroup
	headingToStaticDataResultsMap := make(map[string]clientbackend.CrossellStaticDataResponse)

	var priceDataWaitGrp sync.WaitGroup
	headingToPriceDataResultsMap := make(map[string]clientbackend.SearchHotelsResponse)

	headingToHotelIdsMap := make(map[string][]string)

	for _, card := range singularityResp.CardDetails {
		htlIds := make([]string, len(card.Hotel))
		for i, hotel := range card.Hotel {
			htlIds[i] = hotel.HotelID
		}

		headingToHotelIdsMap[card.Heading] = htlIds

		if !card.SearchContext.IsBooked {
			staticDataWaitGrp.Add(1)
			go getHotelsStaticData(&staticDataWaitGrp, headingToStaticDataResultsMap, card.Heading, htlIds, requestBO, &card.SearchContext)
			priceDataWaitGrp.Add(1)
			go getHotelsSearchData(&priceDataWaitGrp, headingToPriceDataResultsMap, card.Heading, htlIds, requestBO, &card.SearchContext)
		}
	}

	staticDataWaitGrp.Wait()
	cachedRespData := parseStaticDataAndPutInCache(headingToHotelIdsMap, requestBO, singularityResp, headingToStaticDataResultsMap)

	priceDataWaitGrp.Wait()
	parsePriceDataAndPutInCache(requestBO, &cachedRespData, headingToPriceDataResultsMap)
}

func parseStaticDataAndPutInCache(headingToHotelIdsMap map[string][]string, requestBO *models.RequestBO, singularityResp *singularity.CrossSellResponse, headingToStaticDataResultsMap map[string]clientbackend.CrossellStaticDataResponse) response.CrossSellCacheResponse {
	populateHotelList := true
	cardsList := make([]*response.CardDetail, len(singularityResp.CardDetails))

	headingToCardMap := make(map[string]singularity.CardDetail)
	for _, card := range singularityResp.CardDetails {
		headingToCardMap[card.Heading] = card
	}

	//In case of multicity populate only if hotels for all cards present
	if requestBO.ExpData.MultiCity {
		for hdng_ky := range headingToCardMap {
			if len(headingToStaticDataResultsMap[hdng_ky].StaticDetailResponse) == 0 {
				populateHotelList = false
				break
			}
		}
	}

	respCardIndx := -1
	for heading, hotelIds := range headingToHotelIdsMap {
		respCardIndx++
		var htlIdRespMap map[string]clientbackend.HotelXsellStaticDetailResponse
		htlIdRespMap = headingToStaticDataResultsMap[heading].StaticDetailResponse

		cardDtl := response.CardDetail{}
		cardDtl.Heading = heading
		cardDtl.SearchContext = headingToCardMap[heading].SearchContext
		cardDtl.ViewMore = headingToCardMap[heading].ViewMore
		cardDtl.MoreDetails = headingToCardMap[heading].MoreDetails
		htlList := make([]*response.Hotel, len(htlIdRespMap))
		appDeepLink := getBaseHotelDetailsDeepLink(&cardDtl.SearchContext, config.AppConfig.HotelDetailAppDeeplinkURL, "INR")
		deskTopDeepLink := getBaseHotelDetailsDeepLink(&cardDtl.SearchContext, config.AppConfig.HotelDetailDeeplinkURL, "INR")
		if populateHotelList {
			respHtlIndx := -1
			for _, htlId := range hotelIds {
				if staticResp, ok := htlIdRespMap[htlId]; ok {
					respHtlIndx++
					respHtl := response.Hotel{}
					respHtl.Id = staticResp.HotelResult.ID
					respHtl.Name = staticResp.HotelResult.Name
					respHtl.StarRating = staticResp.HotelResult.StarRating
					respHtl.Images = staticResp.HotelResult.Images
					respHtl.Address = response.HotelAddress{}
					respHtl.Address.Line1 = staticResp.HotelResult.Addr1
					respHtl.Address.Line2 = staticResp.HotelResult.Addr2
					if summary, ok := staticResp.FlyfishSummary["MMT"]; ok {
						respHtl.UserRating = summary.CumulativeRating
					}
					if appDeepLink != "" {
						respHtl.AppDeeplink = fmt.Sprintf(appDeepLink, htlId)
					}
					if deskTopDeepLink != "" {
						respHtl.DesktopDeeplink = fmt.Sprintf(deskTopDeepLink, htlId)
					}

					htlList[respHtlIndx] = &respHtl
				}
			}
		}

		cardDtl.Hotels = htlList
		cardsList[respCardIndx] = &cardDtl
	}

	resp := response.HotelCrossSellResponse{}
	resp.OfferData = response.OfferData{}
	resp.OfferData.CrossSellPersonalised = &response.CrossSellPersonalised{}
	resp.OfferData.CrossSellPersonalised.Data = response.CrossSellPersonalisedData{}
	resp.OfferData.CrossSellPersonalised.Data.Heading = singularityResp.Heading
	resp.OfferData.CrossSellPersonalised.Data.CardDetails = cardsList
	resp.OfferData.CrossSellPersonalised.Data.BookingIds = singularityResp.BookingIds

	cacheResp := response.CrossSellCacheResponse{}

	var ltstKy string
	cacheMap := make(map[string]*response.HotelCrossSellResponse)
	if "HOME" == requestBO.PageContext {
		ltstKy = strings.Join(singularityResp.BookingIds, ",")
		cacheResp.Latest = ltstKy
	} else {
		ltstKy = requestBO.BookingId
	}
	cacheMap[ltstKy] = &resp

	cacheResp.BookingIdResponseMap = cacheMap

	logger.Infof("Updating static data in cache , correlationKey %s", requestBO.CorrelationKey)
	cache.Upsert(getCouchKey(requestBO), cacheResp, time.Duration(config.AppConfig.TTLInSeconds)*time.Second, config.AppConfig.Couch.Bucket)

	return cacheResp

}

func getBaseHotelDetailsDeepLink(searchContext *common.SearchContext, deeplink string, currency string) string {
	var url string
	var cityCode string
	if searchContext.LocusData.LocusId != "" {
		cityCode = searchContext.LocusData.LocusId
	} else {
		cityCode = searchContext.CityCode
	}
	roomStayParam := getRoomStayQualifier(searchContext.RoomStayParams)
	checkin := utils.FormatDateString(searchContext.Checkin, constants.DT_FRMT_SEARCH_CNTXT, constants.DT_FRMT_DEEPLINK_URL)
	checkout := utils.FormatDateString(searchContext.Checkout, constants.DT_FRMT_SEARCH_CNTXT, constants.DT_FRMT_DEEPLINK_URL)
	url = fmt.Sprintf(deeplink, "%s", checkin, checkout, searchContext.CountryCode, cityCode, roomStayParam, currency)

	if searchContext.LocusData.LocusId != "" && searchContext.LocusData.LocusType != "" {
		url += constants.AND_SEPARATOR + constants.LOCUS_LOCATION_ID + constants.PR_SEPARATOR + searchContext.LocusData.LocusId
		url += constants.AND_SEPARATOR + constants.LOCUS_TYPE + constants.PR_SEPARATOR + searchContext.LocusData.LocusType
	}

	return url
}

func getRoomStayQualifier(roomStayParams []common.RoomStayParams) string {
	roomStayPrefix := ""
	for _, roomStayParam := range roomStayParams {
		for _, guestCount := range roomStayParam.GuestCounts {
			adltCnt, _ := strconv.Atoi(guestCount.Count)
			chldCnt := len(guestCount.Ages)
			roomStayPrefix += strconv.Itoa(adltCnt)
			roomStayPrefix += constants.RSQ_SPLITTER
			roomStayPrefix += strconv.Itoa(chldCnt)
			roomStayPrefix += constants.RSQ_SPLITTER
			for _, age := range guestCount.Ages {
				if age >= 0 && age <= 2 {
					roomStayPrefix += "1"
				} else if age >= 3 && age <= 6 {
					roomStayPrefix += "3"
				} else {
					roomStayPrefix += "7"
				}
				roomStayPrefix += constants.RSQ_SPLITTER
			}
		}
	}
	return roomStayPrefix
}

func parsePriceDataAndPutInCache(requestBO *models.RequestBO, cacheResponse *response.CrossSellCacheResponse, searchResp map[string]clientbackend.SearchHotelsResponse) {
	for crsl_ky := range cacheResponse.BookingIdResponseMap { // map contains only one key,value pair
		var crsslRespRef = cacheResponse.BookingIdResponseMap[crsl_ky]
		cachedCardDetailsList := crsslRespRef.OfferData.CrossSellPersonalised.Data.CardDetails
		for heading, searchResponse := range searchResp {
			if len(searchResponse.HotelList) > 0 && cachedCardDetailsList != nil {
				for _, cachedCard := range cachedCardDetailsList {
					if heading == cachedCard.Heading {
						populateHotelPriceInfo(&searchResponse, cachedCard.Hotels[:])
						break
					}
				}
			} else {
				logger.Errorf("price data not received for heading : %s correlationKey %s", heading, requestBO.CorrelationKey)
			}
		}

		// set best offer details
		steBestOfferDetails(cachedCardDetailsList)
		//set flag to mark price updated
		crsslRespRef.PriceUpdated = true
	}

	logger.Infof("updating price data in cache correlationKey %s", requestBO.CorrelationKey)
	cache.Upsert(getCouchKey(requestBO), cacheResponse, time.Duration(config.AppConfig.TTLInSeconds)*time.Second, config.AppConfig.Couch.Bucket)
}

func steBestOfferDetails(cachedCardDetailsList []*response.CardDetail) {
	if cachedCardDetailsList != nil {
		for cachedCardIndx := range cachedCardDetailsList {
			cachedCard := cachedCardDetailsList[cachedCardIndx]
			if len(cachedCard.Hotels) > 0 {
				var maxCDFDisc float32 = 0
				var maxCouponDtls *response.CouponDetails

				for _, htl := range cachedCard.Hotels {
					if htl.DisplayFare.CDFDiscount > maxCDFDisc {
						maxCDFDisc = htl.DisplayFare.CDFDiscount
						maxCouponDtls = &htl.DisplayFare.CouponDetails
					}
				}

				if maxCouponDtls != nil {
					cachedCard.BestOfferDetails = response.BestOfferDetails{}
					cachedCard.BestOfferDetails.Description = maxCouponDtls.Description
				}
			}
		}
	}
}

func populateHotelPriceInfo(searchHotelResponse *clientbackend.SearchHotelsResponse, cachedHotels []*response.Hotel) {
	for _, respHtl := range searchHotelResponse.HotelList {
		for _, cachedHtl := range cachedHotels {
			if respHtl.ID == cachedHtl.Id {
				cachedHtl.DisplayFare = response.DisplayFare{}
				cachedHtl.DisplayFare.DisplayPrice = respHtl.DisplayFare.DisplayPriceBreakDown.DisplayPrice
				cachedHtl.DisplayFare.NonDiscountedPrice = respHtl.DisplayFare.DisplayPriceBreakDown.NonDiscountedPrice
				cachedHtl.DisplayFare.CDFDiscount = respHtl.DisplayFare.DisplayPriceBreakDown.CDFDiscount
				cachedHtl.DisplayFare.CouponDetails = response.CouponDetails{}
				cachedHtl.DisplayFare.CouponDetails.Description = respHtl.DisplayFare.DisplayPriceBreakDown.CouponInfo.Description
				cachedHtl.CurrencyCode = searchHotelResponse.Currency
				break
			}
		}
	}
}

func getHotelsStaticData(staticDataWaitGrp *sync.WaitGroup, headingToStaticDataResultsMap map[string]clientbackend.CrossellStaticDataResponse, heading string, htlIds []string, requestBO *models.RequestBO, searchContext *common.SearchContext) {
	defer staticDataWaitGrp.Done()
	staticDataResp := getHotelsCrosellStaticData(htlIds, requestBO, searchContext.CityCode)
	headingToStaticDataResultsMap[heading] = staticDataResp
}

func getHotelsSearchData(priceDataWaitGrp *sync.WaitGroup, headingToPriceDataResultsMap map[string]clientbackend.SearchHotelsResponse, heading string, htlIds []string, requestBO *models.RequestBO, singularitySearchContext *common.SearchContext) {
	defer priceDataWaitGrp.Done()
	searchHotelResp := GetHotelSearchData(htlIds, requestBO, singularitySearchContext)
	if len(searchHotelResp.ResponseErrors.ErrorList) > 0 {
		for _, err := range searchHotelResp.ResponseErrors.ErrorList {
			logger.Errorf("Error in SearchHotel : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, requestBO.CorrelationKey)
		}

	}
	headingToPriceDataResultsMap[heading] = searchHotelResp
}

func prepareResponse(uuids []string) response.HotelCrossSellResponse {
	resp := response.HotelCrossSellResponse{}
	resp.Status = "OK"
	resp.UniqueIds = response.UniqueIds{}
	resp.UniqueIds.CrossSellPersonalised = uuids
	return resp
}

func prepareResponseFromCachedData(requestBo *models.RequestBO, cachedData *response.CrossSellCacheResponse) response.HotelCrossSellResponse {
	resp := response.HotelCrossSellResponse{}
	var cachedCrosellRespRef *response.HotelCrossSellResponse
	if "HOME" == requestBo.PageContext {
		cachedCrosellRespRef = cachedData.BookingIdResponseMap[cachedData.Latest]
	} else {
		cachedCrosellRespRef = cachedData.BookingIdResponseMap[requestBo.BookingId]
	}
	if !cachedCrosellRespRef.PriceUpdated { // set uuids in response if price not updated so far in cache
		resp.UniqueIds.CrossSellPersonalised = requestBo.CrossSellUniqueIds
	}
	resp.OfferData = cachedCrosellRespRef.OfferData
	resp.Status = "OK"
	return resp
}

func getResponseFromCache(requestBo *models.RequestBO) response.CrossSellCacheResponse {
	logger.Infof("in getResponseFromCache correlationKey %s", requestBo.CorrelationKey)
	var cachedData response.CrossSellCacheResponse
	couchKey := getCouchKey(requestBo)
	if len(requestBo.CrossSellUniqueIds) == 0 {
		cachedData = getCachedCrosellData(requestBo.CorrelationKey, couchKey)
	} else {
		threadSleepCount := 5
		for threadSleepCount > 0 {
			time.Sleep(100 * time.Millisecond)
			cachedData = getCachedCrosellData(requestBo.CorrelationKey, couchKey)
			if len(cachedData.BookingIdResponseMap) > 0 {
				break
			} else {
				threadSleepCount--
			}
		}
	}
	return cachedData
}

func getCachedCrosellData(correlationKey string, key string) response.CrossSellCacheResponse {
	resp := response.CrossSellCacheResponse{}
	data, err := cache.Get(key, config.AppConfig.Couch.Bucket)
	if err != nil {
		logger.Errorf("Error in getting data from cache for key : %s  error : %s correlationKey %s", key, err, correlationKey)
	} else {
		_ = json.Unmarshal(data, &resp)
	}
	return resp
}

func getCouchKey(requestBo *models.RequestBO) string {
	var key string
	if requestBo.Uuid != "" && len(requestBo.Uuid) > 0 {
		key = requestBo.Uuid
	} else {
		key = requestBo.DeviceId
	}
	key = key + "_V2"
	return key
}

func prepareErrorResponse(status string, message string) response.HotelCrossSellResponse {
	resp := response.HotelCrossSellResponse{}
	resp.Status = status
	resp.StatusDetails = message
	return resp
}

func GetCrossSellStreamResponse(rsc *request.SearchEvent, ch chan clientgateway.CrossSellStreamResponse, c *gin.Context, pageContext string, expData string, secureURL string, imageCount int, neabBy bool, user *request.User, isCorpRequest bool, offerRequired bool, crossSellRequest request.CrossSellRequest, reqCardId string) {

	var crossSellSH clientgateway.CrossSellSearchHotelsData
	var dres *request.CrossSellDrools
	var crossSellResp clientgateway.CrossSellStreamResponse
	//Set correlation key
	/* correlationKey := rsc.SearchContext.CorrelationKey
	if len(correlationKey) == 0 { */
	correlationKey := guuid.New().String()
	//}
	//set correlation key
	//rsc.SearchContext.CorrelationKey = correlationKey
	//1. Get search hotels from CG
	cards := []clientgateway.Card{}
	card := clientgateway.Card{}
	startTime := time.Now()
	crossSellSH, dres, shreq, dsTime := getHotelSearchDataForCrossSell(rsc, c, pageContext, expData, correlationKey, secureURL, imageCount, neabBy, user, isCorpRequest, crossSellRequest)
	if len(crossSellSH.ResponseErrors.ErrorList) > 0 {
		for _, err := range crossSellSH.ResponseErrors.ErrorList {
			logger.Errorf("Error in SearchHotel : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, correlationKey)
		}
	} else {
		//2. Map search hotels response for cross sell response
		var heading string
		var subheading string
		areas := getAreasFromRequest(rsc)
		appDesignVersion := c.GetHeader("appDesignVersion")
		if neabBy && len(crossSellSH.HotelList) != 0 {
			hotelElement := crossSellSH.HotelList[0]
			crossSellSH.FromCity = hotelElement.FromCity
		}

		r := strings.NewReplacer("<NOP>", strconv.Itoa(crossSellSH.TotalHotelCounts),
			"<CityName>", crossSellSH.CityName, "<Area>", areas, "<FromCity>", crossSellSH.FromCity, "<LocusName>", crossSellSH.LocusData.LocusName)
		premiumHomepage := checkIfPremiumHomepage(c)
		if reflect.DeepEqual(*dres, request.CrossSellDrools{}) && len(dres.Heading) == 0 {
			heading = config.AppConfig.Heading.Defaultheading
		} else {
			if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.FUNNEL_SOURCE_SHORTSTAYS) {
				heading = constants.SHORTSTAYS_CARD_HEADING
				if premiumHomepage {
					heading = constants.SHORTSTAYS_CARD_HEADING_LUXE
				}
			} else if crossSellSH.FiltersRemoved {
				heading = dres.DefaultHeading
			} else {
				heading = dres.Heading
			}
		}
		heading = r.Replace(heading)
		if reflect.DeepEqual(*dres, request.CrossSellDrools{}) && len(dres.SubHeading) == 0 {
			subheading = config.AppConfig.Heading.Defaultsubheading
		} else {
			if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.FUNNEL_SOURCE_SHORTSTAYS) {
				subheading = constants.SHORTSTAYS_CARD_SUB_HEADING
				if premiumHomepage {
					subheading = constants.SHORTSTAYS_CARD_SUB_HEADING_LUXE
				}
			} else if crossSellSH.FiltersRemoved {
				subheading = dres.DefaultSubHeading
			} else {
				subheading = dres.SubHeading
			}
		}
		subheading = r.Replace(subheading)
		header := clientgateway.Header{
			Header:    heading,
			Subheader: subheading,
		}

		if strings.EqualFold(appDesignVersion, "v2") && !premiumHomepage {
			header.Subheader = heading
			header.LobSubheader = subheading
		}
		modifyAddress(crossSellSH, reqCardId)
		crossSellSH.DeeplinkURL = crossSellSH.ListingDeepLink
		crossSellSH.ListingDeepLink = ""

		filterList := []clientgateway.Filter{}

		// create filterMap such that if DATE_FILTER in request and nearby is true , only DATE_FILTER in response
		if neabBy && rsc.Filter && len(rsc.FilterList) != 0 {

			for _, filter := range rsc.FilterList {

				if filter.FilterType != "DATE_FILTER" {
					continue
				}

				buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, crossSellSH.HotelList)
				filterList = append(filterList, filter)
			}
		} else if neabBy {
			// if nearby is true but no date_filter in request , we send distance_filter in response

			if len(crossSellSH.FilterMap) != 0 {

				values := []clientgateway.Filter{}
				ok := false
				values, ok = crossSellSH.FilterMap["DRIVING_DISTANCE_KM"]
				if ok {

					for _, filter := range values {
						//buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, crossSellSH.HotelList)
						filter.FilterName = filter.Title
						filter.Title = ""
						// appending filter in response only if hotel ids present for that case
						if len(filter.HotelIDs) != 0 {
							buildDeeplinkUrl(&filter, crossSellSH.DeeplinkURL)
							filterList = append(filterList, filter)
						}
					}
				}

			}
		} else {
			//create filter list from filter map if nearby false
			if len(crossSellSH.FilterMap) != 0 {
				for key, filters := range crossSellSH.FilterMap {
					createFilterMap(filters, key, filterList)
				}
			}
		}
		crossSellSH.FilterList = filterList
		crossSellSH.FilterMap = nil
		card.HeaderData = header

	}

	// 2. process persuasions
	if rsc.LandingDiscoveryPage {
		processLandingPersuasionData(crossSellSH)
	} else {
		processPersuasionData(crossSellSH)
	}

	// 3. process placeholders
	plcHolders := processPlaceHolders(crossSellSH)

	updateOfferPersuasions(offerRequired, &crossSellSH)
	if !checkIfPremiumHomepage(c) && strings.EqualFold(rsc.SearchContext.FunnelSource, constants.FUNNEL_SOURCE_SHORTSTAYS) {
		addPriceTypeToDisplayPriceBreakdown(&crossSellSH, constants.SHORTSTAYS_PRICE_TYPE)
	}

	if len(plcHolders) != 0 {
		crossSellSH.Placeholders = plcHolders
	}

	// Adding LUXE_CATEGORY_HOTEL_URL on the basis of HOTEL_CATEGORY
	for i := 0; i < len(crossSellSH.HotelList); i++ {
		if strings.EqualFold(crossSellSH.HotelList[i].MmtHotelCategory, constants.LUXE_CATEGORY_VALUE) {
			crossSellSH.HotelList[i].MmtHotelCategoryUrl = constants.LUXE_CATEGORY_HOTEL_URL
		}
	}
	//2. Map search hotels response for cross sell response
	card.CardData = crossSellSH
	card.CardID = rsc.CardID
	card.CardData = crossSellSH
	card.CardID = rsc.CardID
	card.TemplateID = rsc.TemplateID
	card.ComponentID = rsc.ComponentID
	cards = append(cards, card)
	crossSellResp.Cards = cards

	logger.Infof("Total time taken to process search hotel response for correlation key %s is %v", correlationKey, time.Since(startTime))

	/* resp, _ := http.Get("http://localhost:8090/stream?i=" + strconv.Itoa(i))
	body, _ := ioutil.ReadAll(resp.Body) */

	//_ = json.Unmarshal(body, &sample)
	/* logger.Infof("Sending some data: %d", i)
	m := "Message : " + strconv.Itoa(i) */
	//fmt.Println("Message is:", sample)
	/* resp := response.HotelCrossSellStreamResponse{
		Message: m,
	} */
	//3. PDT Logging
	go logIntoPdt(rsc, correlationKey, dres, crossSellSH, shreq, dsTime)

	//4. Write the response to channel
	//logger.Debugf("writing to channel %v", crossSellResp)

	ch <- crossSellResp
}

func addPriceTypeToDisplayPriceBreakdown(crossSellSH *clientgateway.CrossSellSearchHotelsData, priceType string) {
	if len(crossSellSH.HotelList) != 0 {
		for i := 0; i < len(crossSellSH.HotelList); i++ {
			if !reflect.DeepEqual(crossSellSH.HotelList[i].DisplayFare, clientbackend.DisplayFare{}) && !reflect.DeepEqual(crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown, clientbackend.DisplayPriceBreakDown{}) {
				crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown.PriceType = priceType
			}
		}
	}
}

func updateOfferPersuasions(offerRequired bool, crossSellSH *clientgateway.CrossSellSearchHotelsData) {
	var couponMap map[string]int

	if offerRequired && len(crossSellSH.HotelList) != 0 {
		if couponMap == nil {
			couponMap = make(map[string]int)
		}
		var key string
		for i := 0; i < len(crossSellSH.HotelList); i++ {
			if !reflect.DeepEqual(crossSellSH.HotelList[i].DisplayFare, clientbackend.DisplayFare{}) && !reflect.DeepEqual(crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown, clientbackend.DisplayPriceBreakDown{}) && !reflect.DeepEqual(crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown.CouponInfo, clientbackend.CouponInfo{}) {
				cc := crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown.CouponInfo.CouponCode
				if val, ok := couponMap[cc]; ok {
					couponMap[crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown.CouponInfo.CouponCode] = val + 1
					if val+1 > len(crossSellSH.HotelList)/2 {
						key = crossSellSH.HotelList[i].DisplayFare.DisplayPriceBreakDown.CouponInfo.CouponCode
					}
				} else if len(cc) > 0 {
					couponMap[cc] = 1
				}
			}
		}

		if len(key) > 0 && config.PmsCommonConfig.CouponCode == key {
			//set the offer text
			offer := config.PmsCommonConfig.OfferText
			if len(offer) > 0 {
				r := strings.NewReplacer("<COUPON_CODE>", key)
				offer = r.Replace(offer)
				offerPersuasion := clientgateway.OfferPersuasion{}
				offerPersuasion.Text = offer
				offerPersuasions := []clientgateway.OfferPersuasion{}
				offerPersuasions = append(offerPersuasions, offerPersuasion)
				crossSellSH.OfferPersuasions = offerPersuasions
			}
		}
	}
}

func GetCrossSellV2StreamResponseWithRuleEngineSupport(rsc *request.SearchEvent, ch chan clientgateway.CrossSellV2StreamResponse, c *gin.Context, pageContext string, expData string, secureURL string, imageCount int, neabBy bool, user *request.User, isCorpRequest bool, offerRequired bool, ck string, crossSellRequest request.CrossSellRequest, reqCardId string) {
	funnelSource := rsc.SearchContext.FunnelSource
	region := c.GetHeader("region")
	start := time.Now()
	if len(ck) == 0 {
		ck = guuid.New().String()
	}
	logger.Debugf("getting search hotels data for cross sell stream API, correlationKey %s", ck)
	req := clientbackend.SearchHotelsRequest{}
	createRequestForCrossSellV2(rsc, &req, pageContext, expData, ck, c, secureURL, imageCount, neabBy, user, crossSellRequest)
	req.CardId = helpers.FetchCardIdBasisOfHydraSegments(rsc.HydraSegments, pageContext, rsc.CardID)
	// Get search hotels from CG
	var crossSellResp clientgateway.CrossSellV2StreamResponse
	var cards []clientgateway.CardV2
	card := clientgateway.CardV2{}
	var ctaLists []clientgateway.CtaList
	cta := clientgateway.CtaList{}
	var resp clientgateway.CrossSellCardData
	var bgLinearGradient clientgateway.BgLinearGradient
	var iconTags clientgateway.IconTags
	req.FunnelSource = constants.HOTEL_FUNNEL
	if strings.EqualFold(funnelSource, constants.HOMESTAY_FUNNEL) && !strings.EqualFold(region, "AE") {
		// Case of Homestay Funnel
		req.FunnelSource = constants.HOMESTAY_TEXT
	}
	if len(req.CardId) > 0 {
		// Hitting only when we got any matchedHydraSegment in which we configured any CrossSellBenefitCard
		resp = restconnectors.ClientStruct.PostCrossSellCardsData(&req, c)
	} else {
		logger.Debugf("cardId is Empty as no hydraSegments matches")
	}

	// Write the response to channel
	card.CardID = rsc.CardID
	card.TemplateID = rsc.TemplateID
	card.ComponentID = rsc.ComponentID
	cardDataV2 := clientgateway.CardDataV2{}
	// Please handle carefully benefitCardData and cardResponse because these are the pointers and can be give null pointer
	benefitCardData := new(clientgateway.BenefitCardData)
	cardResponse := new(clientgateway.CardResponse)
	if card.CardID == constants.HOTEL_XSELL_BENEFITS {
		benefitCardData = buildBenefitCardData(benefitCardData, resp, pageContext)
		if benefitCardData != nil && benefitCardData.BenefitsData != nil && len(benefitCardData.BenefitsData) > 0 {
			cardDataV2.BenefitCardData = benefitCardData
			cardDataV2.CtaList = buildCtaList(cta, constants.CTA_TEXT, resp, ctaLists, cardDataV2)
			cardDataV2.BgLinearGradient = buildBgLinearGradient(bgLinearGradient, resp)
		}
	} else if card.CardID == constants.HOTEL_EXTEND_YOUR_TRIP {
		cardResponse = buildCardResponse(cardResponse, resp)
		if cardResponse != nil && cardResponse.Items != nil && len(cardResponse.Items) > 0 {
			cardDataV2.CardResponse = cardResponse
			cardDataV2.CtaList = buildCtaList(cta, constants.HOTEL_EXTEND_YOUR_TRIP_CTA_TEXT, resp, ctaLists, cardDataV2)
		}
	} else if card.CardID == constants.INTL_CASHBACK_CARD {
		benefitCardData = buildBenefitCardData(benefitCardData, resp, pageContext)
		if benefitCardData != nil && benefitCardData.BenefitsData != nil && len(benefitCardData.BenefitsData) > 0 {
			cardDataV2.BenefitCardData = benefitCardData
			cardDataV2.CtaList = resp.Data.CtaList
			cardDataV2.BgLinearGradient = buildBgLinearGradient(bgLinearGradient, resp)
			cardDataV2.IconTags = buildIconTags(iconTags, resp)
		}
	}

	// Attached cardDataV2 response
	if (benefitCardData != nil && benefitCardData.BenefitsData != nil && len(benefitCardData.BenefitsData) > 0) || (cardResponse != nil && cardResponse.Items != nil && len(cardResponse.Items) > 0) {
		cardDataV2.CardType = resp.Data.CardType
		card.CardDataV2 = cardDataV2
		cards = append(cards, card)
	}

	crossSellResp.CardsV2 = cards
	ch <- crossSellResp
	logger.Debugf("Total time taken to process search hotel response for hotels and homestay WithRuleEngineSupport for correlation key %s is %v", ck, time.Since(start))
}

func buildBenefitCardData(benefitCardData *clientgateway.BenefitCardData, resp clientgateway.CrossSellCardData, pageContext string) *clientgateway.BenefitCardData {
	if benefitCardData == nil {
		return nil
	}
	// Build BenefitCardData
	benefitCardData.BenefitsData = resp.Data.BenefitsData
	benefitCardData.CouponInfo = resp.Data.CouponInfo
	benefitCardData.Header = resp.Data.TitleText
	benefitCardData.Text = resp.Data.SubText
	benefitCardData.Icon = resp.Data.IconUrl
	benefitCardData.TagUrl = resp.Data.TagUrl
	if config.TY_PAGE_CONTEXT == pageContext && resp.Data.TimerData != nil {
		benefitCardData.TimerData = resp.Data.TimerData
	}
	benefitCardData.WebViewUrl = resp.Data.WebViewUrl
	return benefitCardData
}

func buildCardResponse(cardResponse *clientgateway.CardResponse, resp clientgateway.CrossSellCardData) *clientgateway.CardResponse {
	if cardResponse == nil {
		return nil
	}
	// Build CardResponse for Extend Your Trip
	cardResponse.Items = resp.Data.Items
	cardResponse.Header = resp.Data.TitleText
	cardResponse.Text = resp.Data.SubText
	cardResponse.Icon = resp.Data.IconUrl
	cardResponse.TimerData = resp.Data.TimerData
	return cardResponse
}

func buildBgLinearGradient(bgLinearGradient clientgateway.BgLinearGradient, resp clientgateway.CrossSellCardData) clientgateway.BgLinearGradient {
	// Build BgLinearGradient
	bgLinearGradient.Start = resp.Data.BgLinearGradient.Start
	bgLinearGradient.End = resp.Data.BgLinearGradient.End
	bgLinearGradient.Center = resp.Data.BgLinearGradient.Center
	bgLinearGradient.Direction = resp.Data.BgLinearGradient.Direction
	return bgLinearGradient
}

func buildCtaList(cta clientgateway.CtaList, ctaText string, resp clientgateway.CrossSellCardData, ctaLists []clientgateway.CtaList, cardDataV2 clientgateway.CardDataV2) []clientgateway.CtaList {
	// Building ctaList Data
	cta.DeepLink = resp.DeepLink
	cta.Text = ctaText
	cta.Position = constants.CTA_POSITION
	ctaLists = append(ctaLists, cta)
	return ctaLists
}

func GetCrossSellV2StreamResponse(rsc *request.SearchEvent, ch chan clientgateway.CrossSellV2StreamResponse, c *gin.Context, pageContext string, expData string,
	secureURL string, imageCount int, neabBy bool, user *request.User, isCorpRequest bool, offerRequired bool, correlationKey string, crossSellRequest request.CrossSellRequest, reqCardId string) {
	var tabsDataForHotels clientgateway.CrossSellSearchHotelsData
	var tabsDataForHomestay clientgateway.CrossSellSearchHotelsData
	var selectedTabData clientgateway.CrossSellSearchHotelsData
	// selectedTab is 0 when hotels selected and is when homestay tab is selected
	var selectedTab = 0
	var crossSellResp clientgateway.CrossSellV2StreamResponse
	// if correlationKey is not received from commons, create one
	if len(correlationKey) == 0 {
		correlationKey = guuid.New().String()
	}
	// Get search hotels from CG
	cards := []clientgateway.CardV2{}
	card := clientgateway.CardV2{}
	startTime := time.Now()
	tabsDataForHotels, tabsDataForHomestay, shreq, dsTime := getHotelSearchDataForCrossSellV2(rsc, c, pageContext, expData, correlationKey, secureURL,
		imageCount, neabBy, user, isCorpRequest, crossSellRequest)
	if len(tabsDataForHotels.ResponseErrors.ErrorList) > 0 || len(tabsDataForHomestay.ResponseErrors.ErrorList) > 0 {
		if len(tabsDataForHotels.CorrelationKey) > 0 {
			for _, err := range tabsDataForHotels.ResponseErrors.ErrorList {
				logger.Errorf("Error in SearchHotel for hotels : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, correlationKey)
			}
		}
		if len(tabsDataForHomestay.CorrelationKey) > 0 {
			for _, err := range tabsDataForHomestay.ResponseErrors.ErrorList {
				logger.Errorf("Error in SearchHotel for homestay : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, correlationKey)
			}
		}
		card.CardDataV2.ResponseErrors = buildErrorResponse(tabsDataForHotels, tabsDataForHomestay)
	}
	if len(tabsDataForHotels.HotelList) > 0 || len(tabsDataForHomestay.HotelList) > 0 {

		tabsDataForHotels := modifyAddress(tabsDataForHotels, reqCardId)
		tabsDataForHomestay := modifyAddress(tabsDataForHomestay, reqCardId)

		if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.HOMESTAY_FUNNEL) && len(tabsDataForHomestay.HotelList) > 0 {
			selectedTab = 1
		} else if len(tabsDataForHotels.HotelList) > 0 && len(tabsDataForHomestay.HotelList) > 0 {
			// cross sell use case
			if rsc.Tags.Source != constants.CROSS_SELL_CONDITION {
				selectedTab = 0
			} else if rsc.FunnelActivity[constants.HOTEL_FUNNEL].FunnelCounts.Pageviews.Detail > 0 { // detail page drop off
				// with Alt acco intent
				if rsc.FunnelActivity[constants.HOTEL_FUNNEL].FunnelCounts.ProductTypeCounts.IsAltaccoTrue >= constants.MINIMUM_INTENT_FOR_ALT_ACCO_COUNT {
					selectedTab = 1
				} else { // without Alt Acco intent
					selectedTab = 0
				}
			} else if rsc.FunnelActivity[constants.HOTEL_FUNNEL].FunnelCounts.Pageviews.Listing > 0 { // listing page drop off
				selectedTab = 0
			}
		}
		if selectedTab == 0 {
			selectedTabData = tabsDataForHotels
		} else {
			selectedTabData = tabsDataForHomestay
		}

		var heading string
		var subheading string

		if neabBy && len(selectedTabData.HotelList) != 0 {
			hotelElement := selectedTabData.HotelList[0]
			selectedTabData.FromCity = hotelElement.FromCity
		}
		region := c.GetHeader("region")
		if len(region) != 0 && strings.EqualFold(region, "AE") {
			heading = constants.HEADER_HOTELS_GCC_HEADING
			subheading = helpers.GetSubheading(rsc.CardID)
			r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
			subheading = r.Replace(subheading)
		} else if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.HOMESTAY_FUNNEL) {
			heading = constants.HEADER_HOMESTAY_HEADING
			if helpers.IsMmtCrossSellCardExists(rsc, c) {
				// It means that the card was CROSS_SELL offer Card
				subheading = constants.HEADER_SUBHEADING
			} else if strings.EqualFold(rsc.CardID, constants.MMT_CROSS_SELL_CARD) {
				// It means that the card was DROP_OFF Card
				subheading = constants.MMT_CROSS_SELL_DROPOFF_SUBHEADING
			} else {
				// By Default case will be continue
				subheading = constants.HEADER_HOMESTAY_SUBHEADING
			}
			r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
			subheading = r.Replace(subheading)

		} else if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.HOTEL_FUNNEL) {
			heading = constants.HEADER_HOTELS_HEADING
			if helpers.IsMmtCrossSellCardExists(rsc, c) {
				// It means that the card was CROSS_SELL offer Card
				subheading = constants.HEADER_SUBHEADING
			} else if strings.EqualFold(rsc.CardID, constants.MMT_CROSS_SELL_CARD) {
				// It means that the card was DROP_OFF Card
				subheading = constants.MMT_CROSS_SELL_DROPOFF_SUBHEADING
			} else {
				// By Default case will be continue
				subheading = constants.HEADER_HOTELS_SUBHEADING
				if strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD) || strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2) {
					heading = constants.IMMERSIVE_THEMES_CARD_HEADING_INTENT
					subheading = constants.IMMERSIVE_THEMES_CARD_SUBHEADING_INTENT
					r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
					heading = r.Replace(heading)
				}
			}
			r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
			subheading = r.Replace(subheading)
		} else {
			heading = constants.HEADER_HOTELS_HEADING
			if helpers.IsMmtCrossSellCardExists(rsc, c) {
				// It means that the card was CROSS_SELL offer Card
				subheading = constants.HEADER_SUBHEADING
			} else if strings.EqualFold(rsc.CardID, constants.MMT_CROSS_SELL_CARD) {
				// It means that the card was DROP_OFF Card
				subheading = constants.MMT_CROSS_SELL_DROPOFF_SUBHEADING
			} else {
				// By Default case will be continue
				subheading = constants.HEADER_HOTELS_SUBHEADING
			}
			if strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD) || strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2) {
				heading = constants.IMMERSIVE_THEMES_CARD_HEADING_INTENT
				subheading = constants.IMMERSIVE_THEMES_CARD_SUBHEADING_INTENT
				r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
				heading = r.Replace(heading)
			}
			r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
			subheading = r.Replace(subheading)
		}

		header := clientgateway.Header{
			Header:    heading,
			Subheader: subheading,
		}

		tabsDataForHotels.DeeplinkURL = tabsDataForHotels.ListingDeepLink
		tabsDataForHomestay.DeeplinkURL = tabsDataForHomestay.ListingDeepLink
		tabsDataForHotels.ListingDeepLink = ""
		tabsDataForHomestay.ListingDeepLink = ""
		filterList := []clientgateway.Filter{}
		// create filterMap such that if DATE_FILTER in request and nearby is true , only DATE_FILTER in response
		if neabBy && rsc.Filter && len(rsc.FilterList) != 0 {
			for _, filter := range rsc.FilterList {
				if filter.FilterType != constants.DATE_FILTER_TEXT {
					continue
				}
				buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, selectedTabData.HotelList)
				filterList = append(filterList, filter)
			}
			selectedTabData.FilterList = filterList
			selectedTabData.FilterMap = nil
		} else if neabBy {
			// if nearby is true but no date_filter in request , we send distance_filter in response
			if len(selectedTabData.FilterMap) != 0 {
				values := []clientgateway.Filter{}
				ok := false
				values, ok = selectedTabData.FilterMap[constants.DRIVING_DISTANCE_TEXT]
				if ok {
					for _, filter := range values {
						// buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, crossSellSH.HotelList)
						filter.FilterName = filter.Title
						filter.Title = ""
						// appending filter in response only if hotel ids present for that case
						if len(filter.HotelIDs) != 0 {
							buildDeeplinkUrl(&filter, selectedTabData.DeeplinkURL)
							filterList = append(filterList, filter)
						}
					}
				}
			}
			selectedTabData.FilterList = filterList
			selectedTabData.FilterMap = nil
		} else {
			// create filter list from filter map if nearby false
			filterListHotels := []clientgateway.Filter{}
			if len(tabsDataForHotels.FilterMap) != 0 {
				for key, filters := range tabsDataForHotels.FilterMap {
					createFilterMap(filters, key, filterListHotels)
				}
			}
			tabsDataForHotels.FilterList = filterListHotels
			tabsDataForHotels.FilterMap = nil

			filterListHomestay := []clientgateway.Filter{}
			if len(tabsDataForHomestay.FilterMap) != 0 {
				for key, filters := range tabsDataForHomestay.FilterMap {
					createFilterMap(filters, key, filterListHomestay)
				}
			}
			tabsDataForHomestay.FilterList = filterListHomestay
			tabsDataForHomestay.FilterMap = nil
		}

		card.HeaderData = header

		if config.AppConfig.CardsForPersuasion != nil && helpers.Contains(config.AppConfig.CardsForPersuasion, rsc.CardID) {
			exclusiveBookerHydraSegmentMap := config.AppConfig.CrosssellBookerHydraSegments
			var exclusiveType string
			for key, value := range exclusiveBookerHydraSegmentMap {
				if rsc.HydraSegments != nil && !disjoint(rsc.HydraSegments, value) {
					exclusiveType = strings.ToUpper(string(key[0])) + key[1:]
					break
				}
			}
			if exclusiveType != "" {
				card.CardDataV2.Persuasion.Text = strings.Replace(constants.EXCLUSIVE_PERSUASION_TEXT, "{Exclusive_type}", exclusiveType, -1)
				card.CardDataV2.Persuasion.BgColor = constants.EXCLUSIVE_PERSUASION_BGCOLOR
				card.CardDataV2.Persuasion.Icon = constants.EXCLUSIVE_PERSUASION_ICON
				card.CardDataV2.Persuasion.TxtColor = constants.EXCLUSIVE_PERSUASION_TEXTCOLOR
			}
		}

		// Map search hotels response for cross sell response
		var tabsData = make(map[string]clientgateway.CrossSellSearchHotelsData)
		if len(tabsDataForHotels.HotelList) > 0 {
			if crossSellRequest.Brand == "GI" {
				helpers.BuildGiDeeplinkForHotels(rsc, &tabsDataForHotels)
			}
			processPersuasionData(tabsDataForHotels)             // process persuasions
			plcHolders := processPlaceHolders(tabsDataForHotels) // process placeholders
			if len(plcHolders) != 0 {
				tabsDataForHotels.Placeholders = plcHolders
			}
			updateOfferPersuasions(offerRequired, &tabsDataForHotels) // update offer persuasions
		}
		if len(tabsDataForHomestay.HotelList) > 0 {
			plcHolders := processPlaceHolders(tabsDataForHomestay)
			if len(plcHolders) != 0 {
				tabsDataForHomestay.Placeholders = plcHolders
			}
		}
		//updateOfferPersuasions
		if len(tabsDataForHotels.HotelList) > 0 {
			updateOfferPersuasions(offerRequired, &tabsDataForHotels)
			if checkIfPremiumHomepage(c) {
				if len(rsc.SearchContext.Lob) == 0 {
					addPriceTypeToDisplayPriceBreakdown(&tabsDataForHotels, constants.HOTEL_XSELL_PRICE_TYPE)
				}
				if strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD) || strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2) {
					updatePropertyDescFromHotelUsp(&tabsDataForHotels)
				}
			}
		}
		if len(tabsDataForHomestay.HotelList) > 0 {
			updateOfferPersuasions(offerRequired, &tabsDataForHomestay)
		}
		//Card offer Details for GCC MMT exclusive cards
		if len(tabsDataForHotels.HotelList) > 0 {
			helpers.BuildMmtExclusiveCardResponse(rsc, &tabsDataForHotels, c)
			helpers.BuildCrossSellCardResponse(rsc, &tabsDataForHotels, c)

			if len(crossSellRequest.MetaInfo.UserName) > 0 {
				var name = crossSellRequest.MetaInfo.UserName + ", "
				tabsDataForHotels.CrossSellData.Title = strings.Replace(tabsDataForHotels.CrossSellData.Title, "{userName}", name, 1)
			} else {
				tabsDataForHotels.CrossSellData.Title = strings.Replace(tabsDataForHotels.CrossSellData.Title, "{userName}", "", 1)
			}
			tabsDataForHotels.CrossSellData.Coupon.Code = crossSellRequest.MetaInfo.BookingId
			tabsData[constants.HOTElS_TEXT] = tabsDataForHotels
			var tabDetailsForHotels clientgateway.TabDetail
			tabDetailsForHotels.Id = constants.HOTElS_TEXT
			tabDetailsForHotels.Title = constants.HOTELS_TITLE
			tabDetailsForHotels.IsHotel = true
			if selectedTab == 0 {
				tabDetailsForHotels.Selected = true
			}
			tabDetailsForHotels.Deeplink = tabsData[constants.HOTElS_TEXT].DeeplinkURL
			tabDetailsForHotels.Tg = tabsData[constants.HOTElS_TEXT].Tg
			tabDetailsForHotels.Gd = tabsData[constants.HOTElS_TEXT].Gd
			card.CardDataV2.TabDetails = append(card.CardDataV2.TabDetails, tabDetailsForHotels)
		}
		if len(tabsDataForHomestay.HotelList) > 0 {
			processPersuasionData(tabsDataForHomestay)             // process persuasions
			plcHolders := processPlaceHolders(tabsDataForHomestay) // process placeholders
			if len(plcHolders) != 0 {
				tabsDataForHomestay.Placeholders = plcHolders
			}
			updateOfferPersuasions(offerRequired, &tabsDataForHomestay)      // update offer persuasions
			helpers.BuildCrossSellCardResponse(rsc, &tabsDataForHomestay, c) // cross sell card response

			if len(crossSellRequest.MetaInfo.UserName) > 0 {
				var name = crossSellRequest.MetaInfo.UserName + ", "
				tabsDataForHomestay.CrossSellData.Title = strings.Replace(tabsDataForHomestay.CrossSellData.Title, "{userName}", name, 1)
			} else {
				tabsDataForHomestay.CrossSellData.Title = strings.Replace(tabsDataForHomestay.CrossSellData.Title, "{userName}", "", 1)
			}
			tabsDataForHomestay.CrossSellData.Coupon.Code = crossSellRequest.MetaInfo.BookingId
			tabsData[constants.HOMESTAY_TEXT] = tabsDataForHomestay
			var tabDetailsForHomestay clientgateway.TabDetail
			tabDetailsForHomestay.Id = constants.HOMESTAY_TEXT
			tabDetailsForHomestay.Title = constants.HOMESTAYS_TITLE
			if selectedTab == 1 {
				tabDetailsForHomestay.Selected = true
			}
			tabDetailsForHomestay.Deeplink = tabsData[constants.HOMESTAY_TEXT].DeeplinkURL
			tabDetailsForHomestay.Tg = tabsData[constants.HOMESTAY_TEXT].Tg
			tabDetailsForHomestay.Gd = tabsData[constants.HOMESTAY_TEXT].Gd
			card.CardDataV2.TabDetails = append(card.CardDataV2.TabDetails, tabDetailsForHomestay)
			buildPropertyDesc(tabsData)
		}
		card.CardDataV2.TabsData = tabsData

	}
	if tabsDataForHotels.FilterToHotelMap != nil && len(tabsDataForHotels.FilterToHotelMap) > 0 {
		var tabsData = make(map[string]clientgateway.CrossSellSearchHotelsData)
		for key, value := range tabsDataForHotels.FilterToHotelMap {
			crossSellData := clientgateway.CrossSellSearchHotelsData{
				HotelList: value.HotelList,
			}
			crossSellData = modifyAddress(crossSellData, reqCardId)
			processPersuasionData(crossSellData)
			plcHolders := processPlaceHolders(crossSellData)
			if len(plcHolders) != 0 {
				crossSellData.Placeholders = plcHolders
			}
			updateOfferPersuasions(offerRequired, &crossSellData)
			if checkIfPremiumHomepage(c) {
				addPriceTypeToDisplayPriceBreakdown(&crossSellData, constants.HOTEL_XSELL_PRICE_TYPE)
			}
			if strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD) || strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2) {
				updatePropertyDescFromHotelUsp(&crossSellData)
			}
			tabsData[key] = crossSellData
			card.CardDataV2.TabDetails = append(card.CardDataV2.TabDetails, value.TabDetails)
		}
		if len(rsc.SelectedTabId) != 0 {
			for i := range card.CardDataV2.TabDetails {
				if card.CardDataV2.TabDetails[i].Id == rsc.SelectedTabId {
					card.CardDataV2.TabDetails[i].Selected = true
				} else {
					card.CardDataV2.TabDetails[i].Selected = false
				}
			}
		}
		for i := range card.CardDataV2.TabDetails {
			if card.CardDataV2.TabDetails[i].Selected == true {
				card.CardDataV2.TabDetails[0], card.CardDataV2.TabDetails[i] = card.CardDataV2.TabDetails[i], card.CardDataV2.TabDetails[0]
				break
			}
		}
		card.HeaderData.Header = constants.IMMERSIVE_THEMES_CARD_HEADING
		card.HeaderData.Subheader = constants.IMMERSIVE_THEMES_CARD_SUBHEADING
		card.CardDataV2.TabsData = tabsData
	}
	card.CardDataV2.Intent = rsc.SearchContext.To.Locus.City
	// lobSubHeader not required for mmt exclusive cards
	if !strings.EqualFold(rsc.CardID, constants.MMT_EXCLUSIVE_OFFERS) && !(strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD) || strings.EqualFold(rsc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2)) {
		checkinDate := utils.GetTimeInFormattedString(utils.GetTimeFromEpochAndZone(rsc.SearchContext.FromDateStr.Zone, rsc.SearchContext.FromDateStr.TS))
		checkoutDate := utils.GetTimeInFormattedString(utils.GetTimeFromEpochAndZone(rsc.SearchContext.ToDateStr.Zone, rsc.SearchContext.ToDateStr.TS))
		checkin := utils.FormatDateString(checkinDate, constants.DT_FRMT_SEARCH_CNTXT, time.RFC1123)
		checkin = checkin[:12] + checkin[14:16]
		checkout := utils.FormatDateString(checkoutDate, constants.DT_FRMT_SEARCH_CNTXT, time.RFC1123)
		checkout = checkout[:12] + checkout[14:16]
		card.HeaderData.LobSubheader = checkin + " - " + checkout
	}
	card.CardID = rsc.CardID
	card.TemplateID = rsc.TemplateID
	card.ComponentID = rsc.ComponentID
	cards = append(cards, card)
	crossSellResp.CardsV2 = cards
	logger.Infof("Total time taken to process search hotel response for hotels and homestay for correlation key %s is %v", correlationKey, time.Since(startTime))
	// PDT Logging for hotels
	go logIntoPdt(rsc, correlationKey, nil, tabsDataForHotels, shreq, dsTime)
	// PDT Logging for homestays
	go logIntoPdt(rsc, correlationKey, nil, tabsDataForHomestay, shreq, dsTime)
	// Write the response to channel
	ch <- crossSellResp
}

func disjoint(slice1, slice2 []string) bool {
	set := make(map[string]struct{}, len(slice1))
	for _, v := range slice1 {
		set[v] = struct{}{}
	}
	for _, v := range slice2 {
		if _, ok := set[v]; ok {
			return false
		}
	}
	return true
}

func updatePropertyDescFromHotelUsp(hotels *clientgateway.CrossSellSearchHotelsData) {
	if len(hotels.HotelList) != 0 {
		for i := 0; i < len(hotels.HotelList); i++ {
			if len(hotels.HotelList[i].Persuasions) != 0 && strings.EqualFold(hotels.HotelList[i].Persuasions[0].ID, "USP_PERSUASION") {
				hotels.HotelList[i].PropertyDesc = hotels.HotelList[i].Persuasions[0].Desc
			}
		}
	}
}

func checkIfPremiumHomepage(c *gin.Context) bool {
	var storeId string
	storeId = c.GetHeader("storeId")
	var premiumHomepage bool
	premiumHomepage = strings.EqualFold(strings.ToLower(storeId), "luxe")
	return premiumHomepage
}

func GetLandingDiscoveryV2StreamResponse(rsc *request.SearchEvent, ch chan clientgateway.CardV2, c *gin.Context, pageContext string, expData string, secureURL string, imageCount int, neabBy bool, user *request.User, isCorpRequest bool, offerRequired bool, correlationKey string, crossSellRequest request.CrossSellRequest, reqCardId string) {

	var tabsDataForHotels clientgateway.CrossSellSearchHotelsData
	var tabsDataForHomestay clientgateway.CrossSellSearchHotelsData
	var selectedTabData clientgateway.CrossSellSearchHotelsData
	// selectedTab is 0 when hotels selected and is when homestay tab is selected
	var selectedTab = 0

	if len(correlationKey) == 0 {
		correlationKey = guuid.New().String()
	}
	// Get search hotels from CG
	card := clientgateway.CardV2{}

	/*HTL-37214 client (ios/android) is not passing zone to empeiria -> scion in case of hotel landing discovery card
	* hardcoded Asia/Calcutta after discussion with product. client should send data in this node only no dev required at ORCH end
	 */
	if rsc.SearchContext.FromDateStr.Zone == "" {
		rsc.SearchContext.FromDateStr.Zone = "Asia/Calcutta"
	}
	if rsc.SearchContext.ToDateStr.Zone == "" {
		rsc.SearchContext.ToDateStr.Zone = "Asia/Calcutta"
	}
	if checkIfPremiumHomepage(c) && len(rsc.SearchContext.FunnelSource) == 0 {
		rsc.SearchContext.FunnelSource = constants.HOTEL_FUNNEL
	}
	tabsDataForHotels = getHotelSearchDataForLandingDiscoveryV2(rsc, c, pageContext, expData, correlationKey, secureURL,
		imageCount, neabBy, user, isCorpRequest, crossSellRequest)
	if len(tabsDataForHotels.ResponseErrors.ErrorList) > 0 {
		if len(tabsDataForHotels.CorrelationKey) > 0 {
			for _, err := range tabsDataForHotels.ResponseErrors.ErrorList {
				logger.Errorf("Error in LandingDiscovery for hotels : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, correlationKey)
			}
		}
		//if len(tabsDataForHomestay.CorrelationKey) > 0 {
		//	for _, err := range tabsDataForHomestay.ResponseErrors.ErrorList {
		//		logger.Errorf("Error in LandingDiscovery for homestay : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, correlationKey)
		//	}
		//}
		card.CardDataV2.ResponseErrors = buildErrorResponse(tabsDataForHotels, tabsDataForHomestay)
	}
	if len(tabsDataForHotels.HotelList) > 0 || len(tabsDataForHomestay.HotelList) > 0 {
		tabsDataForHotels := modifyAddress(tabsDataForHotels, reqCardId)
		tabsDataForHomestay := modifyAddress(tabsDataForHomestay, reqCardId)
		if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.HOTEL_FUNNEL) {
			selectedTabData = tabsDataForHotels
		} else {
			selectedTabData = tabsDataForHomestay
		}
		// Map search hotels response for cross sell response
		var heading string
		var subheading string
		var lobSubheading string

		if neabBy && len(selectedTabData.HotelList) != 0 {
			hotelElement := selectedTabData.HotelList[0]
			selectedTabData.FromCity = hotelElement.FromCity
		}
		region := c.GetHeader("region")
		if len(region) != 0 && strings.EqualFold(region, "AE") {
			heading = constants.HEADER_HOTELS_GCC_HEADING
			subheading = constants.HEADER_HOTELS_SUBHEADING
			r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
			subheading = r.Replace(subheading)
		} else if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.HOMESTAY_FUNNEL) {
			subheading, lobSubheading = getLandingCardHeader(rsc, selectedTabData)
		} else if strings.EqualFold(rsc.SearchContext.FunnelSource, constants.HOTEL_FUNNEL) {
			subheading, lobSubheading = getLandingCardHeader(rsc, selectedTabData)
		} else {
			heading = constants.HEADER_HOTELS_HEADING
			subheading = constants.HEADER_HOTELS_SUBHEADING
			r := strings.NewReplacer("<NOP>", selectedTabData.CityName)
			subheading = r.Replace(subheading)
		}
		if checkIfPremiumHomepage(c) {
			if len(rsc.SearchContext.Lob) == 0 {
				addPriceTypeToDisplayPriceBreakdown(&tabsDataForHotels, constants.HOTEL_XSELL_PRICE_TYPE)
			}
			if strings.EqualFold(rsc.CardID, constants.GREAT_VALUE_PACKAGES_V2) {
				if len(rsc.SearchContext.Lob) == 0 {
					heading = constants.HEADER_GREAT_VALUE_HEADING_V2
					subheading = constants.HEADER_GREAT_VALUE_SUBHEADING_V2
				} else {
					heading = constants.HEADER_GREAT_VALUE_HEADING_V2_INTENT
					subheading = constants.HEADER_GREAT_VALUE_SUBHEADING_V2_INTENT
					cityName := selectedTabData.CityName
					if len(rsc.SearchContext.To.CityName) != 0 {
						cityName = rsc.SearchContext.To.CityName
					}
					r := strings.NewReplacer("<NOP>", cityName)
					heading = r.Replace(heading)
				}
			}
		}

		header := clientgateway.Header{
			Header:    heading,
			Subheader: subheading,
		}

		tabsDataForHotels.DeeplinkURL = tabsDataForHotels.ListingDeepLink
		//tabsDataForHomestay.DeeplinkURL = tabsDataForHomestay.ListingDeepLink
		tabsDataForHotels.ListingDeepLink = ""
		//tabsDataForHomestay.ListingDeepLink = ""
		filterList := []clientgateway.Filter{}
		// create filterMap such that if DATE_FILTER in request and nearby is true , only DATE_FILTER in response
		if neabBy && rsc.Filter && len(rsc.FilterList) != 0 {
			for _, filter := range rsc.FilterList {
				if filter.FilterType != constants.DATE_FILTER_TEXT {
					continue
				}
				buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, selectedTabData.HotelList)
				filterList = append(filterList, filter)
			}
			selectedTabData.FilterList = filterList
			selectedTabData.FilterMap = nil
		} else if neabBy {
			// if nearby is true but no date_filter in request , we send distance_filter in response
			if len(selectedTabData.FilterMap) != 0 {
				values := []clientgateway.Filter{}
				ok := false
				values, ok = selectedTabData.FilterMap[constants.DRIVING_DISTANCE_TEXT]
				if ok {
					for _, filter := range values {
						// buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, crossSellSH.HotelList)
						filter.FilterName = filter.Title
						filter.Title = ""
						// appending filter in response only if hotel ids present for that case
						if len(filter.HotelIDs) != 0 {
							buildDeeplinkUrl(&filter, selectedTabData.DeeplinkURL)
							filterList = append(filterList, filter)
						}
					}
				}
			}
			selectedTabData.FilterList = filterList
			selectedTabData.FilterMap = nil
		} else {
			// create filter list from filter map if nearby false
			filterListHotels := []clientgateway.Filter{}
			if len(tabsDataForHotels.FilterMap) != 0 {
				for key, filters := range tabsDataForHotels.FilterMap {
					createFilterMap(filters, key, filterListHotels)
				}
			}
			tabsDataForHotels.FilterList = filterListHotels
			tabsDataForHotels.FilterMap = nil

			//filterListHomestay := []clientgateway.Filter{}
			//if len(tabsDataForHomestay.FilterMap) != 0 {
			//	for key, filters := range tabsDataForHomestay.FilterMap {
			//		createFilterMap(filters, key, filterListHomestay)
			//	}
			//}
			//tabsDataForHomestay.FilterList = filterListHomestay
			//tabsDataForHomestay.FilterMap = nil
		}
		card.HeaderData = header

		// process persuasions
		if len(tabsDataForHotels.HotelList) > 0 {
			processPersuasionData(tabsDataForHotels)
		}
		//if len(tabsDataForHomestay.HotelList) > 0 {
		//	processPersuasionData(tabsDataForHomestay)
		//}
		// process placeholders
		if len(tabsDataForHotels.HotelList) > 0 {
			plcHolders := processPlaceHolders(tabsDataForHotels)
			if len(plcHolders) != 0 {
				tabsDataForHotels.Placeholders = plcHolders
			}
		}
		//if len(tabsDataForHomestay.HotelList) > 0 {
		//	plcHolders := processPlaceHolders(tabsDataForHomestay)
		//	if len(plcHolders) != 0 {
		//		tabsDataForHomestay.Placeholders = plcHolders
		//	}

		//}
		//updateOfferPersuasions
		if len(tabsDataForHotels.HotelList) > 0 {
			updateOfferPersuasions(offerRequired, &tabsDataForHotels)
		}
		//if len(tabsDataForHomestay.HotelList) > 0 {
		//	updateOfferPersuasions(offerRequired, &tabsDataForHomestay)
		//}
		// Map search hotels response for cross sell response
		var tabsData = make(map[string]clientgateway.CrossSellSearchHotelsData)
		if len(tabsDataForHotels.HotelList) > 0 {
			tabsData[constants.HOTElS_TEXT] = tabsDataForHotels
			var tabDetailsForHotels clientgateway.TabDetail
			tabDetailsForHotels.Id = constants.HOTElS_TEXT
			tabDetailsForHotels.Title = constants.HOTELS_TITLE
			tabDetailsForHotels.IsHotel = true
			if selectedTab == 0 {
				tabDetailsForHotels.Selected = true
			}
			tabDetailsForHotels.Deeplink = tabsData[constants.HOTElS_TEXT].DeeplinkURL
			card.CardDataV2.TabDetails = append(card.CardDataV2.TabDetails, tabDetailsForHotels)
		}
		//Commented code for now as homestay is yet to introduce
		//if len(tabsDataForHomestay.HotelList) > 0 {
		//	tabsData[constants.HOMESTAY_TEXT] = tabsDataForHomestay
		//	var tabDetailsForHomestay clientgateway.TabDetail
		//	tabDetailsForHomestay.Id = constants.HOMESTAY_TEXT
		//	tabDetailsForHomestay.Title = constants.HOMESTAYS_TITLE
		//	if selectedTab == 1 {
		//		tabDetailsForHomestay.Selected = true
		//	}
		//	tabDetailsForHomestay.Deeplink = tabsData[constants.HOMESTAY_TEXT].DeeplinkURL
		//	card.CardDataV2.TabDetails = append(card.CardDataV2.TabDetails, tabDetailsForHomestay)
		//	buildPropertyDesc(tabsData)
		//}
		card.CardDataV2.TabsData = tabsData
		card.HeaderData.LobSubheader = lobSubheading
	}
	card.CardDataV2.Intent = rsc.SearchContext.To.Locus.City
	checkinDate := utils.GetTimeInFormattedString(utils.GetTimeFromEpochAndZone(rsc.SearchContext.FromDateStr.Zone, rsc.SearchContext.FromDateStr.TS))
	checkoutDate := utils.GetTimeInFormattedString(utils.GetTimeFromEpochAndZone(rsc.SearchContext.ToDateStr.Zone, rsc.SearchContext.ToDateStr.TS))
	checkin := utils.FormatDateString(checkinDate, constants.DT_FRMT_SEARCH_CNTXT, time.RFC1123)
	checkin = checkin[:12] + checkin[14:16]
	checkout := utils.FormatDateString(checkoutDate, constants.DT_FRMT_SEARCH_CNTXT, time.RFC1123)
	checkout = checkout[:12] + checkout[14:16]
	if strings.EqualFold(rsc.CardID, constants.GREAT_VALUE_PACKAGES_V2) && len(rsc.SearchContext.Lob) > 0 {
		card.HeaderData.LobSubheader = checkin + " - " + checkout
	}
	card.CardID = rsc.CardID
	card.TemplateID = rsc.TemplateID
	card.ComponentID = rsc.ComponentID
	// Write the response to channel [should run irrespective when clientgateway.CardV2 is popped out of stack]
	ch <- card
}

func getLandingCardHeader(rsc *request.SearchEvent, data clientgateway.CrossSellSearchHotelsData) (string, string) {
	//var heading string
	var subheading string
	var lobSubheader string

	if strings.EqualFold(rsc.CardID, constants.GREAT_VALUE_PACKAGES) {
		subheading = constants.HEADER_GREAT_VALUE_HEADING
		lobSubheader = constants.HEADER_GREAT_VALUE_SUBHEADING
		r := strings.NewReplacer("<NOP>", data.CityName)
		lobSubheader = r.Replace(lobSubheader)
	} else if strings.EqualFold(rsc.CardID, constants.HANDPICKED_PROPERITES) {
		subheading = constants.HEADER_HANDPICKED_HEADING
		lobSubheader = constants.HEADER_HANDPICKED_SUBHEADING
		r := strings.NewReplacer("<NOP>", data.CityName)
		lobSubheader = r.Replace(lobSubheader)
	} else if strings.EqualFold(rsc.CardID, constants.RECENTLY_VIEWED_HOTELS) {
		subheading = constants.HEADER_RECENTLY_VIEWED_HEADING
	}
	return subheading, lobSubheader
}

func buildPropertyDesc(tabsData map[string]clientgateway.CrossSellSearchHotelsData) {
	tabsDataForHomestay := tabsData[constants.HOMESTAY_TEXT]
	amenities := map[string]bool{
		"Lounge":          true,
		"Fireplace":       true,
		"Restaurant":      true,
		"Cafe":            true,
		"Coffee Shop":     true,
		"Barbeque":        true,
		"Laundry Service": true,
		"Jacuzzi":         true,
		"Tours and Treks": true,
		"Living Room":     true,
		"Swimming Pool":   true,
		"Housekeeping":    true,
		"Caretaker":       true,
		"Kitchenette":     true,
		"Balcony/Terrace": true,
		"Verandah":        true,
	}
	for i := 0; i < len(tabsDataForHomestay.HotelList); i++ {
		homestay := &tabsDataForHomestay.HotelList[i]
		var _ = ""
		propertyDesc, bedRoomCount, bedCount, amenity := "", "", "", ""
		altAccoRoomInfo := homestay.AltAccoRoomInfo
		if altAccoRoomInfo != nil {
			for key, value := range altAccoRoomInfo {
				_ = key
				if strings.EqualFold(value.ParentRoomCode, "") {
					if len(value.BedRoomCount) > 0 {
						bedRoomCount = value.BedRoomCount
					}
					if value.BedCount > 0 {
						bedCount = strconv.Itoa(value.BedCount)
					}
				}
			}
			for j := 0; j < len(homestay.FacilityHighlights); j++ {
				facility := homestay.FacilityHighlights[j]
				if amenities[facility] == true {
					amenity = facility
				}
			}
		}
		if len(bedCount) > 0 {
			propertyDesc += bedCount + " Bed"

		}
		if len(bedRoomCount) > 0 {
			if len(propertyDesc) > 0 {
				propertyDesc += " | "
			}
			propertyDesc += bedRoomCount + " Bedroom"
		}
		if len(amenity) > 0 {
			if len(propertyDesc) > 0 {
				propertyDesc += " | "
			}
			propertyDesc += amenity
		}

		homestay.PropertyDesc = propertyDesc
	}
}

func buildErrorResponse(hotels clientgateway.CrossSellSearchHotelsData, homestay clientgateway.CrossSellSearchHotelsData) clientbackend.ResponseErrors {
	var responseErrors clientbackend.ResponseErrors
	if len(hotels.CorrelationKey) > 0 && len(hotels.ResponseErrors.ErrorList) > 0 {
		responseErrors.ErrorList = append(responseErrors.ErrorList, hotels.ResponseErrors.ErrorList...)
	}
	if len(homestay.CorrelationKey) > 0 && len(homestay.ResponseErrors.ErrorList) > 0 {
		responseErrors.ErrorList = append(responseErrors.ErrorList, homestay.ResponseErrors.ErrorList...)
	}
	return responseErrors
}

func GetCrossSellCorpStreamResponse(rsc *request.SearchEvent, ch chan clientgateway.CrossSellStreamResponseForCorp, c *gin.Context,
	pageContext string, expData string, secureURL string, imageCount int, neabBy bool, user *request.User, isCorpRequest bool, correlationKey string, crossSellRequest request.CrossSellRequest, reqCardId string) {

	var crossSellCorpSH clientgateway.CrossSellSearchPersonalizedData
	var dres *request.CrossSellDrools
	var crossSellCorpResp clientgateway.CrossSellStreamResponseForCorp
	//Set correlation key
	// if correlationKey is not received from commons, create one
	if len(correlationKey) == 0 {
		correlationKey = guuid.New().String()
	}
	//set correlation key
	//1. Get search hotels from CG
	cards := []clientgateway.CorpCard{}
	card := clientgateway.CorpCard{}
	startTime := time.Now()
	crossSellCorpSH, dres, shreqCorp, dsTime := getHotelSearchDataForCrossSellCorp(rsc, c, pageContext, expData, correlationKey, secureURL, imageCount, neabBy, user, isCorpRequest, crossSellRequest)

	if len(crossSellCorpSH.ResponseErrors.ErrorList) > 0 {
		for _, err := range crossSellCorpSH.ResponseErrors.ErrorList {
			logger.Errorf("Error in SearchHotel : error code %s , error message %s correlationKey %s", err.ErrorCode, err.ErrorMessage, correlationKey)
		}
	} else {
		//2. Map search hotels response for cross sell response
		var heading string
		var subheading string
		areas := getAreasFromRequest(rsc)
		if neabBy && len(crossSellCorpSH.PersonalizedResponse) != 0 && len(crossSellCorpSH.PersonalizedResponse[0].Hotels) != 0 {
			hotelElement := crossSellCorpSH.PersonalizedResponse[0].Hotels[0]
			crossSellCorpSH.FromCity = hotelElement.FromCity
		}

		crossSellCorpSH := modifyAddressCorp(crossSellCorpSH, reqCardId)

		r := strings.NewReplacer("<NOP>", strconv.Itoa(crossSellCorpSH.TotalHotelCounts),
			"<CityName>", crossSellCorpSH.CityName, "<Area>", areas, "<FromCity>", crossSellCorpSH.FromCity)

		var isMyBizPresent bool

		if len(crossSellCorpSH.PersonalizedResponse) != 0 && len(crossSellCorpSH.PersonalizedResponse[0].Section) != 0 {

			for _, element := range crossSellCorpSH.PersonalizedResponse {

				if element.Section == "MYBIZ_ASSURED" {
					isMyBizPresent = true
					break
				}
			}
		}

		if reflect.DeepEqual(*dres, request.CrossSellDrools{}) && len(dres.Heading) == 0 {
			heading = config.AppConfig.Heading.Defaultheading
		} else {

			if !isMyBizPresent {
				heading = "Business Hotels"
			} else {
				if crossSellCorpSH.FiltersRemoved {
					heading = dres.DefaultHeading
				} else {
					heading = dres.Heading
				}
			}

		}
		heading = r.Replace(heading)
		if reflect.DeepEqual(*dres, request.CrossSellDrools{}) && len(dres.SubHeading) == 0 {
			subheading = config.AppConfig.Heading.Defaultsubheading
		} else {

			if !isMyBizPresent {
				subheading = "Business Hotels"
			} else {
				if crossSellCorpSH.FiltersRemoved {
					subheading = dres.DefaultSubHeading
				} else {
					subheading = dres.SubHeading
				}
			}

		}
		subheading = r.Replace(subheading)
		header := clientgateway.Header{
			Header:    heading,
			Subheader: subheading,
		}

		filterList := []clientgateway.Filter{}
		crossSellCorpSH.DeeplinkURL = crossSellCorpSH.ListingDeepLink
		crossSellCorpSH.ListingDeepLink = ""

		// create filterMap such that if DATE_FILTER in request and nearby is true , only DATE_FILTER in response
		if neabBy && rsc.Filter && len(rsc.FilterList) != 0 {

			for _, filter := range rsc.FilterList {

				if filter.FilterType != "DATE_FILTER" {
					continue
				}
				for _, personalizedResponse := range crossSellCorpSH.PersonalizedResponse {
					buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, personalizedResponse.Hotels)
				}
				filterList = append(filterList, filter)
			}
		} else if neabBy {
			// if nearby is true but no date_filter in request , we send distance_filter in response

			if len(crossSellCorpSH.FilterMap) != 0 {

				values := []clientgateway.Filter{}
				ok := false
				values, ok = crossSellCorpSH.FilterMap["DRIVING_DISTANCE_KM"]
				if ok {

					for _, filter := range values {
						//buildHotelIds(&filter, rsc.SearchContext.FromDateStr, rsc.SearchContext.ToDateStr, crossSellSH.HotelList)
						filter.FilterName = filter.Title
						filter.Title = ""
						// appending filter in response only if hotel ids present for that case
						if len(filter.HotelIDs) != 0 {
							buildDeeplinkUrl(&filter, crossSellCorpSH.DeeplinkURL)
							filterList = append(filterList, filter)
						}
					}
				}

			}
		} else {
			//create filter list from filter map if nearby false
			if len(crossSellCorpSH.FilterMap) != 0 {
				for key, filters := range crossSellCorpSH.FilterMap {
					filterList = createFilterMap(filters, key, filterList)
				}

			}

		}
		crossSellCorpSH.FilterList = filterList
		crossSellCorpSH.FilterMap = nil
		card.HeaderData = header

	}

	// 2. process persuasions
	processPersuasionDataCorp(crossSellCorpSH)

	// 3. process placeholders
	plcHolders := processPlaceHoldersCorp(crossSellCorpSH)

	if len(plcHolders) != 0 {
		crossSellCorpSH.Placeholders = plcHolders
	}

	//2. Map search hotels response for cross sell response
	card.CardData = crossSellCorpSH
	card.CardID = rsc.CardID
	card.CardData = crossSellCorpSH
	card.CardID = rsc.CardID
	card.TemplateID = rsc.TemplateID
	card.ComponentID = rsc.ComponentID
	cards = append(cards, card)
	crossSellCorpResp.Cards = cards

	logger.Infof("Total time taken to process search personalized response for correlation key %s is %v", correlationKey, time.Since(startTime))

	//3. PDT Logging
	go logIntoPdtCorp(rsc, correlationKey, dres, crossSellCorpSH, shreqCorp, dsTime)

	//4. Write the response to channel
	ch <- crossSellCorpResp
}

func getAreasFromRequest(rsc *request.SearchEvent) string {
	var areas string
	if !reflect.DeepEqual(rsc.Enrichments, request.Enrichments{}) {
		if !reflect.DeepEqual(rsc.Enrichments.UserPreference, request.RecommendedTrend{}) {
			areas = getAreaNamesFromRequest(&rsc.Enrichments.UserPreference)
		} else if !reflect.DeepEqual(rsc.Enrichments.Trends, request.RecommendedTrend{}) {
			areas = getAreaNamesFromRequest(&rsc.Enrichments.Trends)
		}
	}
	return areas
}

func createFilterMap(filters []clientgateway.Filter, key string, filterList []clientgateway.Filter) []clientgateway.Filter {
	for _, filter := range filters {
		f := clientgateway.Filter{}
		f.FilterType = key
		f.HotelIDs = filter.HotelIDs
		f.FilterValue = filter.FilterValue
		f.FilterRange = filter.FilterRange
		if !reflect.DeepEqual(f.FilterRange, common.FilterRange{}) {
			if f.FilterRange.MinValue == 0 {
				f.FilterName = "under " + filter.Currency + " " + strconv.Itoa(f.FilterRange.MaxValue)
			} else {
				f.FilterName = filter.Currency + " " + strconv.Itoa(f.FilterRange.MinValue) + "-" + filter.Currency + " " + strconv.Itoa(f.FilterRange.MaxValue)
			}
		}
		filterList = append(filterList, f)
	}
	return filterList
}

func buildDeeplinkUrl(filter *clientgateway.Filter, url string) {

	var finalUrl string
	var filterPresent bool
	deeplinkSplit := strings.Split(url, "&")
	for _, subString := range deeplinkSplit {

		if strings.HasPrefix(subString, "filterData") {
			filterPresent = true
			subString = subString + "%5E" + "DRIVING_DISTANCE_KM" + "%7C" + strconv.Itoa(filter.FilterRange.MinValue) + "-" + strconv.Itoa(filter.FilterRange.MaxValue)
		}
		finalUrl = finalUrl + subString + "&"

	}
	finalUrl = finalUrl[:len(finalUrl)-1]
	if !filterPresent {
		finalUrl = finalUrl + "&" + "filterData=" + "DRIVING_DISTANCE_KM" + "%7C" + strconv.Itoa(filter.FilterRange.MinValue) + "-" + strconv.Itoa(filter.FilterRange.MaxValue)
	}
	filter.DeeplinkUrl = finalUrl
}

func buildHotelIds(f *clientgateway.Filter, fromDate request.Date, toDate request.Date, hotelList []clientgateway.Hotel) {

	if int64(f.FilterRange.MinValue) == fromDate.TS && int64(f.FilterRange.MaxValue) == toDate.TS {

		for _, hotel := range hotelList {
			f.HotelIDs = append(f.HotelIDs, hotel.ID)
		}
	}
}

func getAreaNamesFromRequest(trends *request.RecommendedTrend) string {
	var areaNames string
	var noSep = true
	if len(trends.Area) != 0 {
		for _, area := range trends.Area {
			if noSep {
				noSep = false
			} else {
				areaNames = areaNames + ","
			}
			res := strings.Split(area.Value, "::")
			areaNames = areaNames + res[1]
		}
	}
	return areaNames
}

func processPlaceHolders(data clientgateway.CrossSellSearchHotelsData) []clientgateway.PlaceHolderCategory {
	categories := config.PmsCommonConfig.CategoryPriorityList
	var placeHolders []clientgateway.PlaceHolderCategory
	phCat := clientgateway.PlaceHolderCategory{}
	var msFoundInAll = false
	var fcFoundInAll = false
	var blackFoundInAll = false
	if len(data.HotelList) != 0 {
		placeHolders = buildPlaceholders(data.HotelList, categories, phCat, msFoundInAll, blackFoundInAll, fcFoundInAll, placeHolders)
	}
	return placeHolders
}

func processPlaceHoldersCorp(data clientgateway.CrossSellSearchPersonalizedData) []clientgateway.PlaceHolderCategory {
	categories := config.PmsCommonConfig.CategoryPriorityList
	var placeHolders []clientgateway.PlaceHolderCategory
	phCat := clientgateway.PlaceHolderCategory{}
	var msFoundInAll = false
	var fcFoundInAll = false
	var blackFoundInAll = false
	if len(data.PersonalizedResponse) != 0 {
		for j := 0; j < len(data.PersonalizedResponse); j++ {
			if len(data.PersonalizedResponse[j].Hotels) != 0 {
				placeHolders = buildPlaceholders(data.PersonalizedResponse[j].Hotels, categories, phCat, msFoundInAll, blackFoundInAll, fcFoundInAll, placeHolders)
			}
		}
	}
	return placeHolders
}

func buildPlaceholders(data []clientgateway.Hotel, categories []string, phCat clientgateway.PlaceHolderCategory, msFoundInAll bool, blackFoundInAll bool, fcFoundInAll bool, placeHolders []clientgateway.PlaceHolderCategory) []clientgateway.PlaceHolderCategory {
	for _, cat := range categories {
		for _, hotel := range data {
			if "MySafety - Safe and Hygienic Stays" == cat {
				if len(hotel.CategoryDetails) != 0 {
					if val, ok := hotel.CategoryDetails[cat]; ok {
						phCat.Icon = val.IconURL
						phCat.Key = cat
						msFoundInAll = true
					} else {
						msFoundInAll = false
						break
					}
				}
			} else if "MMTBlack" == cat {
				if hotel.DisplayFare != (clientgateway.DisplayFare{}) && hotel.DisplayFare.DisplayPriceBreakDown != (clientgateway.DisplayPriceBreakDown{}) &&
					hotel.DisplayFare.DisplayPriceBreakDown.BlackDiscount > 0.0 {
					blackFoundInAll = true
				} else {
					blackFoundInAll = false
					break
				}
			} else if "FreeCancellation" == cat {
				if hotel.FreeCancellation == true {
					fcFoundInAll = true
				} else {
					fcFoundInAll = false
					break
				}
			}
		}
		if msFoundInAll == true && "MySafety - Safe and Hygienic Stays" == cat {
			placeHolders = append(placeHolders, phCat)
			break
		} else if blackFoundInAll == true && "MMTBlack" == cat {
			phCat = getBlackPlaceHolder()
			placeHolders = append(placeHolders, phCat)
			break
		} else if fcFoundInAll == true && "FreeCancellation" == cat {
			phCat = getFCPlaceHolder()
			placeHolders = append(placeHolders, phCat)
			break
		}
	}
	return placeHolders
}

func processPersuasionData(data clientgateway.CrossSellSearchHotelsData) {
	if len(data.HotelList) != 0 {
		for i := 0; i < len(data.HotelList); i++ {
			buildPersuasionData(data.HotelList[i])
		}

	}
}

func processLandingPersuasionData(data clientgateway.CrossSellSearchHotelsData) {
	if len(data.HotelList) != 0 {
		for i := 0; i < len(data.HotelList); i++ {
			buildPersuasionLandingData(data.HotelList[i])
		}

	}
}

func processPersuasionDataCorp(data clientgateway.CrossSellSearchPersonalizedData) {
	if len(data.PersonalizedResponse) != 0 {
		for j := 0; j < len(data.PersonalizedResponse); j++ {
			if len(data.PersonalizedResponse[j].Hotels) != 0 {
				for i := 0; i < len(data.PersonalizedResponse[j].Hotels); i++ {
					buildPersuasionData(data.PersonalizedResponse[j].Hotels[i])
				}
			}
		}
	}
}

func buildPersuasionData(data clientgateway.Hotel) {
	if data.Persuasions == nil {
		if data.MealPlanIncluded != (clientgateway.ExtraMeal{}) && data.MealPlanIncluded.Code != "EP" {
			var perKey = "MEAL_PLAN_INCLUDED" + "|" + data.MealPlanIncluded.Code
			data.Persuasions = getDynamicPersuasion(perKey)
		} else if data.FreeCancellation == true {
			var perKey = "M_I03"
			data.Persuasions = getDynamicPersuasion(perKey)
		} else if data.ExtraMeals != (clientgateway.ExtraMeal{}) && data.ExtraMeals.Code != "EP" {
			var perKey = "EXTRA_MEAL_AVAILABLE" + "|" + data.ExtraMeals.Code
			data.Persuasions = getDynamicPersuasion(perKey)
		}
	} else {
		data.Persuasions = getPeithoPersuasions(data.Persuasions)
	}
}

func buildPersuasionLandingData(data clientgateway.Hotel) {
	if data.Persuasions == nil {
		data.Persuasions = getPeithoPersuasions(data.Persuasions)
	}
}

func getFCPlaceHolder() clientgateway.PlaceHolderCategory {
	var dynamicPersuasion = config.PmsCommonConfig.CategoryIconsMap
	var res map[string]clientgateway.CategoryIconData
	_ = json.Unmarshal([]byte(dynamicPersuasion), &res)
	phCat := clientgateway.PlaceHolderCategory{}
	fcData := res["FreeCancellation"]
	phCat.Value = fcData.Value
	phCat.Icon = fcData.Icon
	phCat.Key = "FreeCancellation"
	return phCat
}

func getBlackPlaceHolder() clientgateway.PlaceHolderCategory {
	var dynamicPersuasion = config.PmsCommonConfig.CategoryIconsMap
	var res map[string]clientgateway.CategoryIconData
	_ = json.Unmarshal([]byte(dynamicPersuasion), &res)
	phCat := clientgateway.PlaceHolderCategory{}
	fcData := res["MMTBlack"]
	phCat.Value = fcData.Value
	phCat.Icon = fcData.Icon
	phCat.Key = "MMTBlack"
	return phCat
}

func getDynamicPersuasion(perKey string) []clientgateway.Persuasion {
	var dynamicPersuasion = config.PmsCommonConfig.DynamicPersuasionData
	var persuasionsList []clientgateway.Persuasion
	var res map[string]clientgateway.DynamicPersuasion
	_ = json.Unmarshal([]byte(dynamicPersuasion), &res)
	dynPer := res[perKey]
	per := clientgateway.Persuasion{}
	per.ID = dynPer.Id
	per.Desc = dynPer.Desc
	per.Placeholder = dynPer.Placeholder
	per.Priority = dynPer.Priority
	per.Type = "DYNAMIC"
	persuasionsList = append(persuasionsList, per)
	return persuasionsList
}

func getPeithoPersuasions(persuasions []clientgateway.Persuasion) []clientgateway.Persuasion {
	for i := 0; i < len(persuasions); i++ {
		persuasions[i].Type = "PEITHO"
	}
	return persuasions
}

func logIntoPdt(rsc *request.SearchEvent, ck string, dres *request.CrossSellDrools, crossSellSH clientgateway.CrossSellSearchHotelsData, shreq *clientbackend.SearchHotelsRequest, dsTime int) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Panic Recovered, error was %v \n %v", err, string(debug.Stack()))
		}
	}()

	adultAges, childAges, adultCount, childCount, areas, price, hotels, locID, locType, countryCode, checkinDate, checkoutDate, ruleID, heading := loggingPdt(rsc, dres)

	errors := make([]string, 0)
	if len(crossSellSH.ResponseErrors.ErrorList) > 0 {
		for _, err := range crossSellSH.ResponseErrors.ErrorList {
			errors = append(errors, err.ErrorMessage)
		}
	}

	appliedFilter := []common.Filter{}
	if shreq == nil {
		appliedFilter = nil
	} else {
		appliedFilter = getAllFilters(shreq.AppliedFilterMap)
	}

	csPdtModel := pdtModels.CrossSellPDTModel{
		CorrelationKey:       ck,
		LocationId:           locID,
		LocationType:         locType,
		CountryCode:          countryCode,
		CheckIn:              checkinDate,
		CheckOut:             checkoutDate,
		NoOfRooms:            len(rsc.SearchContext.Pax),
		AdultAges:            adultAges,
		AdultCount:           adultCount,
		CardId:               rsc.CardID,
		TemplateId:           rsc.TemplateID,
		ChildAges:            childAges,
		ChildCount:           childCount,
		EnrichmentType:       "trends",
		Errors:               strings.Join(errors, ","),
		FiltersRemoved:       crossSellSH.FiltersRemoved,
		FiltersModified:      false, //TODO compare with base rule id.. derived field?
		RequestedAreas:       areas,
		RequestedStarRatings: nil, //TODO once star rating comes in enrichments
		RequestedPriceRange:  price,
		RequestedHotelIds:    hotels,
		AppliedFilterMap:     appliedFilter,
		NoOfHotelsReq:        rsc.Limit,
		NoOfHotelsResp:       crossSellSH.TotalHotelCounts,
		Heading:              heading,
		RuleId:               ruleID, //TODO once it starts flowing from HES drools
		DownstreamTime:       dsTime,
	}
	pdt.Log(csPdtModel, config.AppConfig.KafkaConfig.CrossellTopicId, config.AppConfig.AvroConfig.CrossellTemplateId)
}

func logIntoPdtCorp(rsc *request.SearchEvent, ck string, dres *request.CrossSellDrools, crossSellSH clientgateway.CrossSellSearchPersonalizedData, shreq *clientbackend.SearchPersonalizedHotelsRequest, dsTime int) {
	defer func() {
		if err := recover(); err != nil {
			logger.Errorf("Panic Recovered, error was %v \n %v", err, string(debug.Stack()))
		}
	}()

	adultAges, childAges, adultCount, childCount, areas, price, hotels, locID, locType, countryCode, checkinDate, checkoutDate, ruleID, heading := loggingPdt(rsc, dres)

	errors := make([]string, 0)
	if len(crossSellSH.ResponseErrors.ErrorList) > 0 {
		for _, err := range crossSellSH.ResponseErrors.ErrorList {
			errors = append(errors, err.ErrorMessage)
		}
	}
	appliedFilter := []common.Filter{}
	if shreq == nil {
		appliedFilter = nil
	} else {
		appliedFilter = getAllFilters(shreq.AppliedFilterMap)
	}
	csPdtModel := pdtModels.CrossSellPDTModel{
		CorrelationKey:       ck,
		LocationId:           locID,
		LocationType:         locType,
		CountryCode:          countryCode,
		CheckIn:              checkinDate,
		CheckOut:             checkoutDate,
		NoOfRooms:            len(rsc.SearchContext.Pax),
		AdultAges:            adultAges,
		AdultCount:           adultCount,
		CardId:               rsc.CardID,
		TemplateId:           rsc.TemplateID,
		ChildAges:            childAges,
		ChildCount:           childCount,
		EnrichmentType:       "trends",
		Errors:               strings.Join(errors, ","),
		FiltersRemoved:       crossSellSH.FiltersRemoved,
		FiltersModified:      false, //TODO compare with base rule id.. derived field?
		RequestedAreas:       areas,
		RequestedStarRatings: nil, //TODO once star rating comes in enrichments
		RequestedPriceRange:  price,
		RequestedHotelIds:    hotels,
		AppliedFilterMap:     appliedFilter,
		NoOfHotelsReq:        rsc.Limit,
		NoOfHotelsResp:       crossSellSH.TotalHotelCounts,
		Heading:              heading,
		RuleId:               ruleID, //TODO once it starts flowing from HES drools
		DownstreamTime:       dsTime,
	}
	pdt.Log(csPdtModel, config.AppConfig.KafkaConfig.CrossellTopicId, config.AppConfig.AvroConfig.CrossellTemplateId)
}

func loggingPdt(rsc *request.SearchEvent, dres *request.CrossSellDrools) ([]int, []int, int, int, []string, []request.TrendValue, []string, string, string, string, string, string, string, string) {
	adultAges := make([]int, 0)
	childAges := make([]int, 0)
	adultCount := 0
	childCount := 0
	if len(rsc.SearchContext.Pax) != 0 {
		for _, pax := range rsc.SearchContext.Pax {
			if !reflect.DeepEqual(pax.Details.Adult, request.PaxCountDetails{}) {
				adultAges = append(adultAges, pax.Details.Adult.Ages...)
				adultCount = adultCount + pax.Details.Adult.Count
			}
			if !reflect.DeepEqual(pax.Details.Child, request.PaxCountDetails{}) {
				childAges = append(childAges, pax.Details.Child.Ages...)
				childCount = childCount + pax.Details.Child.Count
			}
			if !reflect.DeepEqual(pax.Details.Infant, request.PaxCountDetails{}) {
				childAges = append(childAges, pax.Details.Infant.Ages...)
				childCount = childCount + pax.Details.Infant.Count
			}
		}
	}
	areas, price, hotels := getFilterStrings(rsc.Enrichments.Trends)

	locID := rsc.SearchContext.To.Locus.City
	locType := "city" //hard coded as per discussion
	countryCode := rsc.SearchContext.To.CountryCode
	checkinTime := utils.GetTimeFromEpochAndZone(rsc.SearchContext.FromDateStr.Zone, rsc.SearchContext.FromDateStr.TS)
	checkinDate := utils.GetTimeInFormattedString(checkinTime)

	checkoutTime := utils.GetTimeFromEpochAndZone(rsc.SearchContext.ToDateStr.Zone, rsc.SearchContext.ToDateStr.TS)
	checkoutDate := utils.GetTimeInFormattedString(checkoutTime)
	var ruleID string
	var heading string
	if dres != nil {
		ruleID = dres.RuleID
		heading = dres.Heading
	}
	return adultAges, childAges, adultCount, childCount, areas, price, hotels, locID, locType, countryCode, checkinDate, checkoutDate, ruleID, heading
}

func getAllFilters(afmp *map[common.Filter_Group][]common.Filter) []common.Filter {
	if nil == afmp {
		return nil
	}
	filters := make([]common.Filter, 0)
	for _, fltrs := range *afmp {
		filters = append(filters, fltrs...)
	}
	return filters
}

func getFilterStrings(trend request.RecommendedTrend) (area []string, price []request.TrendValue, hotels []string) {
	areaArr := make([]string, 0)
	hotelsArr := make([]string, 0)
	priceStr := make([]request.TrendValue, 0)
	if trend.Area != nil && len(trend.Area) > 0 {
		for _, areaTrend := range trend.Area {
			areaArr = append(areaArr, areaTrend.Value)
		}
	}
	if trend.HotelIDs != nil && len(trend.HotelIDs) > 0 {
		for _, hotelIds := range trend.HotelIDs {
			hotelsArr = append(hotelsArr, hotelIds.Value)
		}
	}
	if trend.Price != nil && len(trend.Price) > 0 {
		priceStr = trend.Price
	}

	return areaArr, priceStr, hotelsArr
}

func modifyAddress(crossSellSHD clientgateway.CrossSellSearchHotelsData, reqCardId string) clientgateway.CrossSellSearchHotelsData {
	//Checking for recently viewed card
	if len(crossSellSHD.HotelList) > 0 {
		for i := 0; i < len(crossSellSHD.HotelList); i++ {
			if strings.EqualFold(reqCardId, constants.RECENTLY_VIEWED_HOTELS) || len(crossSellSHD.HotelList[i].Address.Area) <= 0 {
				crossSellSHD.HotelList[i].Address.DisplayLocationName = crossSellSHD.HotelList[i].CityName
			} else if len(crossSellSHD.HotelList[i].Address.Area) > 0 {
				crossSellSHD.HotelList[i].Address.DisplayLocationName = crossSellSHD.HotelList[i].Address.Area[0]
			}
			if strings.EqualFold(reqCardId, constants.MMT_IMMERESIVE_THEMES_CARD_V2) || strings.EqualFold(reqCardId, constants.MMT_IMMERESIVE_THEMES_CARD) {
				crossSellSHD.HotelList[i].Address.DisplayLocationName = crossSellSHD.HotelList[i].CityName
			}
		}
	}
	return crossSellSHD
}

func modifyAddressCorp(crossSellSP clientgateway.CrossSellSearchPersonalizedData, reqCardId string) clientgateway.CrossSellSearchPersonalizedData {
	//Checking for recently viewed card
	if len(crossSellSP.PersonalizedResponse) > 0 {
		for i := 0; i < len(crossSellSP.PersonalizedResponse); i++ {
			if len(crossSellSP.PersonalizedResponse[i].Hotels) > 0 {
				for j := 0; j < len(crossSellSP.PersonalizedResponse[i].Hotels); j++ {
					if strings.EqualFold(reqCardId, constants.RECENTLY_VIEWED_HOTELS) || len(crossSellSP.PersonalizedResponse[i].Hotels[j].Address.Area) <= 0 {
						crossSellSP.PersonalizedResponse[i].Hotels[j].Address.DisplayLocationName = crossSellSP.PersonalizedResponse[i].Hotels[j].CityName
					} else if len(crossSellSP.PersonalizedResponse[i].Hotels[j].Address.Area) > 0 {
						crossSellSP.PersonalizedResponse[i].Hotels[j].Address.DisplayLocationName = crossSellSP.PersonalizedResponse[i].Hotels[j].Address.Area[0]
					}
				}
			}
		}
	}
	return crossSellSP
}

func buildIconTags(iconTags clientgateway.IconTags, resp clientgateway.CrossSellCardData) clientgateway.IconTags {
	// Populate IconTags
	iconTags.Text = resp.Data.IconTags.Text
	iconTags.BgGradient.Start = resp.Data.IconTags.BgGradient.Start
	iconTags.BgGradient.End = resp.Data.IconTags.BgGradient.End
	iconTags.BorderColor = resp.Data.IconTags.BorderColor
	return iconTags
}
