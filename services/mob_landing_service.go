package services

import (
	"Hotels-Scion/config"
	"Hotels-Scion/constants"
	"Hotels-Scion/helpers"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/response"
	"github.com/gin-gonic/gin"
	guuid "github.com/google/uuid"
	"math"
	"strings"
)

func GetMobLandingResponse(request *request.MobLandingRequest, c *gin.Context) (response.MobLandingResponse, error) {
	// Brand-based routing logic
	brand := request.RequestDetails.Brand
	logger.Infof("Processing mob-landing request for brand: %s", brand)

	if strings.EqualFold(brand, constants.MMT) {
		// Route to MMT backend
		return getMMTMobLandingResponse(request, c)
	} else {
		// Default to GoIbibo backend (existing flow)
		return getGoibiboMobLandingResponse(request, c)
	}
}

func getGoibiboMobLandingResponse(request *request.MobLandingRequest, c *gin.Context) (response.MobLandingResponse, error) {
	resp := response.MobLandingResponse{}
	cardInfo := response.CardInfo{}
	cardPayload := response.CardPayload{}
	cardData := response.CardData{}
	clientRequest := getCGRequestForMobLanding(request)
	cgResponse := getHotelMobLanding(clientRequest, c)
	resp.Response.CorrelationKey = cgResponse.CorrelationKey
	isDsDFound := false
	cityName := ""
	for i := 0; i < len(cgResponse.Response.ListPersonalizationResponse.CardData); i++ {
		firstCardData := cgResponse.Response.ListPersonalizationResponse.CardData[i]
		if "DAILYSTEALDEAL" == firstCardData.CardInfo.ID {
			cardInfo.ID = firstCardData.CardInfo.ID
			cardPayload.ViewAllCard.Title = firstCardData.CardInfo.CardPayload.ViewAllCard.Title
			cardPayload.ViewAllCard.CtaTitle = firstCardData.CardInfo.CardPayload.ViewAllCard.CtaTitle
			cardPayload.ViewAllCard.IconURL = firstCardData.CardInfo.CardPayload.ViewAllCard.IconURL
			cardPayload.ViewAllCard.SubTitle = firstCardData.CardInfo.CardPayload.ViewAllCard.SubTitle
			cardPayload.ViewAllCard.VoyCityID = firstCardData.CardInfo.CardPayload.ViewAllCard.VoyCityID

			cardPayload.TimerCard.TimerRemaining = firstCardData.CardInfo.CardPayload.TimerCard.TimerRemaining
			cardPayload.TimerCard.TimerTextPrefix = firstCardData.CardInfo.CardPayload.TimerCard.TimerTextPrefix
			cardPayload.TimerCard.BgImageURL = firstCardData.CardInfo.CardPayload.TimerCard.BgImageURL
			cardPayload.TimerCard.BottomSubtitle = firstCardData.CardInfo.CardPayload.TimerCard.BottomSubtitle
			cardPayload.TimerCard.BottomTitle = firstCardData.CardInfo.CardPayload.TimerCard.BottomTitle
			cardPayload.HotelListNew = []response.HotelListNew{}
			for k := 0; k < len(firstCardData.CardInfo.CardPayload.HotelListNew); k++ {
				hotelDetail := getHotelDataForDSD(request, firstCardData, k)
				cityName = hotelDetail.CityName
				cardPayload.HotelListNew = append(cardPayload.HotelListNew, hotelDetail)
			}
			helpers.BuildGiDeeplinkForDSDCity(request, cityName, &cardPayload)
			cardInfo.CardPayload = cardPayload
			cardData.CardInfo = cardInfo
			cardData.Sequence = firstCardData.Sequence
			resp.Response.CardData = append(resp.Response.CardData, cardData)
			isDsDFound = true
			break
		}
	}
	if !isDsDFound {
		resp := response.MobLandingResponse{}
		resp.Response.CorrelationKey = cgResponse.CorrelationKey
	}
	return resp, nil
}

func getMMTMobLandingResponse(request *request.MobLandingRequest, c *gin.Context) (response.MobLandingResponse, error) {
	logger.Infof("Processing MMT mob-landing request")
	resp := response.MobLandingResponse{}

	// Transform Scion request to MMT format
	//mmtRequest := getMMTRequestForMobLanding(request)
	mmtRequest := getCGRequestForMobLanding(request)

	// Call MMT backend
	mmtResponse := getMMTHotelMobLanding(mmtRequest, c)

	// Transform MMT response to Scion format
	resp.Response.CorrelationKey = mmtResponse.CorrelationKey

	// Process MMT cards and convert to Scion format
	for i := 0; i < len(mmtResponse.Response.ListPersonalizationResponse.CardData); i++ {
		mmtCardData := mmtResponse.Response.ListPersonalizationResponse.CardData[i]

		// Convert MMT card to Scion card format
		scionCardData := response.CardData{}
		scionCardData.Sequence = mmtCardData.Sequence
		scionCardData.CardInfo.ID = mmtCardData.CardInfo.ID

		// Map MMT card payload to Scion format
		scionCardPayload := response.CardPayload{}

		// Map GenericCardData array
		scionCardPayload.GenericCardData = []response.GenericCardData{}
		for j := 0; j < len(mmtCardData.CardInfo.CardPayload.GenericCardData); j++ {
			genericCard := response.GenericCardData{}
			genericCard.Text = mmtCardData.CardInfo.CardPayload.GenericCardData[j].Text
			genericCard.SubText = mmtCardData.CardInfo.CardPayload.GenericCardData[j].SubText
			genericCard.IconUrl = mmtCardData.CardInfo.CardPayload.GenericCardData[j].IconUrl
			genericCard.Duration = mmtCardData.CardInfo.CardPayload.GenericCardData[j].Duration
			genericCard.PromoCode = mmtCardData.CardInfo.CardPayload.GenericCardData[j].PromoCode
			scionCardPayload.GenericCardData = append(scionCardPayload.GenericCardData, genericCard)
		}

		// Map LocationData array
		scionCardPayload.LocationData = []response.LocationData{}
		for j := 0; j < len(mmtCardData.CardInfo.CardPayload.LocationData); j++ {
			locationData := response.LocationData{}
			locationData.Title = mmtCardData.CardInfo.CardPayload.LocationData[j].Title
			locationData.Description = mmtCardData.CardInfo.CardPayload.LocationData[j].Description
			locationData.ImageUrl = mmtCardData.CardInfo.CardPayload.LocationData[j].ImageUrl
			locationData.VideoUrl = mmtCardData.CardInfo.CardPayload.LocationData[j].VideoUrl
			locationData.ThumbnailUrl = mmtCardData.CardInfo.CardPayload.LocationData[j].ThumbnailUrl
			locationData.Deeplink = mmtCardData.CardInfo.CardPayload.LocationData[j].Deeplink

			// Map InfoList from LocationData
			locationData.InfoList = []response.InfoItem{}
			for k := 0; k < len(mmtCardData.CardInfo.CardPayload.LocationData[j].InfoList); k++ {
				infoItem := response.InfoItem{}
				infoItem.IconUrl = mmtCardData.CardInfo.CardPayload.LocationData[j].InfoList[k].IconUrl
				infoItem.Title = mmtCardData.CardInfo.CardPayload.LocationData[j].InfoList[k].Title
				locationData.InfoList = append(locationData.InfoList, infoItem)
			}
			scionCardPayload.LocationData = append(scionCardPayload.LocationData, locationData)
		}

		// Map HotelListNew if present
		if len(mmtCardData.CardInfo.CardPayload.HotelListNew) > 0 {
			scionCardPayload.HotelListNew = []response.HotelListNew{}
			for k := 0; k < len(mmtCardData.CardInfo.CardPayload.HotelListNew); k++ {
				hotelDetail := getHotelDataForMMT(request, mmtCardData, k)
				scionCardPayload.HotelListNew = append(scionCardPayload.HotelListNew, hotelDetail)
			}
		}

		// Map TimerCard if present
		scionCardPayload.TimerCard.TimerRemaining = mmtCardData.CardInfo.CardPayload.TimerCard.TimerRemaining
		scionCardPayload.TimerCard.TimerTextPrefix = mmtCardData.CardInfo.CardPayload.TimerCard.TimerTextPrefix
		scionCardPayload.TimerCard.BgImageURL = mmtCardData.CardInfo.CardPayload.TimerCard.BgImageURL
		scionCardPayload.TimerCard.BottomSubtitle = mmtCardData.CardInfo.CardPayload.TimerCard.BottomSubtitle
		scionCardPayload.TimerCard.BottomTitle = mmtCardData.CardInfo.CardPayload.TimerCard.BottomTitle

		// Map ViewAllCard if present
		scionCardPayload.ViewAllCard.Title = mmtCardData.CardInfo.CardPayload.ViewAllCard.Title
		scionCardPayload.ViewAllCard.CtaTitle = mmtCardData.CardInfo.CardPayload.ViewAllCard.CtaTitle
		scionCardPayload.ViewAllCard.IconURL = mmtCardData.CardInfo.CardPayload.ViewAllCard.IconURL
		scionCardPayload.ViewAllCard.SubTitle = mmtCardData.CardInfo.CardPayload.ViewAllCard.SubTitle
		scionCardPayload.ViewAllCard.VoyCityID = mmtCardData.CardInfo.CardPayload.ViewAllCard.VoyCityID

		scionCardData.CardInfo.CardPayload = scionCardPayload
		resp.Response.CardData = append(resp.Response.CardData, scionCardData)
	}

	return resp, nil
}

func getHotelDataForDSD(request *request.MobLandingRequest, firstCardData clientgateway.CardData, k int) response.HotelListNew {
	hotelDetail := response.HotelListNew{}
	hotelDetail.ID = firstCardData.CardInfo.CardPayload.HotelListNew[k].ID
	hotelDetail.GiID = firstCardData.CardInfo.CardPayload.HotelListNew[k].GiID
	hotelDetail.Name = firstCardData.CardInfo.CardPayload.HotelListNew[k].Name
	hotelDetail.AppDeeplink = firstCardData.CardInfo.CardPayload.HotelListNew[k].AppDeeplink
	hotelDetail.CityName = firstCardData.CardInfo.CardPayload.HotelListNew[k].LocationDetail.Name
	media := response.Media{}

	hotelDetail.Media = []response.Media{}
	media.MediaType = firstCardData.CardInfo.CardPayload.HotelListNew[k].Media[0].MediaType
	media.URL = firstCardData.CardInfo.CardPayload.HotelListNew[k].Media[0].URL
	hotelDetail.Media = append(hotelDetail.Media, media)
	hotelDetail.HotelPersuasions = firstCardData.CardInfo.CardPayload.HotelListNew[k].HotelPersuasions

	hotelDetail.PriceDetail.Price = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Price
	hotelDetail.PriceDetail.DiscountedPrice = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.DiscountedPrice
	hotelDetail.PriceDetail.DiscountedPriceWithTax = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.DiscountedPriceWithTax
	hotelDetail.PriceDetail.TotalTax = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.TotalTax
	if hotelDetail.PriceDetail.DiscountedPrice > 0 && hotelDetail.PriceDetail.Price > 0 {
		hotelDetail.PriceDetail.DiffPercentage = math.Round((hotelDetail.PriceDetail.DiscountedPrice / hotelDetail.PriceDetail.Price) * 100)
	}
	if hotelDetail.PriceDetail.DiscountedPriceWithTax > 0 && hotelDetail.PriceDetail.Price > 0 {
		hotelDetail.PriceDetail.DiffPercentage = math.Round((hotelDetail.PriceDetail.DiscountedPrice / hotelDetail.PriceDetail.Price) * 100)
	}
	hotelDetail.PriceDetail.Coupon.CouponAmount = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.CouponAmount
	hotelDetail.PriceDetail.Coupon.Code = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Code
	hotelDetail.PriceDetail.Coupon.AutoApplicable = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.AutoApplicable
	hotelDetail.PriceDetail.Coupon.BankOffer = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.BankOffer
	hotelDetail.PriceDetail.Coupon.BnplAllowed = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.BnplAllowed
	hotelDetail.PriceDetail.Coupon.Description = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Description
	hotelDetail.PriceDetail.Coupon.Disabled = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Disabled
	hotelDetail.PriceDetail.Coupon.NoCostEmiApplicable = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.NoCostEmiApplicable
	hotelDetail.PriceDetail.Coupon.SpecialPromo = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.SpecialPromo
	hotelDetail.PriceDetail.Coupon.Type = firstCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Type
	helpers.BuildGiDeeplinkForDSDHotel(request, &hotelDetail, &firstCardData.CardInfo.CardPayload.HotelListNew[k])
	return hotelDetail
}

func getHotelDataForMMT(request *request.MobLandingRequest, mmtCardData clientgateway.CardData, k int) response.HotelListNew {
	hotelDetail := response.HotelListNew{}
	hotelDetail.ID = mmtCardData.CardInfo.CardPayload.HotelListNew[k].ID
	hotelDetail.GiID = mmtCardData.CardInfo.CardPayload.HotelListNew[k].GiID
	hotelDetail.Name = mmtCardData.CardInfo.CardPayload.HotelListNew[k].Name
	hotelDetail.AppDeeplink = mmtCardData.CardInfo.CardPayload.HotelListNew[k].AppDeeplink
	hotelDetail.CityName = mmtCardData.CardInfo.CardPayload.HotelListNew[k].LocationDetail.Name

	// Map media
	hotelDetail.Media = []response.Media{}
	if len(mmtCardData.CardInfo.CardPayload.HotelListNew[k].Media) > 0 {
		media := response.Media{}
		media.MediaType = mmtCardData.CardInfo.CardPayload.HotelListNew[k].Media[0].MediaType
		media.URL = mmtCardData.CardInfo.CardPayload.HotelListNew[k].Media[0].URL
		hotelDetail.Media = append(hotelDetail.Media, media)
	}

	hotelDetail.HotelPersuasions = mmtCardData.CardInfo.CardPayload.HotelListNew[k].HotelPersuasions

	// Map price details
	hotelDetail.PriceDetail.Price = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Price
	hotelDetail.PriceDetail.DiscountedPrice = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.DiscountedPrice
	hotelDetail.PriceDetail.DiscountedPriceWithTax = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.DiscountedPriceWithTax
	hotelDetail.PriceDetail.TotalTax = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.TotalTax

	// Calculate discount percentage
	if hotelDetail.PriceDetail.DiscountedPrice > 0 && hotelDetail.PriceDetail.Price > 0 {
		hotelDetail.PriceDetail.DiffPercentage = math.Round((hotelDetail.PriceDetail.DiscountedPrice / hotelDetail.PriceDetail.Price) * 100)
	}
	if hotelDetail.PriceDetail.DiscountedPriceWithTax > 0 && hotelDetail.PriceDetail.Price > 0 {
		hotelDetail.PriceDetail.DiffPercentage = math.Round((hotelDetail.PriceDetail.DiscountedPrice / hotelDetail.PriceDetail.Price) * 100)
	}

	// Map coupon details
	hotelDetail.PriceDetail.Coupon.CouponAmount = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.CouponAmount
	hotelDetail.PriceDetail.Coupon.Code = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Code
	hotelDetail.PriceDetail.Coupon.AutoApplicable = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.AutoApplicable
	hotelDetail.PriceDetail.Coupon.BankOffer = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.BankOffer
	hotelDetail.PriceDetail.Coupon.BnplAllowed = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.BnplAllowed
	hotelDetail.PriceDetail.Coupon.Description = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Description
	hotelDetail.PriceDetail.Coupon.Disabled = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Disabled
	hotelDetail.PriceDetail.Coupon.NoCostEmiApplicable = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.NoCostEmiApplicable
	hotelDetail.PriceDetail.Coupon.SpecialPromo = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.SpecialPromo
	hotelDetail.PriceDetail.Coupon.Type = mmtCardData.CardInfo.CardPayload.HotelListNew[k].PriceDetail.Coupon.Type

	// Build deeplinks for MMT (similar to GoIbibo but for MMT backend)
	helpers.BuildGiDeeplinkForDSDHotel(request, &hotelDetail, &mmtCardData.CardInfo.CardPayload.HotelListNew[k])

	return hotelDetail
}

func getCGRequestForMobLanding(request *request.MobLandingRequest) clientbackend.MobLandingRequest {
	clientRequest := clientbackend.MobLandingRequest{}
	clientRequest.RequiredApis.CardRequired = request.RequiredApis.CardRequired
	clientRequest.RequiredApis.PersonalizationRequired = request.RequiredApis.CardRequired
	// if correlationKey is not received from commons, create one
	if clientRequest.CorrelationKey = request.RequestDetails.RequestId; clientRequest.CorrelationKey == "" {
		clientRequest.CorrelationKey = guuid.New().String()
	}

	clientRequest.CardIds = request.RequestDetails.CardIds
	clientRequest.SearchCriteria.CityCode = request.SearchCriteria.CityCode
	clientRequest.SearchCriteria.CountryCode = request.SearchCriteria.CountryCode
	clientRequest.SearchCriteria.CheckIn = request.SearchCriteria.CheckIn
	clientRequest.SearchCriteria.CheckOut = request.SearchCriteria.CheckOut
	clientRequest.SearchCriteria.Language = request.SearchCriteria.Language
	clientRequest.SearchCriteria.LocationId = request.SearchCriteria.LocationId
	clientRequest.SearchCriteria.LocationType = request.SearchCriteria.LocationType
	clientRequest.SearchCriteria.Currency = request.SearchCriteria.Currency
	clientRequest.SearchCriteria.VcId = request.SearchCriteria.VcId
	clientRequest.SearchCriteria.Limit = request.SearchCriteria.Limit

	clientRequest.SearchCriteria.PersonalizedSearch = request.SearchCriteria.PersonalizedSearch
	clientRequest.SearchCriteria.RoomStayCandidates = []clientbackend.RoomStayCandidate{}
	for i := 0; i < len(request.SearchCriteria.RoomStayCandidates); i++ {
		candidate := clientbackend.RoomStayCandidate{}
		candidate.AdultCount = request.SearchCriteria.RoomStayCandidates[i].AdultCount
		candidate.Rooms = request.SearchCriteria.RoomStayCandidates[i].Rooms
		candidate.ChildAges = request.SearchCriteria.RoomStayCandidates[i].ChildAges
		clientRequest.SearchCriteria.RoomStayCandidates = append(clientRequest.SearchCriteria.RoomStayCandidates, candidate)
	}

	clientRequest.SearchCriteria.CollectionCriteria.LuxeCardRequired = request.SearchCriteria.CollectionCriteria.LuxeCardRequired
	clientRequest.SearchCriteria.CollectionCriteria.CollectionRequired = request.SearchCriteria.CollectionCriteria.CollectionRequired
	clientRequest.SearchCriteria.CollectionCriteria.StaticFilterCardsRequired = request.SearchCriteria.CollectionCriteria.StaticFilterCardsRequired
	clientRequest.SearchCriteria.CollectionCriteria.InspiredCardsRequired = request.SearchCriteria.CollectionCriteria.InspiredCardsRequired
	clientRequest.SearchCriteria.CollectionCriteria.ValueStayCardsRequired = request.SearchCriteria.CollectionCriteria.ValueStayCardsRequired
	clientRequest.SearchCriteria.CollectionCriteria.AthenaCategory = request.SearchCriteria.CollectionCriteria.AthenaCategory
	clientRequest.SearchCriteria.CollectionCriteria.OffbeatCitiesCardsRequired = request.SearchCriteria.CollectionCriteria.OffbeatCitiesCardsRequired
	clientRequest.SearchCriteria.CollectionCriteria.PropertyTypeCards = request.SearchCriteria.CollectionCriteria.PropertyTypeCards

	clientRequest.RequestDetails.Requestor = request.RequestDetails.Requestor
	clientRequest.RequestDetails.Requester = "SCION"
	clientRequest.RequestDetails.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	clientRequest.RequestDetails.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	clientRequest.RequestDetails.RequestId = request.RequestDetails.RequestId
	clientRequest.RequestDetails.Channel = request.RequestDetails.Channel
	clientRequest.RequestDetails.Brand = request.RequestDetails.Brand
	clientRequest.RequestDetails.Region = request.RequestDetails.Region
	clientRequest.RequestDetails.FunnelSource = request.RequestDetails.FunnelSource
	clientRequest.RequestDetails.IdContext = request.RequestDetails.IdContext
	clientRequest.RequestDetails.Profile = request.RequestDetails.Profile
	clientRequest.RequestDetails.PageContext = request.RequestDetails.PageContext
	clientRequest.RequestDetails.VisitorId = request.RequestDetails.VisitorId
	clientRequest.RequestDetails.LoggedIn = request.RequestDetails.LoggedIn
	clientRequest.RequestDetails.VisitNumber = request.RequestDetails.VisitNumber
	clientRequest.RequestDetails.Premium = request.RequestDetails.Premium

	clientRequest.DeviceDetails.DeviceId = request.DeviceDetails.DeviceId
	clientRequest.DeviceDetails.DeviceType = request.DeviceDetails.DeviceType
	clientRequest.DeviceDetails.BookingDevice = request.DeviceDetails.BookingDevice
	clientRequest.DeviceDetails.AppVersion = request.DeviceDetails.AppVersion

	clientRequest.ExpData = request.ExpData

	clientRequest.FeatureFlags.Locus = request.FeatureFlags.Locus
	clientRequest.FeatureFlags.Coupon = request.FeatureFlags.Coupon
	clientRequest.FeatureFlags.BestCoupon = request.FeatureFlags.BestCoupon

	return clientRequest
}
