package services

import (
	"Hotels-Scion/config"
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/constants"
	"Hotels-Scion/models/downstream/orchestrator"
	"Hotels-Scion/models/request"
	"Hotels-Scion/models/request/common"
	"Hotels-Scion/models/response"
	"Hotels-Scion/utils"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"
)

func GetAltAccoData(searchParams *common.SearchEvent, correlationKey string) response.GetPropertyTypeRsp{
	var getPropertyRsp response.GetPropertyTypeRsp
	altAccoRsp := orchestrator.AltAccoDataResponse{}
	altAccoRsp  = restconnectors.GetAltAccoPropertyData(searchParams, correlationKey)
	if altAccoRsp.RspErrors.ErrorList != nil{
		for _,err := range altAccoRsp.RspErrors.ErrorList{
			logger.Errorf("Error in GetPropertyType : error code %s , error message %s correlationKey %s" ,err.<PERSON>rror<PERSON><PERSON>,err.ErrorMessage,correlationKey)
		}
		err := altAccoRsp.RspErrors.ErrorList[0]
		getPropertyRsp = prepareErrorRsp(err.ErrorCode, err.ErrorMessage)
		return getPropertyRsp
	}
	getPropertyRsp = prepareCosmosResponse(altAccoRsp, searchParams)
	return getPropertyRsp
}

func prepareCosmosResponse(altAccoRsp orchestrator.AltAccoDataResponse, searchParams *common.SearchEvent) response.GetPropertyTypeRsp{
	var resp response.GetPropertyTypeRsp
	altAccoPropertyList := altAccoRsp.CardPayload.AltAccoData
	updateDeeplinkUrl(altAccoPropertyList, searchParams)
	heading := config.AppConfig.AltAccoPropertyHeading
	altAccoData := response.AltAccoData{Heading:heading, AltAccoData:altAccoPropertyList}

	resp = response.GetPropertyTypeRsp{Status:"OK"}
	resp.OfferData = make(map[string]response.AltAccoPropertyType)
	resp.OfferData[constants.ALT_ACCO_PROPERTY] = response.AltAccoPropertyType{Data:altAccoData, DataKey:strconv.Itoa(rand.Intn(100000))}
    return resp
}

func updateDeeplinkUrl(altAccoPropertyList []*response.AltAccoProperty, searchParams *common.SearchEvent){
	var baseUrl string
	baseUrl = getBaseHotelListingDeepLink(searchParams, config.AppConfig.HotelListingDeeplinkURL)

	for _, altAccoProperty := range altAccoPropertyList{
		propertyTypes := strings.Join(altAccoProperty.PropertyTypeList, ",")
		var finalUrl strings.Builder
		finalUrl.WriteString(baseUrl + constants.AND_SEPARATOR + constants.DESKTOP_PROPERTYTYPE_URL_PARAM + constants.PR_SEPARATOR + propertyTypes)
		altAccoProperty.DesktopDeeplink = finalUrl.String()
	}
}

func getBaseHotelListingDeepLink(searchParams *common.SearchEvent, deeplink string) string {
	var baseUrl string
	rsParms := searchParams.RoomStayCandidates

	// first pick default values from pms
	countryCode:= config.PmsCommonConfig.DefaultCountryCode
	cityCode:= config.PmsCommonConfig.DefaultCityCode
	ap:= config.PmsCommonConfig.DefaultAP
	los:= config.PmsCommonConfig.DefaultLOS
	rsc:= config.PmsCommonConfig.DefaultRSC
	currentDate := time.Now()
	checkInDate := currentDate.AddDate(0, 0, ap)
	checkoutDate := checkInDate.AddDate(0, 0, los)
	var checkIn string
	checkIn = checkInDate.Format(constants.DT_FRMT_SEARCH_CNTXT)
	var checkOut string
	checkOut = checkoutDate.Format(constants.DT_FRMT_SEARCH_CNTXT)
	// update default values if received in request
	if searchParams.CountryCode != ""{
		countryCode = searchParams.CountryCode
	}
	if searchParams.CityCode != ""{
		cityCode = searchParams.CityCode
	}
	if searchParams.Checkin != ""{
		checkIn = searchParams.Checkin
	}
	if searchParams.Checkout != ""{
		checkOut = searchParams.Checkout
	}
	if rsParms != nil{
		rsc = getRSQualifier(rsParms)
	}
	checkIn = utils.FormatDateString(checkIn, constants.DT_FRMT_SEARCH_CNTXT, constants.DT_FRMT_DEEPLINK_URL)
	checkOut = utils.FormatDateString(checkOut, constants.DT_FRMT_SEARCH_CNTXT, constants.DT_FRMT_DEEPLINK_URL)

	baseUrl = fmt.Sprintf(deeplink,checkIn,checkOut,cityCode,countryCode,rsc)
	return baseUrl
}

func getRSQualifier(roomStayParams request.RoomStayCandidates) string {
	roomStayPrefix := ""
	for _,roomStayParam := range roomStayParams {
		for _,guestCount := range roomStayParam.GuestCounts {
			adltCnt,_ := strconv.Atoi(guestCount.Count)
			chldCnt := len(guestCount.Ages)
			roomStayPrefix += strconv.Itoa(adltCnt)
			roomStayPrefix += constants.RSQ_SPLITTER
			roomStayPrefix += strconv.Itoa(chldCnt)
			roomStayPrefix += constants.RSQ_SPLITTER
			for _,ageVal := range guestCount.Ages{
				var age int = int(ageVal.(float64))
				if age >=0  && age <= 2 {
					roomStayPrefix +="1"
				}else if age >=3  && age <= 6 {
					roomStayPrefix +="3"
				}else{
					roomStayPrefix +="7"
				}
				roomStayPrefix += constants.RSQ_SPLITTER
			}
		}
	}
	return roomStayPrefix
}

func prepareErrorRsp(errorCode string, errorMsg string) response.GetPropertyTypeRsp{
	var getPropertyRsp response.GetPropertyTypeRsp
	getPropertyRsp.Status = errorCode
	getPropertyRsp.StatusDetails =errorMsg
	return getPropertyRsp
}

