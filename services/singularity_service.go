package services

import (
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/models"
	"Hotels-Scion/models/downstream/singularity"
)

func getSingularityCrossellResponse(requestBO *models.RequestBO) singularity.CrossSellResponse {
	logger.Info("Get response from singularity")

	singularityReq := singularity.CrossSellRequest{}
	singularityReq.DeviceId = requestBO.DeviceId
	singularityReq.BookingDevice = requestBO.BookingDevice
	singularityReq.PEmailId = requestBO.LoginEmailId
	singularityReq.VisitorId = requestBO.VisitorId
	singularityReq.UUID = requestBO.Uuid
	singularityReq.PageContext = requestBO.PageContext
	singularityReq.BookingId = requestBO.BookingId
	singularityReq.ProfileType = requestBO.ProfileType
	singularityReq.CorrelationKey = requestBO.CorrelationKey
	singularityReq.BookingDetails = requestBO.CrossSellBookingDetail
	singularityReq.MmtAuth = requestBO.MmtAuth
	singularityReq.NewApp = requestBO.ExpData.Locus
	singularityReq.NewApp = requestBO.ExpData.MultiCity

	resp := restconnectors.PostCrosellRequest(&singularityReq)
	return resp
}
