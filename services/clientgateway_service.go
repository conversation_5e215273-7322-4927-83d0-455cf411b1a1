package services

import (
	"Hotels-Scion/config"
	restconnectors "Hotels-Scion/connectors"
	"Hotels-Scion/constants"
	"Hotels-Scion/helpers"
	"Hotels-Scion/models/common"
	"Hotels-Scion/models/downstream/clientbackend"
	"Hotels-Scion/models/downstream/clientgateway"
	"Hotels-Scion/models/request"
	"Hotels-Scion/utils"
	"encoding/json"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
)

func getHotelSearchDataForCrossSell(sc *request.SearchEvent, c *gin.Context, pageContext string, expData string, ck string, secureURL string, imageCount int, nearby bool, user *request.User, isCorpRequest bool, crossSellRequest request.CrossSellRequest) (clientgateway.CrossSellSearchHotelsData, *request.CrossSellDrools, *clientbackend.SearchHotelsRequest, int) {
	//get updated trends and filters from drool
	dres, start, data, _, error := getDroolsRequest(sc, c, nearby, isCorpRequest)
	if error {
		return data, &dres, nil, int(time.Since(start).Milliseconds())
	}

	logger.Debugf("getting search hotels data for cross sell stream API, correlationKey %s", ck)
	req := clientbackend.SearchHotelsRequest{}
	if isCorpRequest {
		createRequestForCrossSellCorp2(sc, &req, expData, ck, c, &dres, secureURL, imageCount, nearby, user, crossSellRequest)
	} else {
		createRequestForCrossSell(sc, &req, pageContext, expData, ck, c, &dres, secureURL, imageCount, nearby, user, crossSellRequest)
	}
	e, _ := json.Marshal(req)
	logger.Infof("Request for cross sell stream is: %s", string(e))
	start = time.Now()
	return restconnectors.ClientStruct.PostXSellSearchHotelsRequest(&req, c), &dres, &req, int(time.Since(start).Milliseconds())

}

func getAASufficiency(cityId string, aaSufficientCityList []string, region string) bool {
	if len(region) != 0 && strings.EqualFold(region, "AE") {
		return false
	}
	AASufficiency := false
	for i := 0; i < len(aaSufficientCityList); i++ {
		if cityId == aaSufficientCityList[i] {
			AASufficiency = true
			break
		}
	}
	return AASufficiency
}

func changeCorrelationKeyToOriginal(resp *clientgateway.CrossSellSearchHotelsData, ck string) {
	resp.CorrelationKey = ck
}

func updateCorrelationKeyWithFunnel(req *clientbackend.SearchHotelsRequest, funnelSource string) {
	var crrKey string = req.CorrelationKey
	if strings.EqualFold(funnelSource, constants.HOTEL_FUNNEL) {
		crrKey += "_hotels"
	} else {
		crrKey += "_homestay"
	}
	req.CorrelationKey = crrKey
}

func getHotelSearchDataForCrossSellV2(sc *request.SearchEvent, c *gin.Context, pageContext string, expData string, ck string, secureURL string,
	imageCount int, nearby bool, user *request.User, isCorpRequest bool, crossSellRequest request.CrossSellRequest) (clientgateway.CrossSellSearchHotelsData, clientgateway.CrossSellSearchHotelsData,
	*clientbackend.SearchHotelsRequest, int) {
	start := time.Now()

	logger.Debugf("getting search hotels data for cross sell stream API, correlationKey %s", ck)
	req := clientbackend.SearchHotelsRequest{}
	createRequestForCrossSellV2(sc, &req, pageContext, expData, ck, c, secureURL, imageCount, nearby, user, crossSellRequest)
	req2 := req
	var resp1, resp2 clientgateway.CrossSellSearchHotelsData
	region := c.GetHeader("region")
	req.FunnelSource = constants.HOTEL_FUNNEL
	funnelSource := sc.SearchContext.FunnelSource
	if strings.EqualFold(funnelSource, constants.HOMESTAY_FUNNEL) && !strings.EqualFold(region, "AE") {
		req.FunnelSource = constants.HOMESTAY_TEXT
		resp2 = restconnectors.ClientStruct.PostXSellSearchHotelsRequest(&req, c)
	} else if strings.EqualFold(funnelSource, constants.HOTEL_FUNNEL) {
		cities := config.AppConfig.AASufficientCityList
		var AASufficiency = getAASufficiency(sc.SearchContext.From.Locus.City, cities, region)
		if AASufficiency {
			var chHotels = make(chan clientgateway.CrossSellSearchHotelsData)
			var chHomestays = make(chan clientgateway.CrossSellSearchHotelsData)
			updateCorrelationKeyWithFunnel(&req, constants.HOTEL_FUNNEL)
			go restconnectors.ClientStruct.PostXSellSearchHotelsRequestAsync(&req, c, chHotels)
			req2.FunnelSource = constants.HOMESTAY_TEXT
			updateCorrelationKeyWithFunnel(&req2, constants.HOMESTAY_TEXT)
			go restconnectors.ClientStruct.PostXSellSearchHotelsRequestAsync(&req2, c, chHomestays)
			resp1 = <-chHotels
			changeCorrelationKeyToOriginal(&resp1, ck)
			resp2 = <-chHomestays
			changeCorrelationKeyToOriginal(&resp2, ck)
		} else {
			resp1 = restconnectors.ClientStruct.PostXSellSearchHotelsRequest(&req, c)
		}
	} else {
		// backward compatibility case
		resp1 = restconnectors.ClientStruct.PostXSellSearchHotelsRequest(&req, c)
	}
	e, _ := json.Marshal(req)
	logger.Infof("Request for cross sell stream is: %s", string(e))
	return resp1, resp2, &req, int(time.Since(start).Milliseconds())
}

func getHotelSearchDataForLandingDiscoveryV2(sc *request.SearchEvent, c *gin.Context, pageContext string, expData string, ck string, secureURL string,
	imageCount int, nearby bool, user *request.User, isCorpRequest bool, crossSellRequest request.CrossSellRequest) clientgateway.CrossSellSearchHotelsData {

	logger.Debugf("getting landing card data for landing discovery stream API, correlationKey %s", ck)
	req := clientbackend.SearchHotelsRequest{}
	createRequestForCrossSellV2(sc, &req, pageContext, expData, ck, c, secureURL, imageCount, nearby, user, crossSellRequest)
	var resp1 clientgateway.CrossSellSearchHotelsData
	req.FunnelSource = constants.HOTEL_FUNNEL
	funnelSource := sc.SearchContext.FunnelSource
	if checkIfPremiumHomepage(c) && len(funnelSource) == 0 {
		funnelSource = constants.HOTEL_FUNNEL
	}
	if strings.EqualFold(funnelSource, constants.HOTEL_FUNNEL) {
		resp1 = restconnectors.ClientStruct.PostXSellLandingDiscoveryRequestAsync(&req, c)
	} else {
		restconnectors.ClientStruct.PostXSellLandingDiscoveryRequest(&req, c)
	}
	e, _ := json.Marshal(req)
	logger.Infof("Request for landing card is: %s", string(e))
	return resp1
}

func createRequestForCrossSellV2(sc *request.SearchEvent, req *clientbackend.SearchHotelsRequest, pageContext string, expData string, ck string, c *gin.Context,
	secureURL string, imageCount int, nearBy bool, user *request.User, crossSellRequest request.CrossSellRequest) {
	//set required request fields from headers
	currency := c.GetHeader("currency")
	if len(crossSellRequest.Brand) != 0 {
		req.Brand = crossSellRequest.Brand
	}
	if len(currency) != 0 {
		req.Currency = currency
	}
	deviceID := c.GetHeader("deviceid")
	if len(deviceID) != 0 {
		req.DeviceID = deviceID
	}
	req.DeviceType = "Mobile"
	if crossSellRequest.ImageCategory != nil {
		req.ImageCategory = crossSellRequest.ImageCategory
		req.ImageType = []string{"professional"}
	}

	req.CommonPageContext = pageContext

	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") || strings.EqualFold(deviceType, "IOS") {
			req.BookingDevice = strings.ToUpper(deviceType)
		} else if strings.EqualFold(deviceType, "PWA") {
			req.BookingDevice = "PWA"
		} else {
			req.DeviceType = "Desktop"
			req.BookingDevice = strings.ToUpper(deviceType)
		}
	}

	profileType := c.GetHeader("profiletype")
	if len(profileType) != 0 {
		req.ProfileType = profileType
		if profileType == "BUSINESS" {
			req.CdfContextID = "CORP"
			if len(deviceType) != 0 && strings.EqualFold(deviceType, "Desktop") {
				req.Channel = "B2Bweb"
			} else {
				req.Channel = "MOB"
			}
			req.IDContext = "CORP"
		} else {
			req.CdfContextID = "B2C"
			req.IDContext = "B2C"
			if len(deviceType) != 0 && strings.EqualFold(deviceType, "Desktop") {
				req.Channel = "B2Cweb"
			} else {
				req.Channel = "MOB"
			}
			//sending UUID only in case of personal as for CORP send only mmtAuth
			uuid := c.GetHeader("uuid")
			if len(uuid) != 0 {
				req.UUID = uuid
			}
		}
	}
	ver := c.GetHeader("ver")
	if len(ver) != 0 {
		req.AppVersion = ver
	}
	mmtAuth := c.GetHeader("mmt-auth")
	if len(mmtAuth) != 0 {
		req.LoggedIn = true
	}
	region := c.GetHeader("region")
	if len(region) != 0 {
		req.SiteDomain = region
	}
	req.RequestType = "B2CAgent"
	req.CorrelationKey = ck
	req.ExperimentData = expData
	req.PageContext = "LISTING"
	req.NoOfPersuasions = 1
	req.SecureURL = secureURL
	req.ImageCount = imageCount
	req.Limit = sc.Limit
	req.PersonalizedSearch = sc.SearchContext.PersonalizedSearch
	currentTime := time.Now()
	checkinDate := ""
	checkoutDate := ""
	var checkinTime time.Time

	if len(sc.SearchContext.FromDateStr.Str) != 0 {
		fromTS := utils.DateToEpoch(strings.Split(sc.SearchContext.FromDateStr.Str, " ")[0])
		checkinTime = utils.GetTimeFromEpochAndZone(sc.SearchContext.FromDateStr.Zone, fromTS)
	} else {
		checkinTime = currentTime.AddDate(0, 0, config.AppConfig.DefaultSearchContext.CheckinDateFromCurrent)
	}
	checkinDate = utils.GetTimeInFormattedString(checkinTime)

	var checkoutTime time.Time
	if len(sc.SearchContext.ToDateStr.Str) != 0 {
		toTS := utils.DateToEpoch(strings.Split(sc.SearchContext.ToDateStr.Str, " ")[0])
		checkoutTime = utils.GetTimeFromEpochAndZone(sc.SearchContext.ToDateStr.Zone, toTS)
	} else {
		checkoutTime = currentTime.AddDate(0, 0, config.AppConfig.DefaultSearchContext.CheckoutDateFromCurrent)
	}

	checkoutDate = utils.GetTimeInFormattedString(checkoutTime)

	req.Checkin = checkinDate
	req.Checkout = checkoutDate

	if !nearBy {
		if sc.LandingDiscoveryPage && (strings.EqualFold(sc.CardID, constants.HANDPICKED_PROPERITES) || strings.EqualFold(sc.CardID, constants.RECENTLY_VIEWED_HOTELS) || strings.EqualFold(sc.CardID, constants.GREAT_VALUE_PACKAGES)) {
			req.LocationID = sc.SearchContext.To.Locus.Id
			req.LocationType = sc.SearchContext.To.Locus.Type
			req.CityCode = sc.SearchContext.To.Locus.Id
			req.ImageCategory = []clientbackend.ImageCategory{{Category: "H", Count: 1}}
		} else {
			req.LocationID = sc.SearchContext.To.Locus.City
			req.LocationType = "city"
			req.CityCode = sc.SearchContext.To.Locus.City
			req.CityName = sc.SearchContext.To.Locus.CityName
		}
		req.ResponseFilterFlags = clientbackend.ResponseFilterFlags{}
		if checkIfPremiumHomepage(c) && !strings.EqualFold(sc.SearchContext.FunnelSource, constants.FUNNEL_SOURCE_SHORTSTAYS) {
			if len(req.LocationID) == 0 && len(req.CityCode) == 0 {
				req.LocationType = "storefront"
				req.LocationID = "SFINLUX"
				req.CityCode = "SFINLUX"
				if strings.EqualFold(sc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2) {
					req.ResponseFilterFlags.ThemesCardRequired = true
					if len(sc.SelectedTabId) != 0 {
						req.SelectedTabId = sc.SelectedTabId
					} else {
						req.SelectedTabId = "HERITAGE"
					}

				}
				if strings.EqualFold(sc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD) {
					req.ResponseFilterFlags.ThemesCardRequired = true
					req.Limit = 50
				}
			} else {
				if strings.EqualFold(sc.CardID, constants.MMT_IMMERESIVE_THEMES_CARD_V2) {
					req.LocationType = "storefront"
					req.LocationID = "SFINLUX"
					req.CityCode = "SFINLUX"
					req.ResponseFilterFlags.ThemesCardRequired = true
					if len(sc.SelectedTabId) != 0 {
						req.SelectedTabId = sc.SelectedTabId
					} else {
						req.SelectedTabId = "HERITAGE"
					}

				}
			}
		}
	}

	//req.CityCode = sc.SearchContext.To.Locus.City
	req.CountryCode = sc.SearchContext.To.CountryCode
	// In case recommendedPlaces are coming, we will use it's countryCode and cityCode
	if sc.RecommendedPlaces != nil && len(sc.RecommendedPlaces) > 0 {
		if len(req.CountryCode) == 0 {
			req.CountryCode = sc.RecommendedPlaces[0].CountryCode
		}
		if len(req.LocationID) == 0 {
			req.LocationID = sc.RecommendedPlaces[0].CityCode
		}
	}
	if checkIfPremiumHomepage(c) && len(req.CountryCode) == 0 {
		req.CountryCode = "IN"
	}
	req.NumberOfSoldOuts = 0
	req.NumberOfCoupons = 1

	/* if strings.Contains(config.AppConfig.CityTaxExclusiveCountries, sc.CountryCode) {
		req.ResponseFilterFlags.CityTaxExclusive = true
	} */
	req.ResponseFilterFlags.WalletRequired = true
	req.ResponseFilterFlags.BestCoupon = true
	req.ResponseFilterFlags.PriceInfoReq = true
	req.ResponseFilterFlags.FlyfishSummaryRequired = true
	req.ResponseFilterFlags.CityTaxExclusive = true
	req.ResponseFilterFlags.Locus = true

	if config.AppConfig.CardsForPersuasion != nil && helpers.Contains(config.AppConfig.CardsForPersuasion, sc.CardID) {
		req.ResponseFilterFlags.PersuasionsEngineHit = true
	}

	req.TrafficSource = clientbackend.TrafficSource{}
	req.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	req.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	req.Requester = "SCION"
	req.FirstTimeUser = false
	req.SecureURL = secureURL
	req.ImageCount = imageCount

	//create room stay candidates
	req.RoomStayCandidates = createRoomStayCandidates(sc)
	//adding default pax in case search context was missing from skywalker
	if len(req.RoomStayCandidates) == 0 {
		rsc := clientbackend.RoomStayCandidates{}
		var gcs []clientbackend.GuestCount
		gc := clientbackend.GuestCount{}
		gc.Count = strconv.Itoa(2)
		gcs = append(gcs, gc)
		rsc.GuestCounts = gcs
		req.RoomStayCandidates = append(req.RoomStayCandidates, rsc)
	}
	apfMap := map[common.Filter_Group][]common.Filter{}
	req.AppliedFilterMap = sc.AppliedFilterMap
	mm := clientbackend.MatchMakerRequest{}
	mm.SelectedTags = createSelectedTags(sc)
	mm.LatLng = createPoi(sc)
	if profileType == "BUSINESS" {
		req.UserID = mmtAuth
		req.Domain = "B2C"
		mm.Hotels = createHotelIDs(sc)
	}

	//Changes in crosssell Request for GCC MMT_EXCLUSIVE_OFFERS cards
	helpers.BuildMmtExclusiveCardRequest(sc, req)

	//Changes in crosssell Request for MMT_CROSS_SELL cards
	helpers.BuildCrossSellCardRequest(sc, req)
	if sc.LandingDiscoveryPage || strings.EqualFold(sc.CardID, constants.GREAT_VALUE_PACKAGES_V2) {
		req.AppliedFilterMap = sc.AppliedFilterMap
		req.CardId = sc.CardID
		req.ResponseFilterFlags.PersuasionRequired = sc.PersuasionRequired
		//create hotel ids from enrichments
		req.HotelIDList = recentlyViewedHotelIds(sc)
	}
	if strings.EqualFold(sc.CardID, constants.GREAT_VALUE_PACKAGES_V2) || strings.EqualFold(sc.CardID, constants.GREAT_VALUE_PACKAGES) {
		addPackageRateFilter(req)
	}

	if len(apfMap) != 0 {
		req.AppliedFilterMap = &apfMap
	}
	if !reflect.DeepEqual(mm, clientbackend.MatchMakerRequest{}) {
		req.MatchMakerRequest = &mm
	}

	//create fly fish request for MMT reviews
	ffReq := clientbackend.FlyfishSummaryRequest{}
	otas := []string{"MMT", "TA"}
	tagTypes := []string{"BASE", "WHAT_GUESTS_SAY"}
	ffReq.Filter.Otas = otas
	ffReq.Filter.SubConcept = clientbackend.SubConcept{TagTypes: tagTypes}
	req.FlyfishSummaryRequest = ffReq

	// Set MatchMakerRequest with HotelID from Product ID
	if (sc.SearchContext.Product != request.Product{}) && sc.SearchContext.Product.ID != "" {
		if sc.SearchContext.Meta != nil && sc.SearchContext.Meta.GoDataString != "" {
			var goData request.GoData
			err := json.Unmarshal([]byte(sc.SearchContext.Meta.GoDataString), &goData)
			if err != nil {
				logger.Errorf("Error unmarshalling GoDataString: %v", err)
			} else {
				if goData.LocusData != nil && len(goData.LocusData.Matchmaker) > 0 && goData.LocusData.Matchmaker[0].Id != "" {
					req.MatchMakerRequest = &clientbackend.MatchMakerRequest{
						Hotels: []clientbackend.InputHotel{
							{
								HotelID: goData.LocusData.Matchmaker[0].Id,
							},
						},
					}
				} else {
					logger.Errorf("MMT hotelId is not present in meta node")
				}
			}
		} else {
			logger.Errorf("Meta node cannot be null in case of DHS as it contains MMT hotelId")
		}
	}

	if nearBy {
		req.FunnelSource = "GETAWAY"

		if user != nil && &user.Location != nil && &user.Location.LocationData != nil {
			locationDetail := request.DeviceCurrentLocation{}
			locationDetail = user.Location.LocationData.DeviceCurrentLocation

			if &locationDetail.Lat != nil {
				req.Latitude = locationDetail.Lat
			}
			if &locationDetail.Lng != nil {
				req.Longitude = locationDetail.Lng
			}
		}
	}
	req.RecommendedPlaces = sc.RecommendedPlaces

	req.UserSegments = make([]string, 0)
	if sc.HydraSegments != nil && len(sc.HydraSegments) > 0 {
		for _, areaTrend := range sc.HydraSegments {
			req.UserSegments = append(req.UserSegments, areaTrend)
		}
	}

}

func recentlyViewedHotelIds(sc *request.SearchEvent) []string {
	hotelIDs := make([]string, len(sc.RecentlyViewedHotelIDs))
	var size = len(sc.RecentlyViewedHotelIDs)
	if strings.EqualFold(sc.CardID, constants.RECENTLY_VIEWED_HOTELS) && len(sc.RecentlyViewedHotelIDs) > 0 {
		for i := 0; i < size; i++ {
			hotelIDs[i] = sc.RecentlyViewedHotelIDs[i].Value
		}
		logger.Debugf("Hotels", hotelIDs)
	}
	return hotelIDs
}

func getHotelSearchDataForCrossSellCorp(sc *request.SearchEvent, c *gin.Context, pageContext string, expData string, ck string, secureURL string, imageCount int, nearby bool, user *request.User, isCorpRequest bool, crossSellRequest request.CrossSellRequest) (clientgateway.CrossSellSearchPersonalizedData, *request.CrossSellDrools, *clientbackend.SearchPersonalizedHotelsRequest, int) {
	//get updated trends and filters from drool
	dres, start, _, data, error := getDroolsRequest(sc, c, nearby, isCorpRequest)
	if error {
		return data, &dres, nil, int(time.Since(start).Milliseconds())
	}

	logger.Debugf("getting search hotels data for cross sell stream API, correlationKey %s", ck)
	req := clientbackend.SearchPersonalizedHotelsRequest{}
	createRequestForCrossSellCorp(sc, &req, expData, ck, c, &dres, secureURL, imageCount, nearby, user, crossSellRequest)
	e, _ := json.Marshal(req)
	logger.Infof("Request for cross sell stream corp is: %s", string(e))
	start = time.Now()
	return restconnectors.ClientStruct.PostXSellSearchPersonalizedRequestCorp(&req, c), &dres, &req, int(time.Since(start).Milliseconds())
}

func getDroolsRequest(sc *request.SearchEvent, c *gin.Context, nearby bool, isCorpRequest bool) (request.CrossSellDrools, time.Time, clientgateway.CrossSellSearchHotelsData,
	clientgateway.CrossSellSearchPersonalizedData, bool) {
	dres := request.CrossSellDrools{}
	start := time.Now()
	dreq, err := getCrossSellDroolRequest(sc, c, nearby)
	if err != nil {
		logger.Errorf("Skipping drools hit due to error in making drools request %v", err)
	} else {
		dres = restconnectors.ClientStruct.PostDroolsDataRequest(dreq)
		//if get empty rule id from drools (no match for priority 2 & others), send empty response
		if !reflect.DeepEqual(dres, request.CrossSellDrools{}) && len(dres.RuleID) == 0 && (sc.Priority == 2 || sc.Priority == 3) {
			eCode := "1000001"
			eMsg := "Request with Duplicate Search Context"
			rErr := clientbackend.ResponseError{
				ErrorCode:    eCode,
				ErrorMessage: eMsg,
			}
			var errList []clientbackend.ResponseError
			errList = append(errList, rErr)
			resErrors := clientbackend.ResponseErrors{
				ErrorList: errList,
			}
			if isCorpRequest {
				errRes := clientgateway.CrossSellSearchPersonalizedData{
					ResponseErrors: resErrors,
				}
				return request.CrossSellDrools{}, time.Time{}, clientgateway.CrossSellSearchHotelsData{}, errRes, true
			} else {
				errRes := clientgateway.CrossSellSearchHotelsData{
					ResponseErrors: resErrors,
				}
				return request.CrossSellDrools{}, time.Time{}, errRes, clientgateway.CrossSellSearchPersonalizedData{}, true
			}
		}
	}
	return dres, start, clientgateway.CrossSellSearchHotelsData{}, clientgateway.CrossSellSearchPersonalizedData{}, false
}

func getCrossSellDroolRequest(sc *request.SearchEvent, c *gin.Context, nearby bool) (*request.CrossSellDrools, error) {
	dreq := request.CrossSellDrools{}

	checkinTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.FromDateStr.Zone, sc.SearchContext.FromDateStr.TS)
	checkoutTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.ToDateStr.Zone, sc.SearchContext.ToDateStr.TS)

	los := int(checkoutTime.Sub(checkinTime).Hours() / 24)
	ap := int(checkinTime.Sub(time.Now()).Hours() / 24)
	adc := 0
	cc := 0
	profileType := c.GetHeader("profiletype")
	if len(profileType) != 0 {
		if profileType == "BUSINESS" {
			dreq.IdContext = "CORP"
		} else {
			dreq.IdContext = "B2C"
		}
	}
	region := c.GetHeader("region")
	if len(region) != 0 {
		if region == "AE" {
			dreq.SiteDomain = "AE"
		} else {
			dreq.SiteDomain = "IN"
		}
	}
	if len(sc.SearchContext.Pax) != 0 {
		for _, pax := range sc.SearchContext.Pax {
			if !reflect.DeepEqual(pax.Details.Adult, request.PaxCountDetails{}) {
				adc = adc + pax.Details.Adult.Count
			}
			if !reflect.DeepEqual(pax.Details.Child, request.PaxCountDetails{}) {
				cc = cc + pax.Details.Child.Count
			}
			if !reflect.DeepEqual(pax.Details.Infant, request.PaxCountDetails{}) {
				cc = cc + pax.Details.Infant.Count
			}
		}
	}
	dreq.LOS = los
	if len(sc.HydraSegments) != 0 {
		dreq.HydraSegment = strings.Join(sc.HydraSegments, ",")
	}
	dreq.AdvancedPurchase = ap
	dreq.NoOfChildren = cc
	dreq.AdultCount = adc
	dreq.Destination = sc.SearchContext.To.Locus.City
	if strings.Compare(sc.SearchContext.To.CountryCode, "IN") == 0 {
		dreq.DomIntl = "DOM"
	} else {
		dreq.DomIntl = "INTL"
	}
	if !reflect.DeepEqual(sc.Enrichments, request.Enrichments{}) {
		if !reflect.DeepEqual(sc.Enrichments.Trends, request.RecommendedTrend{}) {
			setTrendsInDroolsRequest(&sc.Enrichments.Trends, &dreq)
		} else {
			setTrendsInDroolsRequest(&sc.Enrichments.UserPreference, &dreq)
		}
	}
	if sc.Priority == 0 {
		dreq.Priority = 1
	} else {
		dreq.Priority = sc.Priority
	}
	if nearby {
		dreq.NearBy = true
	} else {
		dreq.NearBy = false
	}
	return &dreq, nil
}

func setTrendsInDroolsRequest(trend *request.RecommendedTrend, dreq *request.CrossSellDrools) {
	if len(trend.Area) != 0 {
		dreq.Area = trend.Area
	}
	if len(trend.Price) != 0 {
		dreq.Price = trend.Price
	}
	if len(trend.AccommodationType) != 0 {
		dreq.AccomodationType = trend.AccommodationType
	}
	if len(trend.StarRating) != 0 {
		dreq.StarRating = trend.StarRating
	}
}

func createRequestForCrossSell(sc *request.SearchEvent, req *clientbackend.SearchHotelsRequest, pageContext string, expData string, ck string, c *gin.Context, dres *request.CrossSellDrools, secureURL string, imageCount int, nearBy bool, user *request.User, crossSellRequest request.CrossSellRequest) {
	//set required request fields from headers

	currency := c.GetHeader("currency")
	if len(currency) != 0 {
		req.Currency = currency
	}
	deviceID := c.GetHeader("deviceid")
	if len(deviceID) != 0 {
		req.DeviceID = deviceID
	}
	req.DeviceType = "Mobile"
	//if we receive ImageCategory from commons we set it in the request
	if crossSellRequest.ImageCategory != nil {
		req.ImageCategory = crossSellRequest.ImageCategory
		req.ImageType = []string{"professional"}
	}
	req.CommonPageContext = pageContext

	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") || strings.EqualFold(deviceType, "IOS") {
			req.BookingDevice = strings.ToUpper(deviceType)
		} else if strings.EqualFold(deviceType, "PWA") {
			req.BookingDevice = "MSITE"
		} else {
			req.DeviceType = "Desktop"
			req.BookingDevice = strings.ToUpper(deviceType)
		}
	}

	profileType := c.GetHeader("profiletype")
	if len(profileType) != 0 {
		req.ProfileType = profileType
		if profileType == "BUSINESS" {
			req.CdfContextID = "CORP"
			if len(deviceType) != 0 && strings.EqualFold(deviceType, "Desktop") {
				req.Channel = "B2Bweb"
			} else {
				req.Channel = "MOB"
			}
			req.IDContext = "CORP"
		} else {
			req.CdfContextID = "B2C"
			req.IDContext = "B2C"
			if len(deviceType) != 0 && strings.EqualFold(deviceType, "Desktop") {
				req.Channel = "B2Cweb"
			} else {
				req.Channel = "MOB"
			}
			//sending UUID only in case of personal as for CORP send only mmtAuth
			uuid := c.GetHeader("uuid")
			if len(uuid) != 0 {
				req.UUID = uuid
			}
		}
	}
	ver := c.GetHeader("ver")
	if len(ver) != 0 {
		req.AppVersion = ver
	}
	mmtAuth := c.GetHeader("mmt-auth")
	if len(mmtAuth) != 0 {
		req.LoggedIn = true
	}
	region := c.GetHeader("region")
	if len(region) != 0 {
		req.SiteDomain = region
	}
	req.RequestType = "B2CAgent"
	req.CorrelationKey = ck
	req.ExperimentData = expData
	req.PageContext = "LISTING"
	req.NoOfPersuasions = 1
	req.SecureURL = secureURL
	req.ImageCount = imageCount
	req.Limit = sc.Limit
	req.PersonalizedSearch = sc.SearchContext.PersonalizedSearch
	checkinTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.FromDateStr.Zone, sc.SearchContext.FromDateStr.TS)
	checkinDate := utils.GetTimeInFormattedString(checkinTime)

	checkoutTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.ToDateStr.Zone, sc.SearchContext.ToDateStr.TS)
	checkoutDate := utils.GetTimeInFormattedString(checkoutTime)
	req.Checkin = checkinDate
	req.Checkout = checkoutDate

	if !nearBy {
		req.LocationID = sc.SearchContext.To.Locus.City
		req.LocationType = "city"

	}

	req.CityCode = sc.SearchContext.To.Locus.City
	req.CountryCode = sc.SearchContext.To.CountryCode
	req.NumberOfSoldOuts = 0
	req.NumberOfCoupons = 1

	req.ResponseFilterFlags = clientbackend.ResponseFilterFlags{}
	/* if strings.Contains(config.AppConfig.CityTaxExclusiveCountries, sc.CountryCode) {
		req.ResponseFilterFlags.CityTaxExclusive = true
	} */
	req.ResponseFilterFlags.WalletRequired = true
	req.ResponseFilterFlags.BestCoupon = true
	req.ResponseFilterFlags.PriceInfoReq = true
	req.ResponseFilterFlags.FlyfishSummaryRequired = true
	req.ResponseFilterFlags.CityTaxExclusive = true
	req.ResponseFilterFlags.Locus = true

	req.TrafficSource = clientbackend.TrafficSource{}
	req.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	req.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	req.Requester = "SCION"
	req.FirstTimeUser = false
	req.SecureURL = secureURL
	req.ImageCount = imageCount

	//create room stay candidates
	req.RoomStayCandidates = createRoomStayCandidates(sc)
	apfMap := map[common.Filter_Group][]common.Filter{}
	mm := clientbackend.MatchMakerRequest{}
	//create hotel ids from enrichments
	mm.Hotels = createHotelIDs(sc)
	mm.SelectedTags = createSelectedTags(sc)
	mm.LatLng = createPoi(sc)
	//create filters and match maker request from drool response
	if !reflect.DeepEqual(*dres, request.CrossSellDrools{}) {
		getFiltersFromDroolsResponse(&apfMap, &mm, dres)
	} else {
		getFiltersFromCrossSellRequest(&apfMap, &mm, sc)
	}

	if len(apfMap) != 0 {
		req.AppliedFilterMap = &apfMap
	}
	if !reflect.DeepEqual(mm, clientbackend.MatchMakerRequest{}) {
		req.MatchMakerRequest = &mm
	}

	req.AppliedFilterMap = sc.AppliedFilterMap

	//create fly fish request for MMT reviews
	ffReq := clientbackend.FlyfishSummaryRequest{}
	otas := []string{"MMT"}
	tagTypes := []string{"BASE", "WHAT_GUESTS_SAY"}
	ffReq.Filter.Otas = otas
	ffReq.Filter.SubConcept = clientbackend.SubConcept{TagTypes: tagTypes}
	req.FlyfishSummaryRequest = ffReq

	if nearBy {
		req.FunnelSource = "GETAWAY"

		if user != nil && &user.Location != nil && &user.Location.LocationData != nil {
			locationDetail := request.DeviceCurrentLocation{}
			locationDetail = user.Location.LocationData.DeviceCurrentLocation

			if &locationDetail.Lat != nil {
				req.Latitude = locationDetail.Lat
			}
			if &locationDetail.Lng != nil {
				req.Longitude = locationDetail.Lng
			}
		}
	}
	if strings.EqualFold(sc.SearchContext.FunnelSource, constants.FUNNEL_SOURCE_SHORTSTAYS) {
		req.FunnelSource = constants.FUNNEL_SOURCE_SHORTSTAYS
		if checkIfPremiumHomepage(c) {
			addLuxuryHotelCategoryFilter(req)
		}
	}

}

func addLuxuryHotelCategoryFilter(req *clientbackend.SearchHotelsRequest) {
	filterVal := constants.LUXURY_FILTERS_VALUE
	appliedFilterMap := map[common.Filter_Group][]common.Filter{
		common.HOTEL_CATEGORY: {
			{common.HOTEL_CATEGORY, &filterVal, nil, false},
		},
	}
	req.AppliedFilterMap = &appliedFilterMap
}
func addPackageRateFilter(req *clientbackend.SearchHotelsRequest) {
	filterVal := constants.MMT_PACKAGE_RATE_FILTER

	// Check if AppliedFilterMap is nil
	if req.AppliedFilterMap == nil {
		// If nil, create a new map and assign it to AppliedFilterMap
		req.AppliedFilterMap = &map[common.Filter_Group][]common.Filter{
			common.PACKAGE_RATE: {
				{common.PACKAGE_RATE, &filterVal, nil, false},
			},
		}
	} else {
		// If not nil, append to the existing map
		(*req.AppliedFilterMap)[common.PACKAGE_RATE] = []common.Filter{
			{common.PACKAGE_RATE, &filterVal, nil, false},
		}
	}
}

func createRequestForCrossSellCorp(sc *request.SearchEvent, req *clientbackend.SearchPersonalizedHotelsRequest, expData string, ck string, c *gin.Context, dres *request.CrossSellDrools, secureURL string, imageCount int, nearBy bool, user *request.User, crossSellRequest request.CrossSellRequest) {
	//set required request fields from headers

	currency := c.GetHeader("currency")
	if len(currency) != 0 {
		req.Currency = currency
	}
	deviceID := c.GetHeader("deviceid")
	if len(deviceID) != 0 {
		req.DeviceID = deviceID
	}
	req.DeviceType = "Mobile"
	if crossSellRequest.ImageCategory != nil {
		req.ImageCategory = crossSellRequest.ImageCategory
		req.ImageType = []string{"professional"}
	}

	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") || strings.EqualFold(deviceType, "IOS") {
			req.BookingDevice = strings.ToUpper(deviceType)
		} else if strings.EqualFold(deviceType, "PWA") {
			req.BookingDevice = "MSITE"
		} else {
			req.DeviceType = "Desktop"
			req.BookingDevice = strings.ToUpper(deviceType)
		}
	}

	profileType := c.GetHeader("profiletype")
	if len(profileType) != 0 {
		req.CdfContextID = "CORP"
		if len(deviceType) != 0 && strings.EqualFold(deviceType, "Desktop") {
			req.Channel = "B2Bweb"
		} else {
			req.Channel = "MOB"
		}
		req.IDContext = "CORP"
	}
	ver := c.GetHeader("ver")
	if len(ver) != 0 {
		req.AppVersion = ver
	}
	mmtAuth := c.GetHeader("mmt-auth")
	req.UserID = mmtAuth
	if len(mmtAuth) != 0 {
		req.LoggedIn = true
	}

	req.Domain = "B2C"
	req.FunnelSource = "HOTELS"
	req.RequestType = "B2CAgent"
	req.CorrelationKey = ck
	req.ExperimentData = expData
	req.PageContext = "LISTING"
	req.Requester = "SCION"
	req.Limit = sc.Limit
	checkinTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.FromDateStr.Zone, sc.SearchContext.FromDateStr.TS)
	checkinDate := utils.GetTimeInFormattedString(checkinTime)

	checkoutTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.ToDateStr.Zone, sc.SearchContext.ToDateStr.TS)
	checkoutDate := utils.GetTimeInFormattedString(checkoutTime)
	req.Checkin = checkinDate
	req.Checkout = checkoutDate

	if !nearBy {
		req.LocationID = sc.SearchContext.To.Locus.City
		req.LocationType = "city"

	}

	req.CityCode = sc.SearchContext.To.Locus.City
	req.CountryCode = sc.SearchContext.To.CountryCode
	req.NumberOfCoupons = 1

	req.ResponseFilterFlags = clientbackend.ResponseFilterFlags{}
	/* if strings.Contains(config.AppConfig.CityTaxExclusiveCountries, sc.CountryCode) {
		req.ResponseFilterFlags.CityTaxExclusive = true
	} */
	req.ResponseFilterFlags.WalletRequired = true
	req.ResponseFilterFlags.BestCoupon = true
	req.ResponseFilterFlags.PriceInfoReq = true
	req.ResponseFilterFlags.FlyfishSummaryRequired = true
	req.ResponseFilterFlags.CityTaxExclusive = true
	req.ResponseFilterFlags.Locus = true

	req.TrafficSource = clientbackend.TrafficSource{}
	req.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	req.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	req.FirstTimeUser = false

	//create room stay candidates
	req.RoomStayCandidates = createRoomStayCandidates(sc)
	apfMap := map[common.Filter_Group][]common.Filter{}
	mm := clientbackend.MatchMakerRequest{}
	//create hotel ids from enrichments
	mm.Hotels = createHotelIDs(sc)
	mm.SelectedTags = createSelectedTags(sc)
	mm.LatLng = createPoi(sc)
	//create filters and match maker request from drool response
	if !reflect.DeepEqual(*dres, request.CrossSellDrools{}) {
		getFiltersFromDroolsResponse(&apfMap, &mm, dres)
	} else {
		getFiltersFromCrossSellRequest(&apfMap, &mm, sc)
	}

	if len(apfMap) != 0 {
		req.AppliedFilterMap = &apfMap
	}
	if !reflect.DeepEqual(mm, clientbackend.MatchMakerRequest{}) {
		req.MatchMakerRequest = &mm
	}

	//create fly fish request for MMT reviews
	ffReq := clientbackend.FlyfishSummaryRequest{}
	otas := []string{"MMT"}
	tagTypes := []string{"BASE", "WHAT_GUESTS_SAY"}
	ffReq.Filter.Otas = otas
	ffReq.Filter.SubConcept = clientbackend.SubConcept{TagTypes: tagTypes}
	req.FlyfishSummaryRequest = ffReq

	if nearBy {
		req.FunnelSource = "GETAWAY"
	}

}

func createRequestForCrossSellCorp2(sc *request.SearchEvent, req *clientbackend.SearchHotelsRequest, expData string, ck string, c *gin.Context, dres *request.CrossSellDrools, secureURL string, imageCount int, nearBy bool, user *request.User, crossSellRequest request.CrossSellRequest) {
	//set required request fields from headers

	currency := c.GetHeader("currency")
	if len(currency) != 0 {
		req.Currency = currency
	}
	deviceID := c.GetHeader("deviceid")
	if len(deviceID) != 0 {
		req.DeviceID = deviceID
	}
	req.DeviceType = "Mobile"
	if crossSellRequest.ImageCategory != nil {
		req.ImageCategory = crossSellRequest.ImageCategory
		req.ImageType = []string{"professional"}
	}

	deviceType := c.GetHeader("channel")
	if len(deviceType) != 0 {
		if strings.EqualFold(deviceType, "ANDROID") || strings.EqualFold(deviceType, "IOS") {
			req.BookingDevice = strings.ToUpper(deviceType)
		} else if strings.EqualFold(deviceType, "PWA") {
			req.BookingDevice = "MSITE"
		} else {
			req.DeviceType = "Desktop"
			req.BookingDevice = strings.ToUpper(deviceType)
		}
	}

	profileType := c.GetHeader("profiletype")
	if len(profileType) != 0 {
		req.CdfContextID = "CORP"
		if len(deviceType) != 0 && strings.EqualFold(deviceType, "Desktop") {
			req.Channel = "B2Bweb"
		} else {
			req.Channel = "MOB"
		}
		req.IDContext = "CORP"
	}
	ver := c.GetHeader("ver")
	if len(ver) != 0 {
		req.AppVersion = ver
	}
	mmtAuth := c.GetHeader("mmt-auth")
	req.UserID = mmtAuth
	if len(mmtAuth) != 0 {
		req.LoggedIn = true
	}

	req.Domain = "B2C"
	req.FunnelSource = "HOTELS"
	req.RequestType = "B2CAgent"
	req.CorrelationKey = ck
	req.ExperimentData = expData
	req.PageContext = "LISTING"
	req.Requester = "SCION"
	req.Limit = sc.Limit
	checkinTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.FromDateStr.Zone, sc.SearchContext.FromDateStr.TS)
	checkinDate := utils.GetTimeInFormattedString(checkinTime)

	checkoutTime := utils.GetTimeFromEpochAndZone(sc.SearchContext.ToDateStr.Zone, sc.SearchContext.ToDateStr.TS)
	checkoutDate := utils.GetTimeInFormattedString(checkoutTime)
	req.Checkin = checkinDate
	req.Checkout = checkoutDate

	if !nearBy {
		req.LocationID = sc.SearchContext.To.Locus.City
		req.LocationType = "city"

	}

	req.CityCode = sc.SearchContext.To.Locus.City
	req.CountryCode = sc.SearchContext.To.CountryCode
	req.NumberOfCoupons = 1

	req.ResponseFilterFlags = clientbackend.ResponseFilterFlags{}
	/* if strings.Contains(config.AppConfig.CityTaxExclusiveCountries, sc.CountryCode) {
		req.ResponseFilterFlags.CityTaxExclusive = true
	} */
	req.ResponseFilterFlags.WalletRequired = true
	req.ResponseFilterFlags.BestCoupon = true
	req.ResponseFilterFlags.PriceInfoReq = true
	req.ResponseFilterFlags.FlyfishSummaryRequired = true
	req.ResponseFilterFlags.CityTaxExclusive = true
	req.ResponseFilterFlags.Locus = true

	req.TrafficSource = clientbackend.TrafficSource{}
	req.TrafficSource.Source = config.AppConfig.TrafficSource.Source
	req.TrafficSource.Type = config.AppConfig.TrafficSource.Type
	req.FirstTimeUser = false

	//create room stay candidates
	req.RoomStayCandidates = createRoomStayCandidates(sc)
	apfMap := map[common.Filter_Group][]common.Filter{}
	mm := clientbackend.MatchMakerRequest{}
	//create hotel ids from enrichments
	mm.Hotels = createHotelIDs(sc)
	mm.SelectedTags = createSelectedTags(sc)
	mm.LatLng = createPoi(sc)
	//create filters and match maker request from drool response
	if !reflect.DeepEqual(*dres, request.CrossSellDrools{}) {
		getFiltersFromDroolsResponse(&apfMap, &mm, dres)
	} else {
		getFiltersFromCrossSellRequest(&apfMap, &mm, sc)
	}

	if len(apfMap) != 0 {
		req.AppliedFilterMap = &apfMap
	}
	if !reflect.DeepEqual(mm, clientbackend.MatchMakerRequest{}) {
		req.MatchMakerRequest = &mm
	}

	//create fly fish request for MMT reviews
	ffReq := clientbackend.FlyfishSummaryRequest{}
	otas := []string{"MMT"}
	tagTypes := []string{"BASE", "WHAT_GUESTS_SAY"}
	ffReq.Filter.Otas = otas
	ffReq.Filter.SubConcept = clientbackend.SubConcept{TagTypes: tagTypes}
	req.FlyfishSummaryRequest = ffReq

	if nearBy {
		req.FunnelSource = "GETAWAY"
	}

}

func getFiltersFromDroolsResponse(apfMap *map[common.Filter_Group][]common.Filter, mm *clientbackend.MatchMakerRequest, dres *request.CrossSellDrools) {
	if len(dres.UpdatedPrice) != 0 {
		filters := []common.Filter{}
		fg := common.HOTEL_PRICE
		for _, price := range dres.UpdatedPrice {
			filter := getRangeFilterFromTrendValue(price.MinValue, price.MaxValue, fg)
			filters = append(filters, filter)
		}
		(*apfMap)[fg] = filters
	}

	if len(dres.UpdatedArea) != 0 {
		tags := []clientbackend.Tag{}
		for _, area := range dres.UpdatedArea {
			res := strings.Split(area.Value, "::")
			tag := clientbackend.Tag{
				AreaID:   res[0],
				AreaName: res[1],
			}
			tags = append(tags, tag)
		}
		mm.SelectedTags = tags
	}

	if len(dres.UpdatedAccomodationType) != 0 {
		filters := []common.Filter{}
		fg := common.PROPERTY_TYPE
		for _, prop := range dres.UpdatedAccomodationType {
			filter := getFilterFromTrendValue(prop.Value, fg)
			filters = append(filters, filter)
		}
		(*apfMap)[fg] = filters
	}

	if len(dres.UpdatedStarRating) != 0 {
		filters := []common.Filter{}
		fg := common.STAR_RATING
		for _, st := range dres.UpdatedStarRating {
			filter := getFilterFromTrendValue(st.Value, fg)
			filters = append(filters, filter)
		}
		(*apfMap)[fg] = filters
	}

	getFiltersFromAdditonalFilters(apfMap, dres)

}

func getFiltersFromCrossSellRequest(apfMap *map[common.Filter_Group][]common.Filter, mm *clientbackend.MatchMakerRequest, sc *request.SearchEvent) {
	if !reflect.DeepEqual(sc.Enrichments, request.Enrichments{}) {
		if !reflect.DeepEqual(sc.Enrichments.Trends, request.RecommendedTrend{}) {
			getFilterFromTrends(apfMap, &sc.Enrichments.Trends)
			getMatchMakerFromTrends(mm, &sc.Enrichments.Trends)
		} else {
			getFilterFromTrends(apfMap, &sc.Enrichments.UserPreference)
			getMatchMakerFromTrends(mm, &sc.Enrichments.UserPreference)
		}
	}

}

func getMatchMakerFromTrends(mm *clientbackend.MatchMakerRequest, trends *request.RecommendedTrend) {
	if len(trends.Area) != 0 {
		tags := []clientbackend.Tag{}
		for _, area := range trends.Area {
			res := strings.Split(area.Value, "::")
			tag := clientbackend.Tag{
				AreaID:   res[0],
				AreaName: res[1],
			}
			tags = append(tags, tag)
		}
		mm.SelectedTags = tags
	}
}

func getFilterFromTrends(apfMap *map[common.Filter_Group][]common.Filter, trends *request.RecommendedTrend) {
	if len(trends.Price) != 0 {
		filters := []common.Filter{}
		fg := common.HOTEL_PRICE
		for _, price := range trends.Price {
			filter := getRangeFilterFromTrendValue(price.MinValue, price.MaxValue, fg)
			filters = append(filters, filter)
		}
		(*apfMap)[fg] = filters
	}

	if len(trends.AccommodationType) != 0 {
		filters := []common.Filter{}
		fg := common.PROPERTY_TYPE
		for _, prop := range trends.AccommodationType {
			filter := getFilterFromTrendValue(prop.Value, fg)
			filters = append(filters, filter)
		}
		(*apfMap)[fg] = filters
	}

	if len(trends.StarRating) != 0 {
		filters := []common.Filter{}
		fg := common.STAR_RATING
		for _, st := range trends.StarRating {
			filter := getFilterFromTrendValue(st.Value, fg)
			filters = append(filters, filter)
		}
		(*apfMap)[fg] = filters
	}
}

func getFiltersFromAdditonalFilters(apfMap *map[common.Filter_Group][]common.Filter, dres *request.CrossSellDrools) {
	addFilters := dres.AdditionalFilters
	filterArr := strings.Split(addFilters, "^")
	for _, str := range filterArr {
		fgfv := strings.Split(str, "|")
		var tfg common.Filter_Group
		tfg = common.Filter_Group(fgfv[0])
		err1 := tfg.IsValid()
		if err1 == nil {
			filters := []common.Filter{}
			fvs := strings.Split(fgfv[1], ",")
			for _, tfv := range fvs {
				rfv := strings.Split(tfv, "-")
				if len(rfv) > 1 {
					minV, err := strconv.Atoi(rfv[0])
					if err != nil {
						//creating value filter as Mysafety has "-" in its value
						filter := getFilterFromTrendValue(tfv, tfg)
						filters = append(filters, filter)
						continue
					}
					maxV, err := strconv.Atoi(rfv[1])
					if err != nil {
						logger.Errorf("Error while creating range filters from drools additional filters. Error: %v", err)
						continue
					}
					filter := getRangeFilterFromTrendValue(minV, maxV, tfg)
					filters = append(filters, filter)
				} else if len(rfv) == 1 {
					filter := getFilterFromTrendValue(rfv[0], tfg)
					filters = append(filters, filter)
				}
			}
			(*apfMap)[tfg] = filters
		}
	}

}

func getFilterFromTrendValue(fv string, fg common.Filter_Group) common.Filter {
	filter := common.Filter{}
	filter.FilterGroup = fg
	filter.IsRangeFilter = false
	filter.FilterValue = &fv
	return filter
}

func getRangeFilterFromTrendValue(minV int, maxV int, fg common.Filter_Group) common.Filter {
	filter := common.Filter{}
	filter.FilterGroup = fg
	filter.IsRangeFilter = true
	filterRange := common.FilterRange{}
	filterRange.MinValue = minV
	filterRange.MaxValue = maxV
	filter.FilterRange = &filterRange
	return filter
}

func createHotelIDs(sc *request.SearchEvent) []clientbackend.InputHotel {
	hotelIDs := []clientbackend.InputHotel{}
	enrich := sc.Enrichments
	if !reflect.DeepEqual(enrich, request.Enrichments{}) {
		if !reflect.DeepEqual(enrich.Trends, request.RecommendedTrend{}) && len(enrich.Trends.HotelIDs) > 0 {
			for _, hotelID := range enrich.Trends.HotelIDs {
				inputHotel := clientbackend.InputHotel{
					HotelID: hotelID.Value,
				}
				hotelIDs = append(hotelIDs, inputHotel)
			}
		} else if !reflect.DeepEqual(enrich.UserPreference, request.RecommendedTrend{}) && len(enrich.UserPreference.HotelIDs) > 0 {
			for _, hotelID := range enrich.UserPreference.HotelIDs {
				inputHotel := clientbackend.InputHotel{
					HotelID: hotelID.Value,
				}
				hotelIDs = append(hotelIDs, inputHotel)
			}
		}
	}
	return hotelIDs
}

func createSelectedTags(se *request.SearchEvent) []clientbackend.Tag {
	var tags []clientbackend.Tag
	tag := clientbackend.Tag{AreaID: se.SearchContext.From.Locus.AreaId, AreaName: se.SearchContext.From.Locus.AreaName}
	if (clientbackend.Tag{}) == tag {
		return nil
	}
	tags = append(tags, tag)
	return tags
}

func createPoi(se *request.SearchEvent) []clientbackend.LatLng {
	var latLngs []clientbackend.LatLng
	latLng := clientbackend.LatLng{PoiId: se.SearchContext.From.Locus.PoiId, Name: se.SearchContext.From.Locus.PoiName}
	if (clientbackend.LatLng{}) == latLng {
		return nil
	}
	latLngs = append(latLngs, latLng)
	return latLngs
}

func createRoomStayCandidates(sc *request.SearchEvent) []clientbackend.RoomStayCandidates {
	rsc := []clientbackend.RoomStayCandidates{}
	if !reflect.DeepEqual(sc.SearchContext, request.SearchContext{}) && len(sc.SearchContext.Pax) > 0 {
		for _, p := range sc.SearchContext.Pax {
			rs := clientbackend.RoomStayCandidates{}
			gcs := []clientbackend.GuestCount{}
			ages := []string{}
			gc := clientbackend.GuestCount{}
			if reflect.DeepEqual(p.Details, request.PaxDetails{}) {
				logger.Errorf("Empty pax details. Setting default to 2 adult counts")
				gc.Count = strconv.Itoa(2)
				gc.Ages = ages
			} else {
				gc.Count = strconv.Itoa(p.Details.Adult.Count)
				if !reflect.DeepEqual(p.Details.Child, request.PaxCountDetails{}) {
					gc.AgeQualifyingCode = "10"
					for _, age := range p.Details.Child.Ages {
						ages = append(ages, strconv.Itoa(age))
					}
				}
				if !reflect.DeepEqual(p.Details.Infant, request.PaxCountDetails{}) {
					gc.AgeQualifyingCode = "10"
					for _, age := range p.Details.Infant.Ages {
						ages = append(ages, strconv.Itoa(age))
					}
				}
				gc.Ages = ages
			}
			gcs = append(gcs, gc)
			rs.GuestCounts = gcs
			rsc = append(rsc, rs)
		}
	}
	return rsc
}

func getHotelMobLanding(mobLandingRequest clientbackend.MobLandingRequest, c *gin.Context) clientgateway.HotelsMobLandingResponse {

	var resp1 clientgateway.HotelsMobLandingResponse
	resp1 = restconnectors.ClientStruct.PostMobLandingRequest(&mobLandingRequest, c)
	e, _ := json.Marshal(mobLandingRequest) // Need to handle request error
	logger.Infof("Request for landing card is: %s", string(e))
	jsonStr, _ := json.Marshal(resp1) // Need to handle response
	logger.Infof("Response for landing card is: %s", string(jsonStr))
	return resp1
}

func getMMTHotelMobLanding(mmtRequest clientbackend.MobLandingRequest, c *gin.Context) clientgateway.HotelsMobLandingResponse {
	logger.Infof("Calling MMT ClientGateway for mob landing")
	var resp clientgateway.HotelsMobLandingResponse
	resp = restconnectors.ClientStruct.PostMMTMobLandingRequest(&mmtRequest, c)

	// Log request and response for debugging
	reqJSON, _ := json.Marshal(mmtRequest)
	logger.Infof("MMT Request for landing card is: %s", string(reqJSON))
	respJSON, _ := json.Marshal(resp)
	logger.Infof("MMT Response for landing card is: %s", string(respJSON))

	return resp
}
