# Cross-Sell Stream API

## Overview
The Cross-Sell Stream API provides real-time streaming of hotel cross-selling recommendations. It uses Server-Sent Events (SSE) with HTTP streaming to deliver personalized hotel recommendations as they become available.

**Endpoint:** `POST /hotels-scion/api/cross-sell-stream`

## Architecture

### Type
- **Streaming API** with chunked transfer encoding
- **Pattern:** Server-Sent Events (SSE) with HTTP streaming
- **Concurrency:** Goroutine-based parallel processing for each search event
- **Response:** Real-time streaming of cross-sell recommendations

### Key Features
- Real-time data streaming
- Parallel processing of multiple search events
- Client disconnect detection
- Chunked transfer encoding for efficient streaming

## Downstream Services

| Service | URL | Purpose |
|---------|-----|---------|
| **ClientGateway Search Hotels** | `http://hotels-clientgateway.ecs.mmt/clientbackend/entity/api/searchHotels` | Hotel search functionality |
| **ClientBackend Cross-sell Static Details** | `http://mmtclient-bkend.mmt.mmt/clientbackend/entity/api/xsell/staticDetail` | Static hotel details for cross-selling |
| **Singularity Recommendation Engine** | `http://hotels-singularity.ecs.mmt/Hotels-Singularity/v1/getHotelFromPriorBooking` | AI-powered recommendations |
| **Couchbase Cache** | `cb-42-70.mmt.mmt,cb-42-71.mmt.mmt,cb-42-72.mmt.mmt,cb-42-73.mmt.mmt` | Caching layer for performance |

## Request Format

### Headers
```
Content-Type: application/json
Transfer-Encoding: chunked
profileType: BUSINESS (optional)
```

### Request Body
```json
{
  "brand": "string",
  "context": {
    "contextId": "string",
    "experimentData": "string",
    "pageContext": "string",
    "scope": "string",
    "funnelSource": "string"
  },
  "secureUrl": "string",
  "imageCount": 0,
  "searchEvent": [
    {
      "cardId": "string",
      "templateId": "string",
      "componentId": "string",
      "limit": 0,
      "isFilter": false,
      "priority": 0,
      "hydraSegIds": ["string"],
      "meta": {},
      "sc": {
        "lob": "string",
        "lobCategory": "string",
        "fromDateTime": {
          "str": "2024-01-15",
          "ts": 1705276800,
          "zone": "Asia/Kolkata"
        },
        "toDateTime": {
          "str": "2024-01-17",
          "ts": 1705449600,
          "zone": "Asia/Kolkata"
        },
        "pax": [
          {
            "count": 2,
            "details": {
              "adult": {
                "ages": [25, 30],
                "count": 2
              },
              "child": {
                "ages": [],
                "count": 0
              },
              "infant": {
                "ages": [],
                "count": 0
              }
            }
          }
        ],
        "from": {
          "lobCity": "string",
          "locus": {
            "city": "string",
            "areaId": "string",
            "areaName": "string",
            "poiId": "string",
            "poiName": "string",
            "type": "city",
            "id": "CTBOM",
            "cityName": "Mumbai"
          },
          "cityName": "Mumbai",
          "countryName": "India",
          "countryCode": "IN",
          "longitude": 72.8777,
          "latitude": 19.076
        },
        "to": {
          "lobCity": "string",
          "locus": {
            "city": "string",
            "areaId": "string",
            "areaName": "string",
            "poiId": "string",
            "poiName": "string",
            "type": "city",
            "id": "CTDEL",
            "cityName": "Delhi"
          },
          "cityName": "Delhi",
          "countryName": "India",
          "countryCode": "IN",
          "longitude": 77.209,
          "latitude": 28.6139
        },
        "timestamp": 1705276800,
        "rooms": 1,
        "funnelSource": "string",
        "personalizedSearch": false,
        "product": {
          "id": "string",
          "name": "string"
        }
      },
      "selectedTabId": "string",
      "appliedFilterMap": {},
      "funnelActivity": {},
      "enrichments": {
        "trends": {
          "areas": [],
          "price_bucket": [],
          "ap_bucket": [],
          "los_bucket": [],
          "pax": [],
          "pd_id": [],
          "star_rating": [],
          "accommodation_type": [],
          "cities": []
        },
        "userPreferences": {}
      },
      "tags": {
        "edge_type": "string",
        "source": "string"
      },
      "filterList": [],
      "filter": false,
      "persuasionRequired": false,
      "recentlyViewedHotels": [],
      "isLandingDiscoveryPage": false,
      "recommendedPlaces": []
    }
  ],
  "nearBy": false,
  "offerRequired": false,
  "user": {
    "location": {
      "locationData": {
        "currentLocation": {
          "latitude": 19.076,
          "longitude": 72.8777
        }
      }
    }
  },
  "imageCategories": [],
  "correlationKey": "uuid-string",
  "metaInfo": {
    "userName": "string",
    "bookingId": "string"
  }
}
```

## Response Format

### Headers
```
Content-Type: application/json
Transfer-Encoding: chunked
```

### Response Body
```json
{
  "cardDetails": [
    {
      "heading": "Hotels For You",
      "hotels": [
        {
          "id": "hotel-123",
          "name": "Taj Mahal Palace",
          "images": [
            "https://imgak.mmtcdn.com/hotels/123/image1.jpg",
            "https://imgak.mmtcdn.com/hotels/123/image2.jpg"
          ],
          "starRating": 5,
          "userRating": 4.5,
          "currencyCode": "INR",
          "address": {
            "line1": "Apollo Bunder",
            "line2": "Mumbai, Maharashtra"
          },
          "displayFare": {
            "displayPrice": 15000.0,
            "nonDiscountedPrice": 18000.0
          },
          "appDeeplink": "mmyt://htl/listing/?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17",
          "desktopDeeplink": "https://www.makemytrip.com/hotels/hotel-details?hotelId=hotel-123&checkin=2024-01-15&checkout=2024-01-17"
        }
      ],
      "searchContext": {
        "from": "Mumbai",
        "to": "Delhi",
        "checkIn": "2024-01-15",
        "checkOut": "2024-01-17",
        "guests": 2
      },
      "viewMore": {
        "text": "View More Hotels",
        "deeplink": "https://www.makemytrip.com/hotels/hotel-listing"
      },
      "moreDetails": "Based on your search preferences",
      "offerDetails": {
        "description": "Get 15% off on your first booking"
      }
    }
  ]
}
```

## Data Flow

```
1. Client Request
   ↓
2. Handler Processing
   ↓
3. For each SearchEvent:
   ├── Create Channel
   ├── Launch Goroutine
   └── Process in Parallel
   ↓
4. Service Layer
   ├── ClientGateway (Hotel Search)
   ├── Static Data Retrieval
   └── Price Data Processing
   ↓
5. Response Processing
   ├── JSON Marshal
   └── Stream to Client
   ↓
6. Flush Response
   ↓
7. Next SearchEvent (if any)
```

## Error Handling

### Common Error Scenarios
- **Client Disconnect:** Detected via `http.CloseNotifier`
- **Invalid Request:** Returns 400 Bad Request
- **Downstream Service Failure:** Returns partial data with error indicators
- **Timeout:** Configurable timeout for downstream calls

### Error Response Format
```json
{
  "error": "string",
  "status": "ERROR",
  "message": "Error description"
}
```

## Performance Considerations

### Optimization Features
- **Parallel Processing:** Multiple search events processed concurrently
- **Caching:** Couchbase integration for frequently accessed data
- **Streaming:** Real-time data delivery without waiting for all results
- **Connection Management:** Efficient handling of client disconnections

### Timeouts
- **Downstream Service Timeout:** 30 seconds (configurable)
- **Client Connection Timeout:** Based on client configuration
- **Streaming Flush Interval:** Immediate after each response

## Usage Examples

### cURL Example
```bash
curl -X POST \
  http://localhost:8080/hotels-scion/api/cross-sell-stream \
  -H 'Content-Type: application/json' \
  -H 'Transfer-Encoding: chunked' \
  -d '{
    "brand": "MMT",
    "context": {
      "pageContext": "HOTEL_LANDING",
      "experimentData": "exp_123"
    },
    "searchEvent": [
      {
        "cardId": "HOTEL_CROSS_SELL",
        "sc": {
          "from": {"locus": {"id": "CTBOM"}},
          "to": {"locus": {"id": "CTDEL"}},
          "fromDateTime": {"str": "2024-01-15"},
          "toDateTime": {"str": "2024-01-17"},
          "pax": [{"count": 2, "details": {"adult": {"count": 2, "ages": [25, 30]}}}]
        }
      }
    ],
    "correlationKey": "req-123"
  }'
```

### JavaScript Example
```javascript
const response = await fetch('/hotels-scion/api/cross-sell-stream', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    brand: 'MMT',
    context: {
      pageContext: 'HOTEL_LANDING'
    },
    searchEvent: [{
      cardId: 'HOTEL_CROSS_SELL',
      sc: {
        from: { locus: { id: 'CTBOM' } },
        to: { locus: { id: 'CTDEL' } },
        fromDateTime: { str: '2024-01-15' },
        toDateTime: { str: '2024-01-17' },
        pax: [{ count: 2, details: { adult: { count: 2, ages: [25, 30] } } }]
      }
    }],
    correlationKey: 'req-123'
  })
});

// Handle streaming response
const reader = response.body.getReader();
while (true) {
  const { done, value } = await reader.read();
  if (done) break;
  
  const chunk = new TextDecoder().decode(value);
  const data = JSON.parse(chunk);
  console.log('Received:', data);
}
```

## Monitoring and Logging

### Key Metrics
- **Response Time:** Per search event processing time
- **Throughput:** Number of requests per second
- **Error Rate:** Percentage of failed requests
- **Client Disconnect Rate:** Number of client disconnections

### Logging
- **Request Logging:** Full request payload with correlation key
- **Response Logging:** Response status and timing
- **Error Logging:** Detailed error information with stack traces
- **Performance Logging:** Downstream service call timings

## Rate Limiting

### Limits
- **Requests per minute:** 1000 (configurable)
- **Concurrent connections:** 100 (configurable)
- **Downstream service calls:** Based on service-specific limits

### Throttling
- **429 Too Many Requests:** When rate limit exceeded
- **Retry-After header:** Indicates when to retry
- **Graceful degradation:** Returns cached data when possible

## Security

### Authentication
- **Header-based:** `profileType` header for user type
- **Correlation tracking:** UUID-based request correlation
- **Request validation:** Comprehensive input validation

### Data Protection
- **HTTPS only:** All communications encrypted
- **PII handling:** Sensitive data masked in logs
- **Input sanitization:** All inputs validated and sanitized 